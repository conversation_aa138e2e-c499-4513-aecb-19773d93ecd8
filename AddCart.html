<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/AddCart.css">

    <style>
        /* Variant Styles */
        .variant-size {
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .variant-color {
            background-color: #f9f9f9;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
    </style>

    <!-- Slick Slider CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>

    <title>Giỏ hàng | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Cart Header -->
    <div class="cart-header">
        <div class="container">
            <h1>Giỏ hàng của bạn</h1>
            <p>Xem lại sản phẩm và tiến hành thanh toán</p>

            <div class="cart-breadcrumb">
                <a href="index.html">Trang chủ</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <span class="current">Giỏ hàng</span>
            </div>
        </div>
    </div>

    <!-- Empty Cart Message -->
    <div id="cartempty" class="container">
        <h1>Giỏ hàng của bạn đang trống</h1>
        <p>Bạn chưa thêm sản phẩm nào vào giỏ hàng</p>
        <div class="cart-buttons">
            <a href="index.html" class="btn btn-primary">Tiếp tục mua sắm</a>
            <button id="clearCartBtn" class="btn btn-secondary">Xóa giỏ hàng</button>
            <button id="createSampleCartBtn" class="btn btn-success">Tạo giỏ hàng mẫu</button>
        </div>
    </div>

    <!-- Main Cart Content -->
    <div id="maincart">
        <!-- Cart Items -->
        <div id="cartdiv">
            <table class="cart-table">
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th>Giá</th>
                        <th>Số lượng</th>
                        <th>Tổng tiền</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody id="cart-items">
                    <!-- Cart items will be added dynamically with JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Cart Summary -->
        <div id="promocode">
            <!-- Promo Code Section -->
            <div id="promodiv">
                <h4>Mã giảm giá</h4>
                <div class="promo-form">
                    <input type="text" placeholder="Nhập mã giảm giá" id="code">
                    <button id="apply">Áp dụng</button>
                </div>
                     <div class="promo-info">
                    <p><i class="fas fa-tag"></i> Mã giảm giá có sẵn: SALE10, SALE20, SALE30</p>
                </div>
            </div>

                <!-- Đổi Xu lấy Giảm Giá -->
    <div class="promo-form" style="margin-top: 15px;">
        <label for="coinInput" style="flex: 1; font-size: 0.9rem; padding-top: 0.4rem;">Đổi xu (100 xu = 1.000đ):</label>
        <input type="number" id="coinInput" placeholder="Nhập số xu" min="0" step="100" style="flex: 1;">
        <button id="applyCoins">Đổi xu</button>
    </div>

            <!-- Order Summary Section -->
            <div id="orderdiv">
                <h4>Tổng đơn hàng</h4>

                <div class="summary-row">
                    <span>Tạm tính:</span>
                    <span id="Price"></span>
                </div>

                <div class="summary-row">
                    <span>Phí vận chuyển:</span>
                    <span>Miễn phí</span>
                </div>

                <div class="summary-row">
                    <span>Giảm giá:</span>
                    <span id="discount">0đ</span>
                </div>

                <div class="summary-row total">
                    <span>Tổng cộng:</span>
                    <span id="Price2"></span>
                </div>
            </div>

            <div class="checkout-buttons">
                <button id="checkoutbtn"><i class="fas fa-lock"></i> Tiến hành thanh toán</button>
                <button id="clearCartBtn2" class="btn-clear-cart"><i class="fas fa-trash-alt"></i> Xóa giỏ hàng</button>
            </div>

            <div class="secure-payment">
                <p><i class="fas fa-shield-alt"></i> Thanh toán an toàn & bảo mật</p>
                <p><i class="fas fa-truck"></i> Giao hàng miễn phí cho đơn hàng từ 500.000đ</p>
                <p><i class="fas fa-undo"></i> Đổi trả trong vòng 30 ngày</p>
            </div>
        </div>
    </div>
    <!-- Recommended Products -->
    <div id="Recommend">
        <h2>Có thể bạn cũng thích</h2>
        <div id="rcmndData">
            <!-- Product 1 -->
            <div class="recommend-product">
                <div class="recommend-img">
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7rbm3-llsybjgyrayz2f@resize_w450_nl.webp" alt="Áo sơ mi nữ">
                </div>
                <div class="recommend-info">
                    <h3 class="recommend-name">Áo thun PEWARNA IKAT tay ngắn cổ tròn dáng rộng thiết kế mới thời trang mùa hè theo phong cách Mỹ cho nữ</h3>
                    <div class="recommend-price">190.000đ</div>
                    <div class="recommend-colors">
                        <div class="color-option" style="background-color: black;"></div>
                        <div class="color-option" style="background-color: white;"></div>
                        <div class="color-option" style="background-color: #ebeae8;"></div>
                    </div>
                </div>
            </div>

            <!-- Product 2 -->
            <div class="recommend-product">
                <div class="recommend-img">
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m6p58lhyhg4o7b@resize_w450_nl.webp" alt="Quần Jean Nam">
                </div>
                <div class="recommend-info">
                    <h3 class="recommend-name">Áo 2 dây màu trơn có mút đệm ngực dây Tăm Co Giãn Dễ Phối Đồ sườn thời trang mùa hè</h3>
                    <div class="recommend-price">80.000đ</div>
                    <div class="recommend-colors">
                        <div class="color-option" style="background-color: #080808;"></div>
                        <div class="color-option" style="background-color: #ffffff;"></div>
                    </div>
                </div>
            </div>

            <!-- Product 3 -->
            <div class="recommend-product">
                <div class="recommend-img">
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7rfh1-m9h235ups4up25@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/sg-11134201-7rfh1-m9h235ups4up25@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/sg-11134201-7rfh1-m9h235ups4up25@resize_w450_nl.webp" alt="Áo sơ mi thời trang">
                </div>
                <div class="recommend-info">
                    <h3 class="recommend-name">Váy ren thời trang Hàn Quốc rèm ren tạp dề ren rèm hông nhỏ nữ nơ nhiều lớp váy xòe mùa xuân hè</h3>
                    <div class="recommend-price">380.000đ</div>
                    <div class="recommend-colors">
                        <div class="color-option" style="background-color: white;"></div>
                        <div class="color-option" style="background-color: #000000;"></div>
                    </div>
                </div>
            </div>

            <!-- Product 4 -->
            <div class="recommend-product">
                <div class="recommend-img">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lw2cdkyr3uu35c@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lw2cdkyr3uu35c@resize_w450_nl.webp" alt="Quần dài nam nữ">
                </div>
                <div class="recommend-info">
                    <h3 class="recommend-name">BARE_(Có size) Áo kiểu 2 dây phối ren nơ gợi cảm, Áo Kiểu Nữ Phối Ren Có Mút Ngựcchất thun gân tăm dày dặn</h3>
                    <div class="recommend-price">120.000đ</div>
                    <div class="recommend-colors">
                        <div class="color-option" style="background-color: #ffffff;"></div>
                        <div class="color-option" style="background-color: #a0a39d;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/user-display.js"></script>
    <script src="./script/auth-check.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Khởi tạo các hiệu ứng sau khi tải trang
        document.addEventListener('DOMContentLoaded', function() {
            // Kiểm tra đăng nhập
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user || !user.isLoggedIn) {
                // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
                localStorage.setItem('redirectAfterLogin', window.location.href);

                // Chuyển hướng đến trang đăng nhập
                window.location.href = './pages/login.html';
                return;
            }

            // Thêm hiệu ứng cho các phần tử
            const productItems = document.querySelectorAll('.recommend-product');
            productItems.forEach((item, index) => {
                item.setAttribute('data-aos', 'fade-up');
                item.setAttribute('data-aos-delay', (index * 100).toString());
            });

            // Cập nhật số lượng giỏ hàng
            let cart = JSON.parse(localStorage.getItem("cart")) || [];
            let countBag = document.querySelector(".cart-count")
            if (countBag) {
                countBag.textContent = cart.length;
            }

            // Hiển thị giỏ hàng trống nếu không có sản phẩm
            console.log("Cart contents:", cart);
            console.log("Cart length:", cart.length);
            console.log("Cart JSON:", JSON.stringify(cart, null, 2));

            // Hiển thị thông báo debug
            showNotification(`Đang tải ${cart.length} sản phẩm từ giỏ hàng...`, 'info');

            // Kiểm tra từng sản phẩm trong giỏ hàng
            cart.forEach((item, index) => {
                console.log(`Item ${index}:`, item);
                console.log(`- Name: ${item.name}`);
                console.log(`- Price: ${item.price}`);
                console.log(`- Image: ${item.image || item.image1}`);
            });

            // Kiểm tra cấu trúc giỏ hàng
            let hasInvalidItems = false;
            cart.forEach(item => {
                if (!item.name || !item.price) {
                    hasInvalidItems = true;
                    console.error("Invalid item in cart:", item);
                }
            });

            // Nếu có sản phẩm không hợp lệ, xóa giỏ hàng và tải lại trang
            if (hasInvalidItems) {
                console.warn("Invalid items found in cart. Clearing cart...");
                localStorage.removeItem("cart");
                cart = [];
            }

            if (cart.length === 0) {
                document.getElementById("cartempty").style.display = "block";
                document.getElementById("maincart").style.display = "none";
            } else {
                console.log("Displaying", cart.length, "items in cart");
                document.getElementById("cartempty").style.display = "none";
                document.getElementById("maincart").style.display = "flex";

                // Hiển thị sản phẩm trong giỏ hàng
                const tbody = document.getElementById("cart-items");
                // Xóa tất cả các hàng hiện có
                tbody.innerHTML = '';

                let totalAmount = 0;

                cart.forEach((item, index) => {
                    const row = document.createElement("tr");
                    row.dataset.id = item.id || index; // Sử dụng ID duy nhất nếu có

                    // Cột sản phẩm
                    const productCell = document.createElement("td");
                    const productContent = document.createElement("div");
                    productContent.className = "cart-product";

                    const productImg = document.createElement("div");
                    productImg.className = "cart-product-img";
                    const img = document.createElement("img");
                    // Sử dụng image hoặc image1 hoặc hình mặc định
                    let imageSrc = item.image || item.image1 || "./img womens/women 15.webp";

                    // Kiểm tra xem đường dẫn hình ảnh có hợp lệ không
                    if (!imageSrc.startsWith('./') && !imageSrc.startsWith('http')) {
                        // Thêm './' vào đầu đường dẫn nếu cần
                        imageSrc = './' + imageSrc;
                    }

                    img.src = imageSrc;
                    console.log("Product image:", imageSrc);
                    productImg.appendChild(img);

                    const productInfo = document.createElement("div");
                    productInfo.className = "cart-product-info";

                    // Tên sản phẩm
                    const productName = document.createElement("div");
                    productName.className = "cart-product-name";
                    productName.textContent = item.name;
                    productInfo.appendChild(productName);

                    // Thông tin size và màu sắc
                    if (item.size || item.color) {
                        const productVariant = document.createElement("div");
                        productVariant.className = "cart-product-variant";
                        productVariant.style.fontSize = "12px";
                        productVariant.style.color = "#666";
                        productVariant.style.marginTop = "5px";

                        let variantText = "";
                        if (item.size) {
                            variantText += `<span class="variant-size">Size: ${item.size}</span>`;
                        }
                        if (item.color) {
                            variantText += variantText ? ` | <span class="variant-color">Màu: ${item.color}</span>` : `<span class="variant-color">Màu: ${item.color}</span>`;
                        }

                        productVariant.innerHTML = variantText;
                        productInfo.appendChild(productVariant);
                    }

                    productContent.appendChild(productImg);
                    productContent.appendChild(productInfo);
                    productCell.appendChild(productContent);

                    // Cột giá
                    const priceCell = document.createElement("td");
                    priceCell.className = "cart-price";
                    priceCell.textContent = item.price;

                    // Cột số lượng
                    const qtyCell = document.createElement("td");
                    const qtyContent = document.createElement("div");
                    qtyContent.className = "cart-quantity";

                    const minusBtn = document.createElement("button");
                    minusBtn.className = "cart-quantity-btn";
                    minusBtn.innerHTML = '<i class="fas fa-minus"></i>';
                    minusBtn.dataset.index = index;
                    minusBtn.dataset.id = item.id || index; // Sử dụng ID duy nhất
                    minusBtn.addEventListener('click', decreaseQuantity);

                    const qtyInput = document.createElement("input");
                    qtyInput.type = "text";
                    qtyInput.className = "cart-quantity-input";
                    qtyInput.value = item.quantity || 1;
                    qtyInput.readOnly = true;

                    const plusBtn = document.createElement("button");
                    plusBtn.className = "cart-quantity-btn";
                    plusBtn.innerHTML = '<i class="fas fa-plus"></i>';
                    plusBtn.dataset.index = index;
                    plusBtn.dataset.id = item.id || index; // Sử dụng ID duy nhất
                    plusBtn.addEventListener('click', increaseQuantity);

                    qtyContent.appendChild(minusBtn);
                    qtyContent.appendChild(qtyInput);
                    qtyContent.appendChild(plusBtn);
                    qtyCell.appendChild(qtyContent);

                    // Cột tổng tiền
                    const subtotalCell = document.createElement("td");
                    subtotalCell.className = "cart-price";
                    const quantity = item.quantity || 1;

                    // Xử lý giá tiền - chuyển đổi từ chuỗi sang số
                    let price = 0;
                    if (item.price) {
                        // Kiểm tra xem giá có phải là số không
                        if (typeof item.price === 'number') {
                            price = item.price;
                        } else {
                            // Loại bỏ tất cả các ký tự không phải số
                            const priceStr = item.price.toString().replace(/[^\d]/g, '');
                            price = priceStr ? parseFloat(priceStr) : 0;
                        }
                        console.log(`Converting price "${item.price}" to number: ${price}`);
                    }

                    const subtotal = price * quantity;
                    subtotalCell.textContent = subtotal.toLocaleString() + 'đ';

                    // Lưu giá trị subtotal vào thuộc tính data để dễ dàng tính tổng
                    subtotalCell.dataset.subtotal = subtotal;

                    // Cột xóa
                    const removeCell = document.createElement("td");
                    const removeBtn = document.createElement("div");
                    removeBtn.className = "cart-remove";
                    removeBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
                    removeBtn.dataset.index = index;
                    removeBtn.dataset.id = item.id || index; // Sử dụng ID duy nhất
                    removeBtn.addEventListener('click', removeItem);
                    removeCell.appendChild(removeBtn);

                    // Thêm các cột vào hàng
                    row.appendChild(productCell);
                    row.appendChild(priceCell);
                    row.appendChild(qtyCell);
                    row.appendChild(subtotalCell);
                    row.appendChild(removeCell);

                    // Thêm hàng vào bảng
                    tbody.appendChild(row);

                    // Tính tổng tiền
                    totalAmount += subtotal;

                    // Lưu thông tin sản phẩm đã cập nhật vào localStorage
                    cart[index].subtotal = subtotal;
                });

                // Lưu tổng tiền vào localStorage để sử dụng ở trang thanh toán
                localStorage.setItem("cart_total", totalAmount.toString());
                localStorage.setItem("cart_final", totalAmount.toString());
                console.log("Đã lưu tổng tiền vào localStorage:", {
                    cart_total: totalAmount,
                    cart_final: totalAmount
                });

                // Hiển thị tổng tiền
                document.getElementById("Price").textContent = totalAmount.toLocaleString() + "đ";
                document.getElementById("Price2").textContent = totalAmount.toLocaleString() + "đ";

                // Cập nhật lại giỏ hàng trong localStorage
                localStorage.setItem("cart", JSON.stringify(cart));

                // Xử lý mã giảm giá
                document.getElementById("apply").addEventListener("click", applyPromoCode);

                // Kiểm tra nếu đã có mã giảm giá trước đó
                const savedPromoCode = localStorage.getItem("promo_code");
                const savedDiscountPercent = localStorage.getItem("discount_percent");

                if (savedPromoCode && savedDiscountPercent) {
// Tự động áp dụng xu nếu người dùng đã dùng trước đó
const savedCoins = localStorage.getItem("coin_discount_used");
if (savedCoins) {
    const discountFromCoins = (parseInt(savedCoins) / 100) * 1000;
    const totalAmount = parseFloat(localStorage.getItem("cart_total")) || 0;
    const finalDiscount = Math.min(discountFromCoins, totalAmount);
    const finalAmount = totalAmount - finalDiscount;

    document.getElementById("discount").textContent = "-" + finalDiscount.toLocaleString() + "đ";
    document.getElementById("Price2").textContent = finalAmount.toLocaleString() + "đ";

    localStorage.setItem("cart_discount", finalDiscount);
    localStorage.setItem("cart_final", finalAmount);
}
                    document.getElementById("code").value = savedPromoCode;
                    const discountPercent = parseInt(savedDiscountPercent);
                    const discount = totalAmount * (discountPercent / 100);
                    const finalAmount = totalAmount - discount;

                    // Hiển thị giảm giá và tổng tiền mới
                    document.getElementById("discount").textContent = "-" + discount.toLocaleString() + "đ";
                    document.getElementById("Price2").textContent = finalAmount.toLocaleString() + "đ";

                    // Lưu giá trị giảm giá và tổng tiền cuối cùng
                    localStorage.setItem("cart_discount", discount);
                    localStorage.setItem("cart_final", finalAmount);
                }
            }

            // Thêm sự kiện click cho các sản phẩm đề xuất
            document.querySelectorAll('.recommend-product').forEach(product => {
                product.addEventListener('click', function() {
                    window.location.href = 'productdetail.html';
                });
            });

            // Thêm sự kiện click cho nút thanh toán
            document.getElementById('checkoutbtn').addEventListener('click', function() {
                // Kiểm tra xem giỏ hàng có trống không
                const cart = JSON.parse(localStorage.getItem("cart")) || [];
                if (cart.length === 0) {
                    showNotification("Giỏ hàng của bạn đang trống", "error");
                    return;
                }

                // Chuyển đến trang thanh toán
                window.location.href = "checkout.html";
            });

            // Hàm xóa giỏ hàng
            function clearCart() {
                if (confirm('Bạn có chắc chắn muốn xóa toàn bộ giỏ hàng?')) {
                    localStorage.removeItem("cart");
                    localStorage.removeItem("cart_total");
                    localStorage.removeItem("cart_final");
                    localStorage.removeItem("cart_discount");
                    localStorage.removeItem("promo_code");
                    localStorage.removeItem("discount_percent");

                    showNotification("Đã xóa toàn bộ giỏ hàng", "success");

                    // Tải lại trang sau 1 giây
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            }

            // Thêm sự kiện click cho các nút xóa giỏ hàng
            document.getElementById('clearCartBtn').addEventListener('click', clearCart);

            // Thêm sự kiện click cho nút xóa giỏ hàng trong phần thanh toán
            const clearCartBtn2 = document.getElementById('clearCartBtn2');
            if (clearCartBtn2) {
                clearCartBtn2.addEventListener('click', clearCart);
            }

            // Thêm sự kiện click cho nút tạo giỏ hàng mẫu
            document.getElementById('createSampleCartBtn').addEventListener('click', function() {
                // Tạo giỏ hàng mẫu
                const sampleCart = [
                    {
                        id: 'sample-1',
                        name: 'Quần SHORT ĐÙI KAKI NỮ dáng ngắn cạp thấp tôn dáng cho các nàng đùi to mặc siêu đẹp, mặc đi biển ,chơi, du lịch..',
                        price: '58000đ',
                        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lu94nm7jrbbl93@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lu94nm7jrbbl93@resize_w450_nl.webp',
                        image1: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lsdx8ccr51ac22.webp',
                        quantity: 1
                    },
                    {
                        id: 'sample-2',
                        name: 'Marbit Nữ Đồ Lót Lụa Áo Váy Ngủ Váy Ngủ Gợi Cảm Đồ Ngủ Váy Ngủ Mới',
                        price: '40000đ',
                        image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m7c3vq3osy7i45@resize_w450_nl.webp',
                        image1: 'https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m7by7mumm8ki5a@resize_w450_nl.webp',
                        quantity: 2
                    },
                    {
                        id: 'sample-3',
                        name: 'Áo thun trễ lệch vai vải ren mỏng dáng yếm y2k vạt tua rua màu trắng tôn dáng Âu Mỹ ngọt ngào Retro',
                        price: '125000đ',
                        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m9lupjeu83uq1e@resize_w450_nl.webp',
                        image1: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m9lupjf47p8ice@resize_w450_nl.webp',
                        quantity: 1
                    }
                ];

                // Lưu giỏ hàng mẫu vào localStorage
                localStorage.setItem('cart', JSON.stringify(sampleCart));

                // Hiển thị thông báo
                showNotification('Đã tạo giỏ hàng mẫu với 3 sản phẩm', 'success');

                // Tải lại trang sau 1 giây
                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        });

        // Hàm tăng số lượng sản phẩm
        function increaseQuantity() {
            const index = parseInt(this.dataset.index);
            const itemId = this.dataset.id;
            let cart = JSON.parse(localStorage.getItem("cart")) || [];

            // Tìm sản phẩm theo ID nếu có, nếu không thì dùng index
            const itemToUpdate = itemId
                ? cart.find(item => item.id === itemId)
                : cart[index];

            if (itemToUpdate) {
                itemToUpdate.quantity = (itemToUpdate.quantity || 1) + 1;
                localStorage.setItem("cart", JSON.stringify(cart));
                updateCart();
            }
        }

        // Hàm giảm số lượng sản phẩm
        function decreaseQuantity() {
            const index = parseInt(this.dataset.index);
            const itemId = this.dataset.id;
            let cart = JSON.parse(localStorage.getItem("cart")) || [];

            // Tìm sản phẩm theo ID nếu có, nếu không thì dùng index
            const itemToUpdate = itemId
                ? cart.find(item => item.id === itemId)
                : cart[index];

            if (itemToUpdate && (itemToUpdate.quantity || 1) > 1) {
                itemToUpdate.quantity = (itemToUpdate.quantity || 1) - 1;
                localStorage.setItem("cart", JSON.stringify(cart));
                updateCart();
            }
        }

        // Hàm xóa sản phẩm
        function removeItem() {
            const index = parseInt(this.dataset.index);
            let cart = JSON.parse(localStorage.getItem("cart")) || [];

            console.log("Removing item at index:", index);
            console.log("Current cart:", cart);

            if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
                // Xóa theo index
                if (index >= 0 && index < cart.length) {
                    cart.splice(index, 1);
                    console.log("Item removed. New cart:", cart);

                    // Lưu giỏ hàng mới vào localStorage
                    localStorage.setItem("cart", JSON.stringify(cart));

                    // Hiển thị thông báo thành công
                    showNotification("Đã xóa sản phẩm khỏi giỏ hàng", "success");

                    // Cập nhật giao diện
                    updateCart();

                    // Nếu giỏ hàng trống, hiển thị thông báo
                    if (cart.length === 0) {
                        document.getElementById("cartempty").style.display = "block";
                        document.getElementById("maincart").style.display = "none";
                    }
                } else {
                    console.error("Invalid index:", index);
                    showNotification("Không thể xóa sản phẩm. Vui lòng thử lại.", "error");
                }
            }
        }

        // Hàm chuyển đến trang thanh toán
        function proceedToCheckout() {
            // Kiểm tra xem giỏ hàng có trống không
            const cart = JSON.parse(localStorage.getItem("cart")) || [];
            if (cart.length === 0) {
                showNotification("Giỏ hàng của bạn đang trống", "error");
                return;
            }

            // Chuyển đến trang thanh toán
            window.location.href = "checkout.html";
        }

        // Hàm áp dụng mã giảm giá
        function applyPromoCode() {


// Hàm áp dụng đổi xu
document.getElementById("applyCoins").addEventListener("click", function () {
    const coinInput = document.getElementById("coinInput");
    let coins = parseInt(coinInput.value);

    if (isNaN(coins) || coins < 100) {
        showNotification("Vui lòng nhập số xu hợp lệ (tối thiểu 100 xu)", "warning");
        return;
    }

    // Làm tròn xuống bội số 100
    coins = Math.floor(coins / 100) * 100;

    // Đổi xu thành số tiền giảm giá
    const discountFromCoins = (coins / 100) * 1000;

    let totalAmount = parseFloat(localStorage.getItem("cart_total")) || 0;

    // Không vượt quá tổng tiền
    const finalDiscount = Math.min(discountFromCoins, totalAmount);
    const finalAmount = totalAmount - finalDiscount;

    document.getElementById("discount").textContent = "-" + finalDiscount.toLocaleString() + "đ";
    document.getElementById("Price2").textContent = finalAmount.toLocaleString() + "đ";

    // Lưu vào localStorage (đảm bảo lưu dưới dạng chuỗi)
    localStorage.setItem("cart_discount", finalDiscount.toString());
    localStorage.setItem("cart_final", finalAmount.toString());
    localStorage.setItem("coin_discount_used", coins.toString());

    console.log("applyCoins: Đã lưu vào localStorage:", {
        cart_discount: finalDiscount,
        cart_final: finalAmount,
        coin_discount_used: coins
    });

    showNotification(`Đã dùng ${coins} xu để giảm ${finalDiscount.toLocaleString()}đ`, "success");
});



















































            const promoCode = document.getElementById("code").value.trim();

            if (promoCode) {
                // Giả lập mã giảm giá
                let discountPercent = 0;

                if (promoCode === "SALE10") {
                    discountPercent = 10;
                } else if (promoCode === "SALE20") {
                    discountPercent = 20;
                } else if (promoCode === "SALE30") {
                    discountPercent = 30;
                }

                if (discountPercent > 0) {
                    // Lưu mã giảm giá và phần trăm giảm giá vào localStorage
                    localStorage.setItem("promo_code", promoCode);
                    localStorage.setItem("discount_percent", discountPercent);

                    // Hiển thị thông báo thành công
                    showNotification(`Áp dụng mã giảm giá thành công: Giảm ${discountPercent}%`, 'success');

                    // Tính lại tổng tiền
                    let totalAmount = parseFloat(document.getElementById("Price").textContent.replace(/[^\d]/g, ''));
                    const discount = totalAmount * (discountPercent / 100);
                    const finalAmount = totalAmount - discount;

                    // Lưu giá trị giảm giá và tổng tiền cuối cùng (đảm bảo lưu dưới dạng chuỗi)
                    localStorage.setItem("cart_discount", discount.toString());
                    localStorage.setItem("cart_final", finalAmount.toString());

                    console.log("applyPromoCode: Đã lưu vào localStorage:", {
                        cart_discount: discount,
                        cart_final: finalAmount
                    });

                    // Hiển thị giảm giá và tổng tiền mới
                    document.getElementById("discount").textContent = "-" + discount.toLocaleString() + "đ";
                    document.getElementById("Price2").textContent = finalAmount.toLocaleString() + "đ";
                } else {
                    showNotification("Mã giảm giá không hợp lệ hoặc đã hết hạn", 'error');
                }
            } else {
                showNotification("Vui lòng nhập mã giảm giá", 'warning');
            }
        }

        // Hàm hiển thị thông báo
        function showNotification(message, type = 'info') {
            // Tạo phần tử thông báo
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                               type === 'error' ? '#F44336' :
                                               type === 'warning' ? '#FF9800' : '#2196F3';
            notification.style.color = '#fff';
            notification.style.padding = '15px 20px';
            notification.style.borderRadius = '4px';
            notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '250px';
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(50px)';
            notification.style.transition = 'opacity 0.3s, transform 0.3s';

            // Icon dựa trên loại thông báo
            let icon = '';
            switch (type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
            }

            // Nội dung thông báo
            notification.innerHTML = `${icon} ${message}`;

            // Thêm vào body
            document.body.appendChild(notification);

            // Hiển thị thông báo với animation
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Xóa thông báo sau 3 giây
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(50px)';

                // Xóa khỏi DOM sau khi animation hoàn tất
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Hàm cập nhật giỏ hàng
        function updateCart() {
            // Lưu giỏ hàng vào localStorage
            let cart = JSON.parse(localStorage.getItem("cart")) || [];
            localStorage.setItem("cart", JSON.stringify(cart));

            // Cập nhật số lượng giỏ hàng
            let countBag = document.querySelector(".cart-count");
            if (countBag) {
                countBag.textContent = cart.length;
            }

            // Tính lại tổng tiền
            let totalAmount = 0;
            cart.forEach(item => {
                const quantity = item.quantity || 1;
                let price = 0;
                if (item.price) {
                    // Kiểm tra xem giá có phải là số không
                    if (typeof item.price === 'number') {
                        price = item.price;
                    } else {
                        // Loại bỏ tất cả các ký tự không phải số
                        const priceStr = item.price.toString().replace(/[^\d]/g, '');
                        price = priceStr ? parseFloat(priceStr) : 0;
                    }
                }
                const subtotal = price * quantity;
                totalAmount += subtotal;

                // Cập nhật subtotal cho item
                item.subtotal = subtotal;
            });

            // Lưu tổng tiền vào localStorage (đảm bảo lưu dưới dạng chuỗi)
            localStorage.setItem("cart_total", totalAmount.toString());
            console.log("updateCart: Đã lưu cart_total =", totalAmount);





// >>> THÊM XỬ LÝ GIẢM GIÁ TỪ ĐỔI XU <<<

const autoDiscountRate = parseFloat(localStorage.getItem("discountRate"));

if (!isNaN(autoDiscountRate) && autoDiscountRate > 0 && autoDiscountRate <= 1) {
    const discount = totalAmount * autoDiscountRate;
    const finalAmount = totalAmount - discount;

    document.getElementById("discount").textContent = "-" + discount.toLocaleString() + "đ";
    document.getElementById("Price2").textContent = finalAmount.toLocaleString() + "đ";

    localStorage.setItem("cart_discount", discount);
    localStorage.setItem("cart_final", finalAmount);

    // Hiển thị dòng \"Đổi xu\" trong ô mã giảm giá nếu có
    const codeInput = document.getElementById("code");
    if (codeInput) {
        codeInput.value = "Đổi xu";
    }

    // Xóa discountRate sau khi dùng
    localStorage.removeItem("discountRate");
} else {
    // Nếu không có mã giảm giá đổi xu thì giữ nguyên tổng
    localStorage.setItem("cart_final", totalAmount);
    document.getElementById("Price2").textContent = totalAmount.toLocaleString() + "đ";
}























































            // Tải lại trang để cập nhật giỏ hàng
            location.reload();
        }
    </script>
</body>
</html>