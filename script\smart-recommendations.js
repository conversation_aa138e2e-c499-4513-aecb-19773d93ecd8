// Smart Product Recommendations Script

document.addEventListener('DOMContentLoaded', function() {
    // Initialize recommendation system
    initSmartRecommendations();
    
    // Setup event listeners
    setupEventListeners();
});

// Product database (in a real app, this would come from a backend API)
const productDatabase = [
    {
        id: 1,
        name: "<PERSON><PERSON> sơ mi trắng cơ bản",
        price: "350.000đ",
        category: "tops",
        gender: "female",
        style: ["casual", "formal", "minimalist"],
        color: "white",
        season: ["spring", "summer", "fall", "winter"],
        occasion: ["work", "daily"],
        image: "./img womens/women 1.webp",
        tags: ["basic", "office", "versatile"]
    },
    {
        id: 2,
        name: "Quần jean xanh đậm",
        price: "450.000đ",
        category: "bottoms",
        gender: "female",
        style: ["casual", "streetwear"],
        color: "blue",
        season: ["spring", "fall", "winter"],
        occasion: ["daily", "travel"],
        image: "./img womens/women 2.webp",
        tags: ["denim", "casual", "everyday"]
    },
    {
        id: 3,
        name: "<PERSON><PERSON><PERSON> hoa dáng xòe",
        price: "550.000đ",
        category: "dresses",
        gender: "female",
        style: ["vintage", "bohemian"],
        color: "multicolor",
        season: ["spring", "summer"],
        occasion: ["party", "date"],
        image: "./img womens/women 3.webp",
        tags: ["floral", "feminine", "summer"]
    },
    {
        id: 4,
        name: "Áo khoác denim",
        price: "650.000đ",
        category: "outerwear",
        gender: "female",
        style: ["casual", "streetwear"],
        color: "blue",
        season: ["spring", "fall"],
        occasion: ["daily", "travel"],
        image: "./img womens/women 4.webp",
        tags: ["denim", "layering", "casual"]
    },
    {
        id: 5,
        name: "Áo thun trắng cổ tròn",
        price: "250.000đ",
        category: "tops",
        gender: "female",
        style: ["casual", "minimalist"],
        color: "white",
        season: ["spring", "summer", "fall"],
        occasion: ["daily", "sport"],
        image: "./img womens/women 5.webp",
        tags: ["basic", "essential", "versatile"]
    },
    {
        id: 6,
        name: "Áo sơ mi kẻ sọc",
        price: "380.000đ",
        category: "tops",
        gender: "male",
        style: ["casual", "formal"],
        color: "blue",
        season: ["spring", "summer", "fall", "winter"],
        occasion: ["work", "daily"],
        image: "./img mens/men 1.jpg",
        tags: ["striped", "office", "versatile"]
    },
    {
        id: 7,
        name: "Quần jean đen",
        price: "480.000đ",
        category: "bottoms",
        gender: "male",
        style: ["casual", "streetwear"],
        color: "black",
        season: ["spring", "fall", "winter"],
        occasion: ["daily", "travel"],
        image: "./img mens/men 2.jpg",
        tags: ["denim", "casual", "everyday"]
    },
    {
        id: 8,
        name: "Áo khoác bomber",
        price: "750.000đ",
        category: "outerwear",
        gender: "male",
        style: ["streetwear", "casual"],
        color: "black",
        season: ["fall", "winter"],
        occasion: ["daily", "travel"],
        image: "./img mens/men 3.jpg",
        tags: ["trendy", "layering", "urban"]
    }
];

// User behavior tracking
let userBehavior = {
    viewedProducts: [],
    clickedProducts: [],
    addedToCart: [],
    addedToWishlist: [],
    searchQueries: [],
    categoryViews: [],
    stylePreferences: []
};

// Load user behavior from localStorage if available
function loadUserBehavior() {
    const savedBehavior = localStorage.getItem('userBehavior');
    if (savedBehavior) {
        userBehavior = JSON.parse(savedBehavior);
    }
    
    // Also check if user is logged in to get more personalized data
    const user = localStorage.getItem('user');
    if (user) {
        const userData = JSON.parse(user);
        if (userData.stylePreferences) {
            userBehavior.stylePreferences = userData.stylePreferences;
        }
    }
}

// Save user behavior to localStorage
function saveUserBehavior() {
    localStorage.setItem('userBehavior', JSON.stringify(userBehavior));
}

// Track product view
function trackProductView(productId) {
    if (!userBehavior.viewedProducts.includes(productId)) {
        userBehavior.viewedProducts.push(productId);
        
        // Keep only the last 20 viewed products
        if (userBehavior.viewedProducts.length > 20) {
            userBehavior.viewedProducts.shift();
        }
        
        saveUserBehavior();
    }
}

// Track product click
function trackProductClick(productId) {
    userBehavior.clickedProducts.push(productId);
    
    // Keep only the last 20 clicked products
    if (userBehavior.clickedProducts.length > 20) {
        userBehavior.clickedProducts.shift();
    }
    
    saveUserBehavior();
}

// Track add to cart
function trackAddToCart(productId) {
    userBehavior.addedToCart.push(productId);
    
    // Keep only the last 20 added to cart products
    if (userBehavior.addedToCart.length > 20) {
        userBehavior.addedToCart.shift();
    }
    
    saveUserBehavior();
}

// Track add to wishlist
function trackAddToWishlist(productId) {
    userBehavior.addedToWishlist.push(productId);
    
    // Keep only the last 20 added to wishlist products
    if (userBehavior.addedToWishlist.length > 20) {
        userBehavior.addedToWishlist.shift();
    }
    
    saveUserBehavior();
}

// Track search query
function trackSearchQuery(query) {
    userBehavior.searchQueries.push(query);
    
    // Keep only the last 10 search queries
    if (userBehavior.searchQueries.length > 10) {
        userBehavior.searchQueries.shift();
    }
    
    saveUserBehavior();
}

// Track category view
function trackCategoryView(category) {
    userBehavior.categoryViews.push(category);
    
    // Keep only the last 10 category views
    if (userBehavior.categoryViews.length > 10) {
        userBehavior.categoryViews.shift();
    }
    
    saveUserBehavior();
}

// Get recommended products based on user behavior
function getRecommendedProducts(limit = 4) {
    // Load user behavior
    loadUserBehavior();
    
    // If no user behavior, return random products
    if (userBehavior.viewedProducts.length === 0 && 
        userBehavior.clickedProducts.length === 0 && 
        userBehavior.addedToCart.length === 0 && 
        userBehavior.stylePreferences.length === 0) {
        return getRandomProducts(limit);
    }
    
    // Calculate product scores based on user behavior
    const productScores = {};
    
    // Initialize scores for all products
    productDatabase.forEach(product => {
        productScores[product.id] = 0;
    });
    
    // Score based on viewed products
    userBehavior.viewedProducts.forEach(productId => {
        const product = productDatabase.find(p => p.id === productId);
        if (product) {
            // Increase score for this product
            productScores[productId] += 1;
            
            // Increase score for similar products
            productDatabase.forEach(p => {
                if (p.id !== productId) {
                    // Similar category
                    if (p.category === product.category) {
                        productScores[p.id] += 0.5;
                    }
                    
                    // Similar style
                    if (p.style.some(style => product.style.includes(style))) {
                        productScores[p.id] += 0.3;
                    }
                    
                    // Similar color
                    if (p.color === product.color) {
                        productScores[p.id] += 0.2;
                    }
                    
                    // Similar occasion
                    if (p.occasion.some(occ => product.occasion.includes(occ))) {
                        productScores[p.id] += 0.2;
                    }
                }
            });
        }
    });
    
    // Score based on clicked products (higher weight)
    userBehavior.clickedProducts.forEach(productId => {
        const product = productDatabase.find(p => p.id === productId);
        if (product) {
            // Increase score for this product
            productScores[productId] += 2;
            
            // Increase score for similar products
            productDatabase.forEach(p => {
                if (p.id !== productId) {
                    // Similar category
                    if (p.category === product.category) {
                        productScores[p.id] += 1;
                    }
                    
                    // Similar style
                    if (p.style.some(style => product.style.includes(style))) {
                        productScores[p.id] += 0.6;
                    }
                    
                    // Similar color
                    if (p.color === product.color) {
                        productScores[p.id] += 0.4;
                    }
                    
                    // Similar occasion
                    if (p.occasion.some(occ => product.occasion.includes(occ))) {
                        productScores[p.id] += 0.4;
                    }
                }
            });
        }
    });
    
    // Score based on added to cart (highest weight)
    userBehavior.addedToCart.forEach(productId => {
        const product = productDatabase.find(p => p.id === productId);
        if (product) {
            // Increase score for this product
            productScores[productId] += 3;
            
            // Increase score for similar products
            productDatabase.forEach(p => {
                if (p.id !== productId) {
                    // Similar category
                    if (p.category === product.category) {
                        productScores[p.id] += 1.5;
                    }
                    
                    // Similar style
                    if (p.style.some(style => product.style.includes(style))) {
                        productScores[p.id] += 0.9;
                    }
                    
                    // Similar color
                    if (p.color === product.color) {
                        productScores[p.id] += 0.6;
                    }
                    
                    // Similar occasion
                    if (p.occasion.some(occ => product.occasion.includes(occ))) {
                        productScores[p.id] += 0.6;
                    }
                }
            });
        }
    });
    
    // Score based on style preferences
    userBehavior.stylePreferences.forEach(style => {
        productDatabase.forEach(product => {
            if (product.style.includes(style)) {
                productScores[product.id] += 2;
            }
        });
    });
    
    // Sort products by score
    const sortedProducts = productDatabase.sort((a, b) => {
        return productScores[b.id] - productScores[a.id];
    });
    
    // Return top N products
    return sortedProducts.slice(0, limit);
}

// Get random products
function getRandomProducts(limit = 4) {
    const shuffled = [...productDatabase].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, limit);
}

// Get complementary products for a given product
function getComplementaryProducts(productId, limit = 4) {
    const product = productDatabase.find(p => p.id === productId);
    if (!product) {
        return getRandomProducts(limit);
    }
    
    // Calculate complementary scores
    const complementaryScores = {};
    
    productDatabase.forEach(p => {
        if (p.id !== productId) {
            complementaryScores[p.id] = 0;
            
            // Different category but same style
            if (p.category !== product.category && p.style.some(style => product.style.includes(style))) {
                complementaryScores[p.id] += 2;
            }
            
            // Matching color scheme
            if (p.color === product.color || 
                (product.color === 'black' && ['white', 'gray'].includes(p.color)) ||
                (product.color === 'white' && ['black', 'gray'].includes(p.color))) {
                complementaryScores[p.id] += 1;
            }
            
            // Same occasion
            if (p.occasion.some(occ => product.occasion.includes(occ))) {
                complementaryScores[p.id] += 1.5;
            }
            
            // Same season
            if (p.season.some(season => product.season.includes(season))) {
                complementaryScores[p.id] += 1;
            }
            
            // Complementary categories
            if ((product.category === 'tops' && ['bottoms', 'outerwear'].includes(p.category)) ||
                (product.category === 'bottoms' && ['tops', 'outerwear'].includes(p.category)) ||
                (product.category === 'dresses' && ['outerwear', 'accessories'].includes(p.category))) {
                complementaryScores[p.id] += 3;
            }
        }
    });
    
    // Sort products by complementary score
    const sortedProducts = productDatabase
        .filter(p => p.id !== productId)
        .sort((a, b) => {
            return complementaryScores[b.id] - complementaryScores[a.id];
        });
    
    // Return top N products
    return sortedProducts.slice(0, limit);
}

// Initialize smart recommendations
function initSmartRecommendations() {
    // Load user behavior
    loadUserBehavior();
    
    // Update recommendations on the page
    updateRecommendations();
}

// Update recommendations on the page
function updateRecommendations() {
    // Update personalized recommendations section if it exists
    const personalizedRecommendationsContainer = document.getElementById('personalized-recommendations');
    if (personalizedRecommendationsContainer) {
        const recommendations = getRecommendedProducts(4);
        renderRecommendations(personalizedRecommendationsContainer, recommendations, 'Đề xuất cho bạn');
    }
    
    // Update complementary products if on product detail page
    const productDetailContainer = document.querySelector('.product-detail');
    if (productDetailContainer) {
        const productId = parseInt(productDetailContainer.dataset.productId);
        if (productId) {
            const complementaryContainer = document.getElementById('complementary-products');
            if (complementaryContainer) {
                const complementaryProducts = getComplementaryProducts(productId, 4);
                renderRecommendations(complementaryContainer, complementaryProducts, 'Sản phẩm phối hợp');
            }
        }
    }
}

// Render recommendations to container
function renderRecommendations(container, products, title) {
    // Clear container
    container.innerHTML = '';
    
    // Add title if provided
    if (title) {
        const titleElement = document.createElement('h2');
        titleElement.className = 'section-title';
        titleElement.textContent = title;
        container.appendChild(titleElement);
    }
    
    // Create products grid
    const productsGrid = document.createElement('div');
    productsGrid.className = 'products-grid';
    
    // Add products
    products.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Add to container
    container.appendChild(productsGrid);
}

// Create product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.dataset.productId = product.id;
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.name}">
            <div class="product-overlay">
                <button class="btn-shop add-to-cart-btn" data-product-id="${product.id}">Thêm vào giỏ</button>
                <button class="btn-quick-view quick-view-btn" data-product-id="${product.id}">Xem nhanh</button>
            </div>
        </div>
        <div class="product-info">
            <h3 class="product-name">${product.name}</h3>
            <p class="product-price">${product.price}</p>
        </div>
    `;
    
    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Track product views
    document.querySelectorAll('.product-card').forEach(card => {
        const productId = parseInt(card.dataset.productId);
        if (productId) {
            // Track view when card is visible in viewport
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        trackProductView(productId);
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(card);
            
            // Track clicks
            card.addEventListener('click', () => {
                trackProductClick(productId);
            });
        }
    });
    
    // Track add to cart
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const productId = parseInt(button.dataset.productId);
            if (productId) {
                trackAddToCart(productId);
                updateRecommendations();
            }
        });
    });
    
    // Track add to wishlist
    document.querySelectorAll('.wishlist-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const productId = parseInt(button.dataset.productId);
            if (productId) {
                trackAddToWishlist(productId);
                updateRecommendations();
            }
        });
    });
    
    // Track search queries
    const searchForm = document.querySelector('.search-box');
    if (searchForm) {
        searchForm.addEventListener('submit', (e) => {
            const searchInput = document.getElementById('search-input');
            if (searchInput && searchInput.value.trim()) {
                trackSearchQuery(searchInput.value.trim());
            }
        });
    }
    
    // Track category views
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', () => {
            const categoryName = card.querySelector('h3').textContent;
            if (categoryName) {
                trackCategoryView(categoryName);
            }
        });
    });
}
