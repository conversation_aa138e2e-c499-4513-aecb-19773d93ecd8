body {
    font-family: 'Arial', sans-serif;
    text-align: center;
    background: linear-gradient(135deg, #ff9a9e, #fad0c4);
    color: white;
    margin: 20px;
}

h1, h2 {
    margin-bottom: 10px;
}

.spin-section, .redeem-section {
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 10px;
    margin: 20px auto;
    width: 80%;
    max-width: 400px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

button {
    background: linear-gradient(45deg, #ff5733, #ff8c00);
    border: none;
    padding: 10px 20px;
    color: white;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s;
}

button:hover {
    transform: scale(1.05);
    background: linear-gradient(45deg, #e64a19, #ff4500);
}
