// Marquee Animation Script
document.addEventListener('DOMContentLoaded', function() {
    // Ki<PERSON>m tra xem có phần tử marquee không
    const marqueeContainer = document.querySelector('.marquee-container');
    if (!marqueeContainer) return;

    const marqueeContent = document.querySelector('.marquee-content');
    if (!marqueeContent) return;

    // Đảm bảo marquee chuyển động bằng cách thêm animation
    marqueeContent.style.animation = 'marquee 30s linear infinite';

    // Danh sách các thông báo quảng cáo
    const promotions = [
        { icon: 'fas fa-tags', text: 'Giảm giá 50% cho tất cả sản phẩm mùa hè' },
        { icon: 'fas fa-shipping-fast', text: 'Miễn phí vận chuyển cho đơn hàng trên 500.000đ' },
        { icon: 'fas fa-gift', text: 'Tặng quà cho 100 khách hàng đầu tiên' },
        { icon: 'fas fa-percent', text: '<PERSON><PERSON><PERSON><PERSON> thêm 10% khi thanh toán qua ví điện tử' },
        { icon: 'fas fa-calendar-alt', text: 'Flash sale mỗi ngày từ 12h-14h' },
        { icon: 'fas fa-star', text: 'Ưu đãi đặc biệt cho thành viên VIP' },
        { icon: 'fas fa-clock', text: 'Đơn hàng trên 1 triệu được tặng thẻ thành viên' },
        { icon: 'fas fa-heart', text: 'Giảm 15% cho khách hàng sinh nhật trong tháng' }
    ];

    // Tạo các phần tử marquee-item từ danh sách promotions
    function createMarqueeItems() {
        // Xóa nội dung hiện tại
        marqueeContent.innerHTML = '';

        // Thêm các mục quảng cáo
        promotions.forEach(promo => {
            const item = document.createElement('div');
            item.className = 'marquee-item';
            item.innerHTML = `<i class="${promo.icon}"></i> ${promo.text}`;
            marqueeContent.appendChild(item);
        });

        // Nhân đôi nội dung để tạo hiệu ứng liên tục
        const items = [...marqueeContent.querySelectorAll('.marquee-item')];
        items.forEach(item => {
            const clone = item.cloneNode(true);
            marqueeContent.appendChild(clone);
        });
    }

    // Tạo animation cho marquee
    function animateMarquee() {
        // Lấy chiều rộng của nội dung
        const contentWidth = marqueeContent.scrollWidth / 2; // Chia 2 vì đã nhân đôi nội dung

        // Tính toán thời gian animation dựa trên chiều rộng
        const duration = contentWidth / 60; // 60px mỗi giây

        // Thiết lập animation
        marqueeContent.style.animationDuration = `${duration}s`;
        marqueeContent.style.animationName = 'marquee';
        marqueeContent.style.animationTimingFunction = 'linear';
        marqueeContent.style.animationIterationCount = 'infinite';
    }

    // Tạo keyframes animation
    function createKeyframes() {
        // Kiểm tra xem đã có style element chưa
        let styleElement = document.getElementById('marquee-keyframes');

        if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'marquee-keyframes';
            document.head.appendChild(styleElement);
        }

        // Thêm keyframes
        styleElement.textContent = `
            @keyframes marquee {
                0% {
                    transform: translateX(0);
                }
                100% {
                    transform: translateX(-50%);
                }
            }
        `;
    }

    // Tạo hiệu ứng hover pause
    function setupHoverPause() {
        marqueeContainer.addEventListener('mouseenter', function() {
            marqueeContent.style.animationPlayState = 'paused';
        });

        marqueeContainer.addEventListener('mouseleave', function() {
            marqueeContent.style.animationPlayState = 'running';
        });
    }

    // Thay đổi màu nền của marquee theo thời gian
    function setupColorChange() {
        const colors = [
            'linear-gradient(90deg, #3498db, #2c3e50)',
            'linear-gradient(90deg, #e74c3c, #c0392b)',
            'linear-gradient(90deg, #2ecc71, #27ae60)',
            'linear-gradient(90deg, #f39c12, #d35400)',
            'linear-gradient(90deg, #9b59b6, #8e44ad)'
        ];

        let colorIndex = 0;

        // Thay đổi màu mỗi 10 giây
        setInterval(() => {
            colorIndex = (colorIndex + 1) % colors.length;
            marqueeContainer.style.background = colors[colorIndex];

            // Thêm hiệu ứng transition
            marqueeContainer.style.transition = 'background 2s ease';
        }, 10000);
    }

    // Khởi tạo
    createMarqueeItems();
    createKeyframes();
    animateMarquee();
    setupHoverPause();
    setupColorChange();

    // Cập nhật animation khi resize
    window.addEventListener('resize', animateMarquee);
});
