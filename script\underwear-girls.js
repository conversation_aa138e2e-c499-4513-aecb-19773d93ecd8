// Underwear Girls JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sample product data
    const products = [
        {
            id: 'g1',
            name: '<PERSON><PERSON><PERSON><PERSON><PERSON>ong <PERSON>, <PERSON><PERSON><PERSON>n <PERSON>',
            price: 199000,
            originalPrice: 249000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvydtwnrtk2j46@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvydtwnrtk2j46@resize_w450_nl.webp',
            category: 'underwear',
            colors: ['Hồng', 'Trắng', 'Vàng nhạt'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-16Y'],
            rating: 4.9,
            reviews: 124,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'g2',
            name: '<PERSON><PERSON><PERSON>ái, Thấm Hút Tốt AMYBRA B8999',
            price: 149000,
            originalPrice: 179000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7agl07bbqr7c9@resize_w450_nl.webp',
            category: 'undershirt',
            colors: ['Trắng', 'Hồng nhạt', 'Xanh mint'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y'],
            rating: 4.8,
            reviews: 98,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'g3',
            name: 'Bộ đồ lót  Cho Bé Chất Su Nhẹ Nhàng Thoáng Mát - Áo Lót Học Sinh Không Mút Mỏng Nhẹ Thoải Mái SAVILL B667',
            price: 249000,
            originalPrice: 299000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m8pmgqrcx2mfa7@resize_w450_nl.webp',
            category: 'set',
            colors: ['Hồng', 'Tím nhạt', 'Xanh mint'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y'],
            rating: 4.9,
            reviews: 87,
            isNew: true,
            isBestSeller: true
        },
        {
            id: 'g4',
            name: 'Bộ đồ ngủ dài tay Modal cỡ trung bình 80-170cm dành cho trẻ em Phiên bản Hàn Quốc',
            price: 349000,
            originalPrice: 429000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rd4n-m7vp1t0sekg344@resize_w450_nl.webp',
            category: 'sleepwear',
            colors: ['Hồng', 'Tím', 'Xanh dương'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-15Y'],
            rating: 4.7,
            reviews: 76,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'g5',
            name: 'Bộ 3 đôi tất họa tiết dễ thương',
            price: 99000,
            originalPrice: 129000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m834lddrrlafa5@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m834lddrrlafa5@resize_w450_nl.webp',
            category: 'socks',
            colors: ['Hồng', 'Tím', 'Xanh mint'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-18Y'],
            rating: 4.6,
            reviews: 112,
            isNew: false,
            isBestSeller: false
        },
        {
            id: 'g6',
            name: 'Bộ đồ ngủ pyjama dài tay cotton',
            price: 399000,
            originalPrice: 499000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7reqe-m8078dl903wgd1@resize_w450_nl.webp',
            category: 'sleepwear',
            colors: ['Hồng', 'Tím nhạt', 'Xanh mint'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-11Y'],
            rating: 4.8,
            reviews: 94,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'g7',
            name: 'Quần lót nữ cotton viền ren lượn sóng , Quần lót cạp màu hình thỏ dễ thương ',
            price: 229000,
            originalPrice: 279000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lqx0i044pvo957@resize_w450_nl.webp',
            category: 'underwear',
            colors: ['Hồng', 'Trắng', 'Vàng nhạt'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-17Y'],
            rating: 4.9,
            reviews: 156,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'g8',
            name: 'Áo lá học sinh cấp 2 - Áo lót 2 dây cài sau hỗ trợ phát triển ngực cách tự nhiên cho bé',
            price: 179000,
            originalPrice: 229000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0tys8zdrz4fd8@resize_w450_nl.webp',
            category: 'undershirt',
            colors: ['Trắng', 'Hồng nhạt', 'Vàng nhạt'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-11Y'],
            rating: 4.7,
            reviews: 87,
            isNew: false,
            isBestSeller: false
        }
    ];

    // Function to render products
    function renderProducts(productsToRender) {
        const productsContainer = document.getElementById('products-container');
        if (!productsContainer) return;

        productsContainer.innerHTML = '';

        productsToRender.forEach(product => {
            const discount = Math.round((1 - product.price / product.originalPrice) * 100);

            const productCard = document.createElement('div');
            productCard.className = 'product-card';
            productCard.setAttribute('data-category', product.category);

            // Check if product is in wishlist
            const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
            const isInWishlist = wishlist.some(item => item.id === product.id);

            productCard.innerHTML = `
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}" onerror="this.src='./img/placeholder.jpg'">
                    ${product.isNew ? '<span class="product-tag new-tag">Mới</span>' : ''}
                    ${product.isBestSeller ? '<span class="product-tag bestseller-tag">Bán chạy</span>' : ''}
                    ${discount > 0 ? `<span class="product-tag sale-tag">-${discount}%</span>` : ''}
                    <div class="product-overlay">
                        <button class="product-overlay-btn quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="product-overlay-btn add-to-wishlist-btn" data-product-id="${product.id}">
                            <i class="${isInWishlist ? 'fas' : 'far'} fa-heart" ${isInWishlist ? 'style="color: #ff6b6b;"' : ''}></i>
                        </button>
                        <button class="product-overlay-btn add-to-cart-btn" data-product-id="${product.id}">
                            <i class="fas fa-shopping-bag"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.name}</h3>
                    <div class="product-price">
                        ${discount > 0 ? `<span class="original-price">${product.originalPrice.toLocaleString()}đ</span>` : ''}
                        <span class="current-price">${product.price.toLocaleString()}đ</span>
                    </div>
                    <div class="product-meta">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <span>${product.rating} (${product.reviews})</span>
                        </div>
                        <div class="product-sizes">
                            ${product.sizes.slice(0, 3).map(size => `<span class="product-size">${size}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;

            productsContainer.appendChild(productCard);
        });

        // Add event listeners to buttons
        document.querySelectorAll('.quick-view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                quickViewProduct(productId);
            });
        });

        document.querySelectorAll('.add-to-wishlist-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToWishlist(productId);
            });
        });

        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToCart(productId);
            });
        });
    }

    // Function to filter products
    function filterProducts() {
        const activeFilter = document.querySelector('.filter-btn.active');
        if (!activeFilter) return products;

        const filterValue = activeFilter.getAttribute('data-filter');
        if (filterValue === 'all') return products;

        return products.filter(product => product.category === filterValue);
    }

    // Function to sort products
    function sortProducts(productsToSort) {
        const sortSelect = document.getElementById('sort-select');
        if (!sortSelect) return productsToSort;

        const sortValue = sortSelect.value;

        switch (sortValue) {
            case 'price-asc':
                return [...productsToSort].sort((a, b) => a.price - b.price);
            case 'price-desc':
                return [...productsToSort].sort((a, b) => b.price - a.price);
            case 'newest':
                return [...productsToSort].sort((a, b) => b.isNew - a.isNew);
            case 'discount':
                return [...productsToSort].sort((a, b) => {
                    const discountA = (a.originalPrice - a.price) / a.originalPrice;
                    const discountB = (b.originalPrice - b.price) / b.originalPrice;
                    return discountB - discountA;
                });
            case 'popular':
            default:
                return [...productsToSort].sort((a, b) => {
                    if (a.isBestSeller && !b.isBestSeller) return -1;
                    if (!a.isBestSeller && b.isBestSeller) return 1;
                    return b.reviews - a.reviews;
                });
        }
    }

    // Function to update products based on filters and sorting
    function updateProducts() {
        const filteredProducts = filterProducts();
        const sortedProducts = sortProducts(filteredProducts);
        renderProducts(sortedProducts);
    }

    // Initialize products
    updateProducts();

    // Add event listeners to filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            updateProducts();
        });
    });

    // Add event listener to sort select
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', updateProducts);
    }

    // Quick view product function
    function quickViewProduct(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        showNotification(`Xem nhanh: ${product.name}`, 'info');
        // In a real implementation, this would open a modal with product details
    }

    // Add to wishlist function
    function addToWishlist(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        // Check if user is logged in
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.isLoggedIn) {
            showNotification('Vui lòng đăng nhập để thêm sản phẩm vào danh sách yêu thích', 'warning');
            return;
        }

        // Get current wishlist from localStorage
        let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

        // Check if product already exists in wishlist
        const existingIndex = wishlist.findIndex(item => item.id === product.id);

        if (existingIndex !== -1) {
            // Remove from wishlist if already exists
            wishlist.splice(existingIndex, 1);
            showNotification(`Đã xóa ${product.name} khỏi danh sách yêu thích`, 'info');

            // Update button icon
            const wishlistBtn = document.querySelector(`.add-to-wishlist-btn[data-product-id="${productId}"] i`);
            if (wishlistBtn) {
                wishlistBtn.classList.remove('fas');
                wishlistBtn.classList.add('far');
                wishlistBtn.style.color = '';
            }
        } else {
            // Add to wishlist
            wishlist.push({
                id: product.id,
                title: product.name,
                name: product.name,
                price: product.price.toLocaleString() + 'đ',
                image: product.image,
                addedAt: new Date().toISOString()
            });
            showNotification(`Đã thêm ${product.name} vào danh sách yêu thích`, 'success');

            // Update button icon
            const wishlistBtn = document.querySelector(`.add-to-wishlist-btn[data-product-id="${productId}"] i`);
            if (wishlistBtn) {
                wishlistBtn.classList.remove('far');
                wishlistBtn.classList.add('fas');
                wishlistBtn.style.color = '#ff6b6b';
            }
        }

        // Save wishlist to localStorage
        localStorage.setItem('wishlist', JSON.stringify(wishlist));

        // Update wishlist count if function exists
        if (typeof updateWishlistCount === 'function') {
            updateWishlistCount();
        }
    }

    // Add to cart function
    function addToCart(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        // Check if user is logged in
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.isLoggedIn) {
            // Show login prompt
            showLoginPrompt();
            return;
        }

        showNotification(`Đã thêm ${product.name} vào giỏ hàng`, 'success');

        // Get current cart from localStorage
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Add product to cart
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price.toLocaleString() + 'đ', // Chuyển đổi giá thành chuỗi có định dạng
            image: product.image,
            image1: product.image, // Thêm image1 để tương thích với AddCart.html
            quantity: 1
        });

        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));

        // Update cart count
        updateCartCount();
    }

    // Show login prompt function
    function showLoginPrompt() {
        // Create login prompt container
        const loginPrompt = document.createElement('div');
        loginPrompt.className = 'login-prompt-overlay';
        loginPrompt.style.position = 'fixed';
        loginPrompt.style.top = '0';
        loginPrompt.style.left = '0';
        loginPrompt.style.width = '100%';
        loginPrompt.style.height = '100%';
        loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        loginPrompt.style.display = 'flex';
        loginPrompt.style.alignItems = 'center';
        loginPrompt.style.justifyContent = 'center';
        loginPrompt.style.zIndex = '9999';
        loginPrompt.style.opacity = '0';
        loginPrompt.style.transition = 'opacity 0.3s ease';

        loginPrompt.innerHTML = `
            <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                    <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                    <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                </div>
                <div class="login-prompt-body" style="padding: 20px;">
                    <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                    <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                        <a href="login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                        <a href="register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                    </div>
                </div>
            </div>
        `;

        // Add to body
        document.body.appendChild(loginPrompt);

        // Show with animation
        setTimeout(() => {
            loginPrompt.style.opacity = '1';
        }, 10);

        // Close button event
        const closeButton = loginPrompt.querySelector('.login-prompt-close');
        closeButton.addEventListener('click', function() {
            loginPrompt.style.opacity = '0';
            setTimeout(() => {
                loginPrompt.remove();
            }, 300);
        });

        // Save current URL to redirect back after login
        localStorage.setItem('redirectAfterLogin', window.location.href);
    }

    // Update cart count function
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = cart.length;
        }
    }

    // Update wishlist count function
    function updateWishlistCount() {
        const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
        const wishlistCount = document.querySelector('.wishlist-count');
        if (wishlistCount) {
            wishlistCount.textContent = wishlist.length;
        }
    }

    // Initialize counts
    updateCartCount();
    updateWishlistCount();

    // Load more button functionality
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            showNotification('Đang tải thêm sản phẩm...', 'info');
            // In a real implementation, this would load more products
            setTimeout(() => {
                showNotification('Đã tải tất cả sản phẩm', 'success');
                this.disabled = true;
                this.textContent = 'Đã tải tất cả';
            }, 1000);
        });
    }
});
