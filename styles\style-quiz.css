/* Style Quiz Styles */

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #f8f9fa;
    --accent-color: #339af0;
    --success-color: #40c057;
    --warning-color: #fab005;
    --danger-color: #fa5252;
    --text-color: #343a40;
    --light-text: #868e96;
    --border-color: #dee2e6;
    --background-color: #ffffff;
    --hover-color: #ff5252;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* Main Container */
.style-quiz-container {
    padding: 40px 0;
    background-color: var(--secondary-color);
    min-height: 80vh;
}

.quiz-header {
    text-align: center;
    margin-bottom: 40px;
}

.quiz-header h1 {
    font-size: 2.5rem;
    color: var(--text-color);
    margin-bottom: 10px;
    position: relative;
    display: inline-block;
}

.quiz-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

.quiz-header p {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
    margin: 0 auto;
}

/* Quiz Content */
.quiz-content {
    background-color: var(--background-color);
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--box-shadow);
    max-width: 800px;
    margin: 0 auto;
}

/* Progress Bar */
.quiz-progress {
    margin-bottom: 30px;
}

.progress-bar {
    height: 6px;
    background-color: var(--border-color);
    border-radius: 3px;
    margin-bottom: 15px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--border-color);
    z-index: 1;
}

.step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--background-color);
    border: 2px solid var(--border-color);
    color: var(--light-text);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.step.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.step.completed {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

/* Quiz Steps */
.quiz-step {
    display: none;
}

.quiz-step.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.quiz-step h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 10px;
}

.quiz-step p {
    color: var(--light-text);
    margin-bottom: 25px;
}

/* Form Groups */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 10px;
    font-weight: 500;
}

/* Option Buttons */
.option-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.option-btn {
    padding: 10px 20px;
    background-color: var(--secondary-color);
    border: 2px solid var(--border-color);
    border-radius: 30px;
    color: var(--text-color);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.option-btn:hover {
    background-color: rgba(255, 107, 107, 0.1);
    border-color: var(--primary-color);
}

.option-btn.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Range Sliders */
.range-slider {
    width: 100%;
    height: 6px;
    -webkit-appearance: none;
    background: var(--border-color);
    border-radius: 3px;
    outline: none;
    margin-bottom: 10px;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.range-value {
    text-align: center;
    font-size: 0.9rem;
    color: var(--light-text);
}

/* Style Options */
.style-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.style-option {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
}

.style-option:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.style-option.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.3);
}

.style-image {
    height: 150px;
    overflow: hidden;
}

.style-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.style-option:hover .style-image img {
    transform: scale(1.05);
}

.style-name {
    padding: 10px;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.style-description {
    padding: 10px;
    font-size: 0.8rem;
    color: var(--light-text);
    text-align: center;
}

/* Color Options */
.color-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.color-option {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    box-shadow: 0 0 0 3px var(--primary-color);
}

.color-option.selected::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: white;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    font-size: 1.2rem;
}

.color-name {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    color: var(--text-color);
    white-space: nowrap;
}

/* Occasion Options */
.occasion-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.occasion-option {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.occasion-option:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.occasion-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(255, 107, 107, 0.1);
}

.occasion-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.occasion-name {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
}

/* Budget Options */
.budget-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.budget-option {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.budget-option:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.budget-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(255, 107, 107, 0.1);
}

.budget-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.budget-range {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.budget-description {
    font-size: 0.8rem;
    color: var(--light-text);
}

/* Navigation Buttons */
.quiz-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.nav-btn {
    padding: 12px 25px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

#prev-btn {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

#prev-btn:hover {
    background-color: var(--border-color);
}

#prev-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#next-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

#next-btn:hover {
    background-color: var(--hover-color);
}

/* Results Step */
.loading-results {
    text-align: center;
    padding: 40px 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    margin: 0 auto 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.quiz-results {
    animation: fadeIn 0.5s ease;
}

.quiz-results h2 {
    font-size: 1.8rem;
    color: var(--text-color);
    margin-bottom: 15px;
    text-align: center;
}

#style-name {
    color: var(--primary-color);
}

#style-description {
    text-align: center;
    margin-bottom: 30px;
    color: var(--light-text);
    line-height: 1.6;
}

.style-match {
    text-align: center;
    margin-bottom: 30px;
}

.match-percentage {
    font-size: 3rem;
    font-weight: 700;
    color: var(--success-color);
}

.match-text {
    font-size: 1rem;
    color: var(--light-text);
}

.style-recommendations {
    margin-bottom: 30px;
}

.style-recommendations h3 {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.style-recommendations h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary-color);
}

.recommendation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 20px;
}

.recommendation-item {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.recommendation-item:hover {
    transform: translateY(-5px);
}

.recommendation-image {
    height: 180px;
    overflow: hidden;
}

.recommendation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.recommendation-item:hover .recommendation-image img {
    transform: scale(1.05);
}

.recommendation-info {
    padding: 10px;
    background-color: var(--background-color);
}

.recommendation-name {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommendation-price {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

.style-tips {
    margin-bottom: 30px;
}

.style-tips h3 {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.style-tips h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary-color);
}

#style-tips-list {
    padding-left: 20px;
}

#style-tips-list li {
    margin-bottom: 10px;
    color: var(--text-color);
    line-height: 1.6;
}

.result-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.btn-primary {
    padding: 12px 25px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background-color: var(--hover-color);
    transform: translateY(-3px);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .style-options, .budget-options {
        grid-template-columns: 1fr;
    }
    
    .color-options {
        justify-content: center;
    }
    
    .occasion-options {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .recommendation-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .btn-primary {
        width: 100%;
    }
}
