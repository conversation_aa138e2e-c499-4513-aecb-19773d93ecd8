/* Hero Slider CSS */
@import url('style.css');

.hero-slider {
    position: relative;
    overflow: hidden;
    height: 600px;
}

.slide {
    position: relative;
    height: 600px;
    overflow: hidden;
}

.slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%);
    z-index: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 6s ease;
}

.slick-active .slide img {
    transform: scale(1.1);
}

.slide-content {
    position: absolute;
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    max-width: 550px;
    color: var(--light-text);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 2;
    opacity: 0;
}

.slick-active .slide-content {
    animation: fadeInUp 1s ease forwards 0.5s;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, -50%, 0);
    }
}

.slide-content.dark-content {
    color: var(--dark-text);
    text-shadow: none;
}

.slide-content h1 {
    font-family: var(--heading-font);
    font-size: 3.2rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
    font-weight: 700;
}

.slide-content p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    max-width: 90%;
    line-height: 1.6;
}

.slide-buttons {
    display: flex;
    gap: var(--spacing-md);
}

/* Slick Slider Customization */
.slick-prev, .slick-next {
    z-index: 10;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: background-color var(--transition-fast);
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.slick-prev:hover, .slick-next:hover {
    background-color: var(--accent-color);
}

.slick-prev {
    left: 20px;
}

.slick-next {
    right: 20px;
}

.slick-prev:before, .slick-next:before {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 20px;
    opacity: 1;
}

.slick-prev:before {
    content: '\f104';
}

.slick-next:before {
    content: '\f105';
}

.slick-dots {
    bottom: 20px;
}

.slick-dots li {
    margin: 0 5px;
}

.slick-dots li button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transition: background-color var(--transition-fast);
}

.slick-dots li.slick-active button {
    background-color: var(--accent-color);
}

.slick-dots li button:before {
    display: none;
}

/* Products Slider */
.products-slider {
    margin: 0 -15px;
    position: relative;
}

.product-card {
    padding: 0 15px;
    margin-bottom: var(--spacing-lg);
}

.product-image {
    position: relative;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.product-image img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.product-image:hover img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    bottom: -60px;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    padding: var(--spacing-md) 0;
    text-align: center;
    transition: bottom var(--transition-medium);
    box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.1);
}

.product-image:hover .product-overlay {
    bottom: 0;
}

.product-overlay .btn-shop {
    background-color: var(--accent-color);
    color: white;
    padding: 8px 15px;
    border-radius: var(--border-radius-sm);
    font-size: 0.85rem;
    transition: background-color var(--transition-fast), transform var(--transition-fast);
}

.product-overlay .btn-shop:hover {
    background-color: var(--accent-hover);
    transform: translateY(-2px);
}

.product-info {
    text-align: center;
    padding: var(--spacing-sm) var(--spacing-xs);
}

.product-name {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
    color: var(--primary-color);
    line-height: 1.4;
    height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    color: var(--accent-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.product-colors {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: var(--spacing-xs);
}

.color-option {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    cursor: pointer;
    border: 1px solid #ddd;
    transition: transform var(--transition-fast);
}

.color-option:hover {
    transform: scale(1.2);
}

/* Products Slider Navigation */
.products-slider .slick-prev,
.products-slider .slick-next {
    background-color: #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.products-slider .slick-prev:before,
.products-slider .slick-next:before {
    color: var(--primary-color);
}

.products-slider .slick-prev:hover,
.products-slider .slick-next:hover {
    background-color: var(--accent-color);
}

.products-slider .slick-prev:hover:before,
.products-slider .slick-next:hover:before {
    color: #fff;
}

/* Responsive */
@media (max-width: 992px) {
    .hero-slider, .slide {
        height: 500px;
    }

    .slide-content h1 {
        font-size: 2.5rem;
    }

    .slide-content p {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .hero-slider, .slide {
        height: 450px;
    }

    .slide-content {
        left: 5%;
        max-width: 90%;
    }

    .slide-content h1 {
        font-size: 2rem;
    }

    .slide-content p {
        font-size: 1rem;
    }

    .slide-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .product-image img {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .hero-slider, .slide {
        height: 400px;
    }

    .slide-content h1 {
        font-size: 1.8rem;
        margin-bottom: var(--spacing-sm);
    }

    .slide-content p {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-md);
    }

    .product-image img {
        height: 250px;
    }
}
