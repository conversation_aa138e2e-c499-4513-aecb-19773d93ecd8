// JavaScript chính cho website

document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo số lượng giỏ hàng
    updateCartCount();

    // Xử lý menu mobile
    initMobileMenu();

    // Xử lý tìm kiếm
    initSearch();

    // Xử lý sticky navigation
    initStickyNav();

    // Khởi tạo slider hero
    initHeroSlider();

    // Khởi tạo slider sản phẩm
    initProductSlider();

    // Xử lý form đăng ký nhận tin
    initNewsletterForm();

    // Hiệu ứng animation khi scroll
    initScrollAnimations();
});

// Cập nhật số lượng giỏ hàng
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        cartCount.textContent = cart.length;
    }
}

// Xử lý menu mobile
function initMobileMenu() {
    const menuToggle = document.querySelector('.menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (menuToggle && navMenu) {
        menuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            menuToggle.classList.toggle('active');

            // Thay đổi icon
            const icon = menuToggle.querySelector('i');
            if (icon) {
                if (navMenu.classList.contains('active')) {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                } else {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            }
        });

        // Đóng menu khi click bên ngoài
        document.addEventListener('click', function(event) {
            if (!navMenu.contains(event.target) && !menuToggle.contains(event.target) && navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                menuToggle.classList.remove('active');

                const icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            }
        });

        // Xử lý dropdown trên mobile
        const dropdownItems = document.querySelectorAll('.has-dropdown');
        dropdownItems.forEach(item => {
            item.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    e.preventDefault();
                    const dropdown = this.querySelector('.dropdown-menu');
                    if (dropdown) {
                        if (dropdown.style.display === 'block') {
                            dropdown.style.display = 'none';
                            this.classList.remove('active');
                        } else {
                            // Đóng tất cả dropdown khác
                            dropdownItems.forEach(otherItem => {
                                if (otherItem !== item) {
                                    const otherDropdown = otherItem.querySelector('.dropdown-menu');
                                    if (otherDropdown) {
                                        otherDropdown.style.display = 'none';
                                        otherItem.classList.remove('active');
                                    }
                                }
                            });

                            dropdown.style.display = 'block';
                            this.classList.add('active');
                        }
                    }
                }
            });
        });
    }
}

// Xử lý tìm kiếm
function initSearch() {
    const searchBtn = document.getElementById('search-btn');
    const searchInput = document.getElementById('search-input');

    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', function() {
            performSearch();
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
}

// Thực hiện tìm kiếm
function performSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput && searchInput.value.trim()) {
        const query = searchInput.value.trim();
        localStorage.setItem('query', query);

        // Xác định đường dẫn tương đối dựa trên URL hiện tại
        let searchResultPath;
        if (window.location.pathname.includes('/pages/')) {
            // Nếu đang ở trong thư mục pages
            searchResultPath = 'searchresult.html';
        } else {
            // Nếu đang ở trang chủ hoặc thư mục gốc
            searchResultPath = './pages/searchresult.html';
        }

        window.location.href = searchResultPath + '?q=' + encodeURIComponent(query);
    } else {
        // Hiển thị thông báo nếu không có từ khóa tìm kiếm
        showNotification('Vui lòng nhập từ khóa tìm kiếm', 'warning');
    }
}

// Xử lý sticky navigation
function initStickyNav() {
    const header = document.querySelector('.main-nav');
    if (header) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                header.classList.add('sticky-nav');
                document.body.style.paddingTop = header.offsetHeight + 'px';
            } else {
                header.classList.remove('sticky-nav');
                document.body.style.paddingTop = 0;
            }
        });
    }
}

// Khởi tạo slider hero
function initHeroSlider() {
    const heroSlider = document.querySelector('.hero-slider');
    if (heroSlider && typeof $.fn.slick !== 'undefined') {
        $(heroSlider).slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 5000,
            dots: true,
            arrows: true,
            fade: true,
            cssEase: 'cubic-bezier(0.7, 0, 0.3, 1)',
            prevArrow: '<button class="slick-prev" aria-label="Previous" type="button"><i class="fas fa-chevron-left"></i></button>',
            nextArrow: '<button class="slick-next" aria-label="Next" type="button"><i class="fas fa-chevron-right"></i></button>'
        });
    }
}

// Khởi tạo slider sản phẩm
function initProductSlider() {
    const productsSlider = document.querySelector('.products-slider');
    if (productsSlider && typeof $.fn.slick !== 'undefined') {
        $(productsSlider).slick({
            slidesToShow: 4,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 3000,
            dots: false,
            arrows: true,
            prevArrow: '<button class="slick-prev" aria-label="Previous" type="button"><i class="fas fa-chevron-left"></i></button>',
            nextArrow: '<button class="slick-next" aria-label="Next" type="button"><i class="fas fa-chevron-right"></i></button>',
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2
                    }
                },
                {
                    breakpoint: 576,
                    settings: {
                        slidesToShow: 1
                    }
                }
            ]
        });

        // Xử lý nút thêm vào giỏ hàng
        const addToCartButtons = document.querySelectorAll('.product-overlay .btn-shop');
        addToCartButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productCard = this.closest('.product-card');
                if (productCard) {
                    const productName = productCard.querySelector('.product-name').textContent;
                    const productPrice = productCard.querySelector('.product-price').textContent;
                    const productImage = productCard.querySelector('.product-image img').src;

                    const product = {
                        name: productName,
                        price: productPrice,
                        image: productImage,
                        quantity: 1
                    };

                    addToCart(product);
                }
            });
        });
    }
}

// Thêm sản phẩm vào giỏ hàng
function addToCart(product) {
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
    const existingProductIndex = cart.findIndex(item => item.name === product.name);

    if (existingProductIndex !== -1) {
        // Nếu sản phẩm đã có, tăng số lượng
        cart[existingProductIndex].quantity += 1;
    } else {
        // Nếu sản phẩm chưa có, thêm mới
        cart.push(product);
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();

    // Hiển thị thông báo
    showNotification(`Đã thêm ${product.name} vào giỏ hàng!`);
}

// Hiển thị thông báo
function showNotification(message, type = 'success') {
    // Kiểm tra xem đã có notification container chưa
    let notificationContainer = document.querySelector('.notification-container');

    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }

    // Xác định icon dựa trên loại thông báo
    let icon;
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle"></i>';
            break;
        default:
            icon = '<i class="fas fa-check-circle"></i>';
    }

    // Tạo notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            ${icon}
        </div>
        <div class="notification-message">${message}</div>
    `;

    // Thêm vào container
    notificationContainer.appendChild(notification);

    // Hiệu ứng hiển thị
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Tự động ẩn sau 3 giây
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Xử lý form đăng ký nhận tin
function initNewsletterForm() {
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const emailInput = this.querySelector('input[type="email"]');
            if (emailInput && emailInput.value.trim()) {
                // Giả lập gửi form thành công
                showNotification('Cảm ơn bạn đã đăng ký nhận tin!');
                emailInput.value = '';
            }
        });
    }
}

// Hiệu ứng animation khi scroll
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.fade-in');

    if (animatedElements.length) {
        // Kiểm tra xem phần tử có trong viewport không
        function isInViewport(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                rect.bottom >= 0
            );
        }

        // Xử lý scroll
        function handleScroll() {
            animatedElements.forEach(element => {
                if (isInViewport(element) && !element.classList.contains('animated')) {
                    element.classList.add('animated');
                }
            });
        }

        // Khởi tạo
        window.addEventListener('scroll', handleScroll);
        window.addEventListener('resize', handleScroll);

        // Chạy lần đầu
        handleScroll();
    }
}
