<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/product.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/underwear.css">
    <link rel="stylesheet" type="text/css" href="./styles/underwear-nav.css">
    <link rel="stylesheet" type="text/css" href="./styles/sexy-products.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <title>Đồ Sexy Nữ | Fashion Store</title>

    <style>
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .product-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .product-image {
            position: relative;
            height: 300px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-info {
            padding: 20px;
            background-color: #fff;
        }

        .product-title {
            font-size: 1.1rem;
            margin-bottom: 10px;
            font-weight: 500;
            color: #333;
        }

        .product-price {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .current-price {
            font-weight: 600;
            font-size: 1.2rem;
            color: #e74c3c;
        }

        .old-price {
            text-decoration: line-through;
            color: #999;
            margin-left: 10px;
            font-size: 0.9rem;
        }

        .btn-add-to-cart {
            width: 100%;
            padding: 12px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: 500;
            margin-top: 10px;
        }

        .btn-add-to-cart:hover {
            background-color: #c0392b;
        }

        .badge {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 2;
        }

        .badge.hot {
            background-color: #e74c3c;
            color: white;
        }

        .badge.new {
            background-color: #2ecc71;
            color: white;
        }

        .badge.sale {
            background-color: #f39c12;
            color: white;
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="product-hero" style="background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('./img/sexy-women-hero.jpg');">
        <div class="product-hero-content">
            <h1 data-aos="fade-up">Đồ Sexy Nữ</h1>
            <p data-aos="fade-up" data-aos-delay="200">Tôn vinh vẻ đẹp và sự quyến rũ của phái nữ</p>
        </div>
    </section>

    <!-- Underwear Navigation -->
    <nav class="underwear-nav">
        <div class="container">
            <div class="underwear-nav-container">
                <a href="./underwear-women.html" class="underwear-nav-item">Nội y nữ</a>
                <a href="./sexy-women.html" class="underwear-nav-item active">Đồ sexy nữ</a>
                <a href="./underwear-men.html" class="underwear-nav-item">Nội y nam</a>
                <a href="./underwear-girls.html" class="underwear-nav-item">Nội y bé gái</a>
                <a href="./underwear-boys.html" class="underwear-nav-item">Nội y bé trai</a>
                <a href="./size-guide.html" class="underwear-nav-item">Hướng dẫn chọn size</a>
            </div>
        </div>
    </nav>

    <!-- Featured Collection -->
    <section class="featured-collection">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Bộ Sưu Tập Nổi Bật</h2>
            <p class="section-subtitle text-center" data-aos="fade-up" data-aos-delay="100">Khám phá những bộ sưu tập đặc biệt với thiết kế độc đáo và chất liệu cao cấp</p>

            <div class="featured-grid" data-aos="fade-up" data-aos-delay="200">
                <div class="featured-card">
                    <div class="featured-image">
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-ltzcj26xkf8f49@resize_w450_nl.webp" alt="Bộ sưu tập Midnight Elegance">
                        <div class="featured-overlay">
                            <a href="#" class="btn-view">Xem bộ sưu tập</a>
                        </div>
                    </div>
                    <div class="featured-content">
                        <h3>Midnight Elegance</h3>
                        <p>Bộ sưu tập đồ ngủ và nội y cao cấp với chất liệu ren và lụa sang trọng</p>
                        <div class="featured-meta">
                            <span><i class="fas fa-star"></i> 4.9/5</span>
                            <span><i class="fas fa-heart"></i> 120+</span>
                        </div>
                    </div>
                </div>

                <div class="featured-card">
                    <div class="featured-image">
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-llhiu06s73xyb9@resize_w450_nl.webp" alt="Bộ sưu tập Summer Passion">
                        <div class="featured-overlay">
                            <a href="#" class="btn-view">Xem bộ sưu tập</a>
                        </div>
                    </div>
                    <div class="featured-content">
                        <h3>Summer Passion</h3>
                        <p>Bộ sưu tập đồ tắm và bikini quyến rũ cho mùa hè sôi động</p>
                        <div class="featured-meta">
                            <span><i class="fas fa-star"></i> 4.8/5</span>
                            <span><i class="fas fa-heart"></i> 98+</span>
                        </div>
                    </div>
                </div>

                <div class="featured-card">
                    <div class="featured-image">
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m2hx8bmvkomqed@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m2hx8bmvkomqed@resize_w450_nl.webp" alt="Bộ sưu tập Romantic Lace">
                        <div class="featured-overlay">
                            <a href="#" class="btn-view">Xem bộ sưu tập</a>
                        </div>
                    </div>
                    <div class="featured-content">
                        <h3>Romantic Lace</h3>
                        <p>Bộ sưu tập đồ lót ren tinh tế và quyến rũ cho những dịp đặc biệt</p>
                        <div class="featured-meta">
                            <span><i class="fas fa-star"></i> 4.7/5</span>
                            <span><i class="fas fa-heart"></i> 85+</span>
                        </div>
                    </div>
                </div>

                <div class="featured-card">
                    <div class="featured-image">
                        <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7rdvw-lyxksx7hdq20c6@resize_w450_nl.webp" alt="Bộ sưu tập Elegant Nights">
                        <div class="featured-overlay">
                            <a href="#" class="btn-view">Xem bộ sưu tập</a>
                        </div>
                    </div>
                    <div class="featured-content">
                        <h3>Elegant Nights</h3>
                        <p>Bộ sưu tập đồ ngủ sang trọng với thiết kế hiện đại và quyến rũ</p>
                        <div class="featured-meta">
                            <span><i class="fas fa-star"></i> 4.9/5</span>
                            <span><i class="fas fa-heart"></i> 110+</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories -->
    <section class="product-categories">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Danh Mục Sản Phẩm</h2>
            <p class="section-subtitle text-center" data-aos="fade-up" data-aos-delay="100">Khám phá các danh mục sản phẩm đa dạng của chúng tôi</p>

            <div class="categories-grid">
                <div class="category-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="category-badge">Hot</div>
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m7rnw4zf5hgf6e@resize_w450_nl.webp" alt="Đồ ngủ sexy">
                    <div class="category-content">
                        <h3>Đồ ngủ sexy</h3>
                        <p>Thoải mái và quyến rũ</p>
                        <a href="#" class="btn-shop">Xem bộ sưu tập <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                <div class="category-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="category-badge">Sale</div>
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134202-7rdvd-lz1jlijej66f23@resize_w450_nl.webp" alt="Đồ lót gợi cảm">
                    <div class="category-content">
                        <h3>Đồ lót gợi cảm</h3>
                        <p>Tinh tế và sang trọng</p>
                        <a href="#" class="btn-shop">Xem bộ sưu tập <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                <div class="category-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="category-badge">New</div>
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134201-23030-haildfpqa7nva9@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134201-23030-haildfpqa7nva9@resize_w450_nl.webp" alt="Bodysuit">
                    <div class="category-content">
                        <h3>Bodysuit</h3>
                        <p>Tôn dáng hoàn hảo</p>
                        <a href="#" class="btn-shop">Xem bộ sưu tập <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                <div class="category-card" data-aos="fade-up" data-aos-delay="400">
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lukmvj8fq04ec9@resize_w450_nl.webp" alt="Bikini & Đồ tắm">
                    <div class="category-content">
                        <h3>Bikini & Đồ tắm</h3>
                        <p>Tự tin tỏa sáng</p>
                        <a href="#" class="btn-shop">Xem bộ sưu tập <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .section-subtitle {
            font-size: 1.1rem;
            color: #777;
            max-width: 700px;
            margin: 0 auto 30px;
        }

        .text-center {
            text-align: center;
        }

        /* Bộ Sưu Tập Nổi Bật - Grid Layout */
        .featured-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            margin-top: 40px;
        }

        .featured-card {
            background-color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.08);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
        }

        .featured-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .featured-image {
            position: relative;
            height: 250px;
            overflow: hidden;
        }

        .featured-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.3));
            z-index: 1;
        }

        .featured-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.6s ease;
        }

        .featured-card:hover .featured-image img {
            transform: scale(1.1);
        }

        .featured-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: 2;
        }

        .featured-card:hover .featured-overlay {
            opacity: 1;
        }

        .btn-view {
            display: inline-block;
            padding: 12px 25px;
            background-color: #e74c3c;
            color: white;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 500;
            transform: translateY(20px);
            transition: all 0.4s ease;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .featured-card:hover .btn-view {
            transform: translateY(0);
        }

        .btn-view:hover {
            background-color: #c0392b;
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }

        .featured-content {
            padding: 25px;
            position: relative;
        }

        .featured-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background-color: #e74c3c;
            border-radius: 3px;
        }

        .featured-content h3 {
            font-size: 1.3rem;
            margin: 15px 0 10px;
            color: #2c3e50;
            font-weight: 600;
        }

        .featured-content p {
            color: #777;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .featured-meta {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 0.9rem;
            color: #777;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .featured-meta span {
            display: flex;
            align-items: center;
        }

        .featured-meta i {
            margin-right: 5px;
            color: #f39c12;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .category-card {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .category-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .category-card:hover img {
            transform: scale(1.05);
        }

        .category-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
            color: white;
            transition: padding 0.3s ease;
        }

        .category-card:hover .category-content {
            padding-bottom: 30px;
        }

        .category-content h3 {
            margin: 0 0 10px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .category-content p {
            margin: 0 0 15px;
            opacity: 0.8;
        }

        .btn-shop {
            display: inline-block;
            padding: 8px 15px;
            background-color: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-shop:hover {
            background-color: #c0392b;
        }

        .category-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 2;
            background-color: #e74c3c;
            color: white;
        }

        .category-badge:nth-child(1) {
            background-color: #e74c3c;
        }

        .category-badge:nth-child(2) {
            background-color: #f39c12;
        }

        .category-badge:nth-child(3) {
            background-color: #2ecc71;
        }

        /* Responsive styles for featured grid */
        @media (max-width: 1200px) {
            .featured-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 992px) {
            .featured-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .featured-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <!-- Best Sellers -->
    <section class="best-sellers">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Sản Phẩm Bán Chạy</h2>
            <p class="section-subtitle text-center" data-aos="fade-up" data-aos-delay="100">Những sản phẩm được yêu thích nhất của chúng tôi</p>

            <div class="filter-tabs" data-aos="fade-up" data-aos-delay="200">
                <button class="filter-tab active" data-filter="all">Tất cả</button>
                <button class="filter-tab" data-filter="lingerie">Đồ lót</button>
                <button class="filter-tab" data-filter="sleepwear">Đồ ngủ</button>
                <button class="filter-tab" data-filter="swimwear">Đồ tắm</button>
            </div>

            <div class="products-grid" id="products-container">
                <!-- Product 1 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="100" data-category="sleepwear">
                    <div class="product-image">
                        <span class="badge hot">Hot</span>
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-m0btsi2gdeofe1@resize_w450_nl.webp" alt="Váy ngủ ren Midnight Dream">
                        <div class="product-actions">
                            <button class="btn-wishlist" title="Thêm vào danh sách yêu thích"><i class="far fa-heart"></i></button>
                            <button class="btn-quickview" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span>(25)</span>
                        </div>
                        <h3 class="product-title">Váy ngủ ren Midnight Dream</h3>
                        <div class="product-price">
                            <span class="current-price">450.000đ</span>
                            <span class="old-price">650.000đ</span>
                            <span class="discount-percent">-30%</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option active" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #ff0000;" title="Đỏ"></span>
                            <span class="color-option" style="background-color: #ffffff; border: 1px solid #ddd;" title="Trắng"></span>
                        </div>
                        <button class="btn-add-to-cart">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="200" data-category="lingerie">
                    <div class="product-image">
                        <span class="badge sale">-30%</span>
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lsm4jzkcatok62.webp" alt="Bodysuit ren Passion">
                        <div class="product-actions">
                            <button class="btn-wishlist" title="Thêm vào danh sách yêu thích"><i class="far fa-heart"></i></button>
                            <button class="btn-quickview" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                            <span>(18)</span>
                        </div>
                        <h3 class="product-title">Bodysuit ren Passion</h3>
                        <div class="product-price">
                            <span class="current-price">420.000đ</span>
                            <span class="old-price">600.000đ</span>
                            <span class="discount-percent">-30%</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option active" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #ff0000;" title="Đỏ"></span>
                        </div>
                        <button class="btn-add-to-cart">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="300" data-category="lingerie">
                    <div class="product-image">
                        <span class="badge new">Mới</span>
                        <img src="https://down-vn.img.susercontent.com/file/fc0c521215e7833b6dc1b1c9f8a05562@resize_w450_nl.webp" alt="Bộ đồ lót ren Romantic">
                        <div class="product-actions">
                            <button class="btn-wishlist" title="Thêm vào danh sách yêu thích"><i class="far fa-heart"></i></button>
                            <button class="btn-quickview" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span>(32)</span>
                        </div>
                        <h3 class="product-title">Bộ đồ lót ren Romantic</h3>
                        <div class="product-price">
                            <span class="current-price">380.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option active" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #ff69b4;" title="Hồng"></span>
                            <span class="color-option" style="background-color: #ffffff; border: 1px solid #ddd;" title="Trắng"></span>
                        </div>
                        <button class="btn-add-to-cart">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="400" data-category="swimwear">
                    <div class="product-image">
                        <span class="badge hot">Hot</span>
                        <img src="https://down-vn.img.susercontent.com/file/fc0c521215e7833b6dc1b1c9f8a05562@resize_w450_nl.webp" alt="Bikini hai mảnh Summer Vibe">
                        <div class="product-actions">
                            <button class="btn-wishlist" title="Thêm vào danh sách yêu thích"><i class="far fa-heart"></i></button>
                            <button class="btn-quickview" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span>(42)</span>
                        </div>
                        <h3 class="product-title">Bikini hai mảnh Summer Vibe</h3>
                        <div class="product-price">
                            <span class="current-price">520.000đ</span>
                            <span class="old-price">680.000đ</span>
                            <span class="discount-percent">-24%</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option active" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #ff0000;" title="Đỏ"></span>
                            <span class="color-option" style="background-color: #0000ff;" title="Xanh"></span>
                        </div>
                        <button class="btn-add-to-cart">Thêm vào giỏ hàng</button>
                    </div>
                </div>
            </div>

            <div class="text-center" data-aos="fade-up">
                <a href="#" class="btn-view-all">Xem tất cả sản phẩm <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </section>

    <style>
        /* Product Styles */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .product-rating {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #f39c12;
            font-size: 0.9rem;
        }

        .product-rating span {
            color: #777;
            margin-left: 5px;
        }

        .discount-percent {
            background-color: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .product-actions {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            opacity: 0;
            transform: translateX(20px);
            transition: opacity 0.3s, transform 0.3s;
        }

        .product-card:hover .product-actions {
            opacity: 1;
            transform: translateX(0);
        }

        .btn-wishlist, .btn-quickview {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: background-color 0.3s, transform 0.3s;
        }

        .btn-wishlist:hover, .btn-quickview:hover {
            background-color: #e74c3c;
            color: white;
            transform: scale(1.1);
        }

        .color-option {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 5px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .color-option:hover, .color-option.active {
            transform: scale(1.2);
            box-shadow: 0 0 0 2px #e74c3c;
        }

        .btn-view-all {
            display: inline-block;
            padding: 12px 25px;
            background-color: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            margin-top: 20px;
            transition: background-color 0.3s, transform 0.3s;
        }

        .btn-view-all:hover {
            background-color: #c0392b;
            transform: translateY(-3px);
        }

        .btn-view-all i {
            margin-left: 5px;
            transition: transform 0.3s;
        }

        .btn-view-all:hover i {
            transform: translateX(5px);
        }

        /* Filter Tabs */
        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .filter-tab {
            padding: 8px 20px;
            background: none;
            border: 2px solid #e74c3c;
            border-radius: 30px;
            color: #e74c3c;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s, color 0.3s;
        }

        .filter-tab.active, .filter-tab:hover {
            background-color: #e74c3c;
            color: white;
        }
    </style>

    <!-- Testimonials - Khách Hàng Nói Gì Về Chúng Tôi -->
    <section class="testimonials">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Khách Hàng Nói Gì Về Chúng Tôi</h2>
            <p class="section-subtitle text-center" data-aos="fade-up" data-aos-delay="100">Trải nghiệm và đánh giá từ những khách hàng đã mua sắm tại cửa hàng của chúng tôi</p>

            <div class="testimonials-grid" data-aos="fade-up" data-aos-delay="200">
                <!-- Testimonial 1 -->
                <div class="testimonial-card">
                    <div class="testimonial-quote"><i class="fas fa-quote-left"></i></div>
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"Chất lượng sản phẩm tuyệt vời, chất liệu mềm mại và thoải mái. Thiết kế rất quyến rũ và tinh tế. Tôi rất hài lòng với trải nghiệm mua sắm tại đây."</p>
                    <div class="testimonial-author">
                        <img src="./img/sexy-women/customer1.jpg" alt="Nguyễn Thị A">
                        <div>
                            <h4>Nguyễn Thị A</h4>
                            <p>Khách hàng thân thiết</p>
                        </div>
                    </div>
                    <div class="testimonial-date">15/06/2023</div>
                </div>

                <!-- Testimonial 2 -->
                <div class="testimonial-card">
                    <div class="testimonial-quote"><i class="fas fa-quote-left"></i></div>
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"Đồ sexy ở đây rất đa dạng về mẫu mã và kích cỡ. Tôi đặc biệt thích bộ sưu tập Midnight Elegance, rất sang trọng và quyến rũ. Sẽ tiếp tục ủng hộ shop!"</p>
                    <div class="testimonial-author">
                        <img src="./img/sexy-women/customer2.jpg" alt="Trần Thị B">
                        <div>
                            <h4>Trần Thị B</h4>
                            <p>Khách hàng mới</p>
                        </div>
                    </div>
                    <div class="testimonial-date">22/07/2023</div>
                </div>

                <!-- Testimonial 3 -->
                <div class="testimonial-card">
                    <div class="testimonial-quote"><i class="fas fa-quote-left"></i></div>
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <p class="testimonial-text">"Dịch vụ khách hàng tuyệt vời, giao hàng nhanh và đóng gói kín đáo. Sản phẩm đúng như mô tả và hình ảnh. Tôi rất hài lòng và sẽ quay lại mua sắm nhiều lần nữa."</p>
                    <div class="testimonial-author">
                        <img src="./img/sexy-women/customer3.jpg" alt="Lê Thị C">
                        <div>
                            <h4>Lê Thị C</h4>
                            <p>Khách hàng thân thiết</p>
                        </div>
                    </div>
                    <div class="testimonial-date">05/08/2023</div>
                </div>

                <!-- Testimonial 4 -->
                <div class="testimonial-card">
                    <div class="testimonial-quote"><i class="fas fa-quote-left"></i></div>
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"Tôi đã mua bộ sưu tập Summer Passion và cực kỳ hài lòng. Thiết kế đẹp và phù hợp với vóc dáng của tôi. Chất liệu cao cấp và thoải mái khi mặc. Sẽ tiếp tục ủng hộ shop!"</p>
                    <div class="testimonial-author">
                        <img src="./img/sexy-women/customer2.jpg" alt="Phạm Thu Trang">
                        <div>
                            <h4>Phạm Thu Trang</h4>
                            <p>Khách hàng thường xuyên</p>
                        </div>
                    </div>
                    <div class="testimonial-date">10/08/2023</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter -->
    <section class="newsletter">
        <div class="container">
            <div class="newsletter-content" data-aos="fade-up">
                <h2>Đăng ký nhận thông tin</h2>
                <p>Đăng ký để nhận thông tin về sản phẩm mới, khuyến mãi đặc biệt và các sự kiện độc quyền</p>
                <form class="newsletter-form">
                    <div class="form-group">
                        <input type="email" placeholder="Nhập email của bạn" required>
                        <button type="submit">Đăng ký <i class="fas fa-paper-plane"></i></button>
                    </div>
                    <div class="form-privacy">
                        <input type="checkbox" id="privacy-policy" required>
                        <label for="privacy-policy">Tôi đồng ý với <a href="#">chính sách bảo mật</a> của cửa hàng</label>
                    </div>
                </form>
                <div class="newsletter-socials">
                    <p>Hoặc theo dõi chúng tôi trên:</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-tiktok"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Testimonial Styles */
        .testimonials {
            background-color: #f9f9f9;
            padding: 80px 0;
            margin-top: 50px;
        }

        /* Grid Layout for Testimonials */
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            margin-top: 40px;
        }

        .testimonial-card {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.05);
            position: relative;
            transition: all 0.4s ease;
            height: 100%;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .testimonial-quote {
            position: absolute;
            top: -15px;
            left: 30px;
            width: 40px;
            height: 40px;
            background-color: #e74c3c;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
            z-index: 1;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(to right, #e74c3c, #f39c12);
            border-radius: 15px 15px 0 0;
        }

        .testimonial-rating {
            margin-bottom: 15px;
            color: #f39c12;
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
            color: #555;
            line-height: 1.6;
            min-height: 100px;
            position: relative;
            z-index: 1;
        }

        .testimonial-text::before {
            content: '"';
            position: absolute;
            top: -20px;
            left: -10px;
            font-size: 5rem;
            color: rgba(231, 76, 60, 0.05);
            font-family: serif;
            z-index: -1;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        .testimonial-author img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 3px solid #e74c3c;
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }

        .testimonial-author h4 {
            margin: 0;
            font-size: 1.1rem;
            color: #333;
            font-weight: 600;
        }

        .testimonial-author p {
            margin: 5px 0 0;
            font-size: 0.9rem;
            color: #777;
        }

        .testimonial-date {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 0.8rem;
            color: #999;
            font-style: italic;
        }

        /* Responsive styles for testimonials grid */
        @media (max-width: 1200px) {
            .testimonials-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 992px) {
            .testimonials-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .testimonials-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Newsletter Styles */
        .newsletter {
            background: linear-gradient(135deg, #858585, #c0392b);
            padding: 80px 0;
            color: white;
        }

        .newsletter-content {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }

        .newsletter-content h2 {
            font-size: 2.2rem;
            margin-bottom: 15px;
        }

        .newsletter-content p {
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .newsletter-form {
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            max-width: 500px;
            margin: 0 auto;
        }

        .newsletter-form input[type="email"] {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 5px 0 0 5px;
            font-size: 1rem;
        }

        .newsletter-form button {
            padding: 0 25px;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .newsletter-form button:hover {
            background-color: #222;
        }

        .form-privacy {
            margin-top: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .form-privacy a {
            color: white;
            text-decoration: underline;
        }

        .newsletter-socials {
            margin-top: 30px;
        }

        .newsletter-socials p {
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .social-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s, transform 0.3s;
        }

        .social-icon:hover {
            background-color: white;
            color: #e74c3c;
            transform: translateY(-3px);
        }
    </style>

    <div id="footerbox"></div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/popup.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/lucky-wheel.js"></script>
    <script src="./script/chatbot.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Thêm sự kiện click cho các nút "Thêm vào giỏ hàng"
        document.querySelectorAll('.btn-add-to-cart').forEach(button => {
            button.addEventListener('click', function() {
                // Kiểm tra đăng nhập
                const user = JSON.parse(localStorage.getItem('user'));
                if (!user || !user.isLoggedIn) {
                    // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
                    localStorage.setItem('redirectAfterLogin', window.location.href);

                    // Hiển thị hộp thoại đăng nhập
                    showLoginPrompt();
                    return;
                }

                // Lấy thông tin sản phẩm
                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-title').textContent;
                const productPrice = productCard.querySelector('.current-price').textContent;
                const productImage = productCard.querySelector('.product-image img').src;

                // Thêm sản phẩm vào giỏ hàng
                let cart = JSON.parse(localStorage.getItem('cart')) || [];

                cart.push({
                    id: 'sexy-' + Date.now(),
                    name: productName,
                    price: productPrice,
                    image: productImage,
                    image1: productImage,
                    quantity: 1
                });

                // Lưu giỏ hàng vào localStorage
                localStorage.setItem('cart', JSON.stringify(cart));

                // Cập nhật số lượng giỏ hàng
                updateCartCount();

                // Hiển thị thông báo
                showNotification(`Đã thêm ${productName} vào giỏ hàng`, 'success');
            });
        });

        // Hàm hiển thị hộp thoại đăng nhập
        function showLoginPrompt() {
            // Create login prompt container
            const loginPrompt = document.createElement('div');
            loginPrompt.className = 'login-prompt-overlay';
            loginPrompt.style.position = 'fixed';
            loginPrompt.style.top = '0';
            loginPrompt.style.left = '0';
            loginPrompt.style.width = '100%';
            loginPrompt.style.height = '100%';
            loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loginPrompt.style.display = 'flex';
            loginPrompt.style.alignItems = 'center';
            loginPrompt.style.justifyContent = 'center';
            loginPrompt.style.zIndex = '9999';
            loginPrompt.style.opacity = '0';
            loginPrompt.style.transition = 'opacity 0.3s ease';

            loginPrompt.innerHTML = `
                <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                    <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                        <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                        <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="login-prompt-body" style="padding: 20px;">
                        <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                        <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                            <a href="./pages/login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                            <a href="./pages/register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                        </div>
                    </div>
                </div>
            `;

            // Add to body
            document.body.appendChild(loginPrompt);

            // Show with animation
            setTimeout(() => {
                loginPrompt.style.opacity = '1';
            }, 10);

            // Close button event
            const closeButton = loginPrompt.querySelector('.login-prompt-close');
            closeButton.addEventListener('click', function() {
                loginPrompt.style.opacity = '0';
                setTimeout(() => {
                    loginPrompt.remove();
                }, 300);
            });
        }

        // Hàm cập nhật số lượng giỏ hàng
        function updateCartCount() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const cartCount = document.querySelector('.cart-count');
            if (cartCount) {
                cartCount.textContent = cart.length;
            }
        }

        // Hàm hiển thị thông báo
        function showNotification(message, type = 'info') {
            // Tạo phần tử thông báo
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                               type === 'error' ? '#F44336' :
                                               type === 'warning' ? '#FF9800' : '#2196F3';
            notification.style.color = '#fff';
            notification.style.padding = '15px 20px';
            notification.style.borderRadius = '4px';
            notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '250px';
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(50px)';
            notification.style.transition = 'opacity 0.3s, transform 0.3s';

            // Icon dựa trên loại thông báo
            let icon = '';
            switch (type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
            }

            // Nội dung thông báo
            notification.innerHTML = `${icon} ${message}`;

            // Thêm vào body
            document.body.appendChild(notification);

            // Hiển thị thông báo với animation
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Xóa thông báo sau 3 giây
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(50px)';

                // Xóa khỏi DOM sau khi animation hoàn tất
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Cập nhật số lượng giỏ hàng khi tải trang
        updateCartCount();

        // Initialize Slick Slider
        $(document).ready(function(){
            $('.featured-slider').slick({
                dots: true,
                arrows: true,
                infinite: true,
                speed: 500,
                slidesToShow: 3,
                slidesToScroll: 1,
                autoplay: true,
                autoplaySpeed: 5000,
                responsive: [
                    {
                        breakpoint: 992,
                        settings: {
                            slidesToShow: 2
                        }
                    },
                    {
                        breakpoint: 768,
                        settings: {
                            slidesToShow: 1
                        }
                    }
                ]
            });

            // Testimonials now use grid layout instead of slider
        });
    </script>
</body>
</html>
