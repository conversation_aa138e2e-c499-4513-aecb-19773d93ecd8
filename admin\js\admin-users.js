// Admin Users JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sample user data
    const users = [
        {
            id: '001',
            name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
            email: 'ng<PERSON><PERSON><PERSON>@gmail.com',
            phone: '0912345678',
            role: 'admin',
            registrationDate: '01/01/2024',
            status: 'active',
            avatar: '../img/user1.jpg',
            lastLogin: '15/05/2024 10:30',
            orders: 12,
            totalSpent: '5.2M',
            address: '123 Đường Nguyễn Trãi, Quận 1, TP. <PERSON><PERSON>',
            birthDate: '01/01/1990',
            gender: 'Nam'
        },
        {
            id: '002',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            email: '<EMAIL>',
            phone: '0923456789',
            role: 'customer',
            registrationDate: '15/02/2024',
            status: 'active',
            avatar: '../img/user2.jpg',
            lastLogin: '14/05/2024 15:45',
            orders: 5,
            totalSpent: '2.8M',
            address: '456 <PERSON>, <PERSON>uậ<PERSON> 3, T<PERSON><PERSON> <PERSON>',
            birthDate: '15/05/1992',
            gender: '<PERSON><PERSON>'
        },
        {
            id: '003',
            name: 'Lê Văn C',
            email: '<EMAIL>',
            phone: '0934567890',
            role: 'customer',
            registrationDate: '20/03/2024',
            status: 'blocked',
            avatar: '../img/user3.jpg',
            lastLogin: '10/05/2024 09:15',
            orders: 3,
            totalSpent: '1.5M',
            address: '789 Đường Cách Mạng Tháng 8, Quận 10, TP. Hồ Chí Minh',
            birthDate: '20/08/1988',
            gender: 'Nam'
        },
        {
            id: '004',
            name: 'Phạm Thị D',
            email: '<EMAIL>',
            phone: '0945678901',
            role: 'customer',
            registrationDate: '05/04/2024',
            status: 'active',
            avatar: '../img/user4.jpg',
            lastLogin: '13/05/2024 18:20',
            orders: 7,
            totalSpent: '3.6M',
            address: '101 Đường Võ Văn Tần, Quận 3, TP. Hồ Chí Minh',
            birthDate: '10/12/1995',
            gender: 'Nữ'
        },
        {
            id: '005',
            name: 'Hoàng Văn E',
            email: '<EMAIL>',
            phone: '0956789012',
            role: 'customer',
            registrationDate: '10/05/2024',
            status: 'inactive',
            avatar: '../img/user5.jpg',
            lastLogin: '11/05/2024 11:10',
            orders: 1,
            totalSpent: '0.8M',
            address: '202 Đường Nguyễn Thị Minh Khai, Quận 1, TP. Hồ Chí Minh',
            birthDate: '05/03/1993',
            gender: 'Nam'
        }
    ];

    // View user details
    const viewUserBtns = document.querySelectorAll('.btn-action.view');
    const userDetailModal = document.getElementById('user-detail-modal');
    
    if (viewUserBtns.length > 0 && userDetailModal) {
        viewUserBtns.forEach((btn, index) => {
            btn.addEventListener('click', function() {
                // Get user data
                const user = users[index];
                
                // Update modal with user data
                const userAvatar = userDetailModal.querySelector('.user-profile-avatar');
                const userName = userDetailModal.querySelector('.user-profile-info h2');
                const userRole = userDetailModal.querySelector('.user-profile-info p:nth-child(2) span');
                const userEmail = userDetailModal.querySelector('.user-profile-info p:nth-child(3)');
                const userPhone = userDetailModal.querySelector('.user-profile-info p:nth-child(4)');
                
                // Personal info
                const userBirthDate = userDetailModal.querySelector('.detail-row:nth-child(1) .detail-value');
                const userGender = userDetailModal.querySelector('.detail-row:nth-child(2) .detail-value');
                const userAddress = userDetailModal.querySelector('.detail-row:nth-child(3) .detail-value');
                
                // Account info
                const userId = userDetailModal.querySelector('.detail-row:nth-child(4) .detail-value');
                const userRegDate = userDetailModal.querySelector('.detail-row:nth-child(5) .detail-value');
                const userStatus = userDetailModal.querySelector('.detail-row:nth-child(6) .detail-value');
                const userLastLogin = userDetailModal.querySelector('.detail-row:nth-child(7) .detail-value');
                
                // Set values
                userAvatar.src = user.avatar;
                userName.textContent = user.name;
                userRole.textContent = user.role === 'admin' ? 'Quản trị viên' : 'Khách hàng';
                userRole.className = user.role === 'admin' ? 'badge admin' : 'badge customer';
                userEmail.innerHTML = `<i class="fas fa-envelope"></i> ${user.email}`;
                userPhone.innerHTML = `<i class="fas fa-phone"></i> ${user.phone}`;
                
                userBirthDate.textContent = user.birthDate;
                userGender.textContent = user.gender;
                userAddress.textContent = user.address;
                
                userId.textContent = `#${user.id}`;
                userRegDate.textContent = user.registrationDate;
                userStatus.textContent = user.status === 'active' ? 'Hoạt động' : user.status === 'inactive' ? 'Không hoạt động' : 'Đã khóa';
                userStatus.className = `detail-value status ${user.status}`;
                userLastLogin.textContent = user.lastLogin;
                
                // Show modal
                userDetailModal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            });
        });
    }
    
    // Close modal
    const closeModalBtns = document.querySelectorAll('.btn-close-modal, .btn-close-modal-footer');
    
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
    
    // Edit user
    const editUserBtns = document.querySelectorAll('.btn-action.edit');
    
    editUserBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            const user = users[index];
            showNotification(`Chỉnh sửa thông tin người dùng: ${user.name}`, 'info');
            // Here you would typically open an edit modal or redirect to an edit page
        });
    });
    
    // Delete user
    const deleteUserBtns = document.querySelectorAll('.btn-action.delete');
    
    deleteUserBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            const user = users[index];
            
            if (confirm(`Bạn có chắc chắn muốn xóa người dùng ${user.name}?`)) {
                // Here you would typically send a request to delete the user
                showNotification(`Đã xóa người dùng ${user.name}`, 'success');
            }
        });
    });
    
    // Select all users
    const selectAllCheckbox = document.getElementById('select-all-users');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // Update select all checkbox when individual checkboxes change
        userCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allChecked = Array.from(userCheckboxes).every(cb => cb.checked);
                const someChecked = Array.from(userCheckboxes).some(cb => cb.checked);
                
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            });
        });
    }
    
    // Filter users
    const filterBtn = document.querySelector('.btn-filter');
    const filterMenu = document.querySelector('.filter-menu');
    
    if (filterBtn && filterMenu) {
        filterBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            filterMenu.style.display = filterMenu.style.display === 'block' ? 'none' : 'block';
        });
        
        // Close filter menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.filter-dropdown') && filterMenu.style.display === 'block') {
                filterMenu.style.display = 'none';
            }
        });
    }
    
    // Apply filter
    const applyFilterBtn = document.querySelector('.btn-apply-filter');
    
    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', function() {
            // Get filter values
            const roleFilters = document.querySelectorAll('.filter-group:nth-child(1) input:checked');
            const statusFilters = document.querySelectorAll('.filter-group:nth-child(2) input:checked');
            const dateFrom = document.querySelector('.date-range input:nth-child(1)').value;
            const dateTo = document.querySelector('.date-range input:nth-child(2)').value;
            
            // Apply filters (this would typically call an API or filter data client-side)
            console.log('Applying filters:', {
                roles: Array.from(roleFilters).map(el => el.value),
                statuses: Array.from(statusFilters).map(el => el.value),
                dateFrom,
                dateTo
            });
            
            // Close filter menu
            filterMenu.style.display = 'none';
            
            // Show notification
            showNotification('Đã áp dụng bộ lọc', 'success');
        });
    }
    
    // Reset filter
    const resetFilterBtn = document.querySelector('.btn-reset-filter');
    
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', function() {
            // Reset checkboxes
            document.querySelectorAll('.filter-group input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = checkbox.value === 'all';
            });
            
            // Reset date inputs
            document.querySelectorAll('.date-range input').forEach(input => {
                input.value = '';
            });
            
            // Close filter menu
            filterMenu.style.display = 'none';
            
            // Show notification
            showNotification('Đã đặt lại bộ lọc', 'info');
        });
    }
    
    // Add new user
    const addUserBtn = document.querySelector('.btn-add-new');
    
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            // Show add user form/modal
            showNotification('Chức năng thêm người dùng đang được phát triển', 'info');
        });
    }
    
    // Pagination
    const paginationBtns = document.querySelectorAll('.btn-pagination');
    
    paginationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled && !this.classList.contains('active')) {
                // Update active button
                document.querySelector('.btn-pagination.active').classList.remove('active');
                this.classList.add('active');
                
                // Here you would typically load the corresponding page of users
                showNotification(`Đã chuyển đến trang ${this.textContent}`, 'info');
            }
        });
    });
});
