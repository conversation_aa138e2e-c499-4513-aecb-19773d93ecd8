/* Styles cho trang thẻ thành viên */
.membership-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.membership-header {
    text-align: center;
    margin-bottom: 40px;
}

.membership-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #333;
}

.membership-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

.membership-card {
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    border-radius: 15px;
    padding: 30px;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s;
}

.membership-card:hover {
    transform: translateY(-5px);
}

.membership-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/card-pattern.png');
    opacity: 0.1;
    z-index: 0;
}

.card-content {
    position: relative;
    z-index: 1;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
}

.card-logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.card-level {
    font-size: 1.2rem;
    padding: 5px 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

.card-details {
    margin-bottom: 20px;
}

.card-name {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.card-number {
    font-size: 1.2rem;
    letter-spacing: 2px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-points {
    font-size: 1.2rem;
}

.card-since {
    font-size: 0.9rem;
    opacity: 0.8;
}

.section-title {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.membership-levels {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.level-card {
    flex: 1;
    min-width: 200px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 0 10px 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.level-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.level-card.active {
    border: 2px solid #b21f1f;
}

.level-header {
    text-align: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.level-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #b21f1f;
}

.level-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.level-points {
    font-size: 0.9rem;
    color: #666;
}

.level-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
}

.level-benefits li {
    padding: 5px 0;
    font-size: 0.9rem;
    color: #333;
}

.level-benefits li i {
    color: #4CAF50;
    margin-right: 5px;
}

.membership-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 50px;
}

.info-card {
    flex: 1;
    min-width: 300px;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    margin: 0 10px 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.info-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.info-icon {
    font-size: 1.5rem;
    margin-right: 15px;
    color: #b21f1f;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(178, 31, 31, 0.1);
    border-radius: 50%;
}

.info-title {
    font-size: 1.3rem;
    font-weight: bold;
    margin: 0;
}

.info-content {
    color: #666;
}

.info-content ul {
    padding-left: 20px;
}

.info-content ul li {
    margin-bottom: 10px;
}

.redeem-section {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 50px;
}

.redeem-header {
    text-align: center;
    margin-bottom: 30px;
}

.redeem-header h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #333;
}

.redeem-header p {
    color: #666;
}

.redeem-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.redeem-option {
    width: 200px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.redeem-option:hover {
    transform: translateY(-5px);
}

.redeem-icon {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #b21f1f;
}

.redeem-name {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.redeem-points {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
}

.redeem-btn {
    background-color: #b21f1f;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.redeem-btn:hover {
    background-color: #8c1919;
}

.redeem-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Styles cho các cấp độ thẻ */
.standard-card {
    background: linear-gradient(135deg, #757F9A, #D7DDE8);
}

.silver-card {
    background: linear-gradient(135deg, #BDC3C7, #2C3E50);
}

.gold-card {
    background: linear-gradient(135deg, #FDEB71, #F8D800);
    color: #333;
}

.platinum-card {
    background: linear-gradient(135deg, #C9D6FF, #E2E2E2);
    color: #333;
}

.diamond-card {
    background: linear-gradient(135deg, #43CEA2, #185A9D);
}

/* Responsive */
@media (max-width: 768px) {
    .membership-levels {
        flex-direction: column;
    }
    
    .level-card {
        margin: 0 0 20px;
    }
    
    .membership-info {
        flex-direction: column;
    }
    
    .info-card {
        margin: 0 0 20px;
    }
    
    .redeem-options {
        justify-content: center;
    }
    
    .redeem-option {
        width: 45%;
        margin: 10px 5px;
    }
}

@media (max-width: 480px) {
    .membership-header h1 {
        font-size: 2rem;
    }
    
    .card-header {
        flex-direction: column;
    }
    
    .card-logo {
        margin-bottom: 10px;
    }
    
    .card-footer {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-points {
        margin-bottom: 10px;
    }
    
    .redeem-option {
        width: 100%;
    }
}
