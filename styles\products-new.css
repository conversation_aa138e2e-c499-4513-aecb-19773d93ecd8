/* New Products Page Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --accent-light: #f8d7da;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

/* Hero Section */
.products-hero {
    height: 50vh;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
}

.products-hero-content {
    max-width: 800px;
    padding: 0 20px;
    z-index: 1;
}

.products-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.products-hero-content p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb .separator {
    font-size: 0.8rem;
    opacity: 0.6;
}

.breadcrumb .current {
    font-weight: 500;
}

/* Main Content */
.products-container {
    display: flex;
    padding: 50px 0;
    max-width: 1400px;
    margin: 0 auto;
}

/* Sidebar Filters */
.filters-sidebar {
    width: 280px;
    padding-right: 30px;
}

.filter-section {
    margin-bottom: 30px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    cursor: pointer;
}

.filter-header h3 {
    font-size: 1.1rem;
    color: var(--primary-color);
    font-weight: 600;
    margin: 0;
}

.filter-header i {
    transition: transform 0.3s ease;
}

.filter-header.active i {
    transform: rotate(180deg);
}

.filter-content {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease;
}

.filter-content.active {
    max-height: 500px;
}

.filter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-item {
    margin-bottom: 10px;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.filter-checkbox input {
    margin-right: 10px;
    cursor: pointer;
    width: 18px;
    height: 18px;
}

.filter-checkbox label {
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
}

.filter-checkbox:hover label {
    color: var(--accent-color);
}

.price-range {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.price-slider {
    width: 100%;
    height: 5px;
    background-color: var(--border-color);
    border-radius: 5px;
    position: relative;
}

.price-progress {
    position: absolute;
    height: 100%;
    left: 25%;
    right: 25%;
    background-color: var(--accent-color);
    border-radius: 5px;
}

.range-input {
    position: relative;
}

.range-input input {
    position: absolute;
    top: -5px;
    height: 5px;
    width: 100%;
    background: none;
    pointer-events: none;
    -webkit-appearance: none;
}

.range-input input::-webkit-slider-thumb {
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background: var(--accent-color);
    pointer-events: auto;
    -webkit-appearance: none;
    cursor: pointer;
}

.price-input {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-input .field {
    display: flex;
    align-items: center;
    width: 45%;
}

.price-input .field span {
    margin-right: 5px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.price-input .field input {
    width: 100%;
    padding: 5px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
}

.color-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.color-option {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: transform 0.2s ease;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.filter-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn-filter {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-apply {
    background-color: var(--accent-color);
    color: white;
    flex: 1;
}

.btn-apply:hover {
    background-color: var(--accent-hover);
}

.btn-reset {
    background-color: transparent;
    color: var(--text-light);
    border: 1px solid var(--border-color);
    margin-left: 10px;
}

.btn-reset:hover {
    background-color: var(--hover-color);
}

/* Products Grid */
.products-content {
    flex: 1;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.products-title h2 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin: 0;
}

.products-title p {
    font-size: 0.95rem;
    color: var(--text-light);
    margin: 5px 0 0 0;
}

.products-sorting {
    display: flex;
    align-items: center;
    gap: 15px;
}

.view-options {
    display: flex;
    gap: 10px;
}

.view-option {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-option:hover, .view-option.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.sort-dropdown {
    position: relative;
}

.sort-select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: white;
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
    min-width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sort-select i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.sort-select.active i {
    transform: rotate(180deg);
}

.sort-options {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    margin-top: 5px;
    z-index: 10;
    box-shadow: var(--box-shadow);
    display: none;
}

.sort-options.active {
    display: block;
}

.sort-option {
    padding: 10px 15px;
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.sort-option:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.products-grid.list-view {
    grid-template-columns: 1fr;
}

.product-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
}

.product-badge.new {
    background-color: var(--success-color);
    color: white;
}

.product-badge.sale {
    background-color: var(--accent-color);
    color: white;
}

.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.product-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-action-btn:hover {
    background-color: var(--accent-color);
    color: white;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--text-color);
    height: 2.4em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.current-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-color);
}

.old-price {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.product-colors {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.product-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.product-color:hover {
    transform: scale(1.2);
}

.add-to-cart-btn {
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.add-to-cart-btn:hover {
    background-color: var(--accent-color);
}

/* List View Styles */
.products-grid.list-view .product-card {
    display: flex;
    height: 250px;
}

.products-grid.list-view .product-image {
    width: 250px;
    height: 100%;
}

.products-grid.list-view .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.products-grid.list-view .product-title {
    height: auto;
    -webkit-line-clamp: 1;
}

.products-grid.list-view .product-description {
    display: block;
    margin-bottom: 15px;
    color: var(--text-light);
    font-size: 0.9rem;
}

.products-grid:not(.list-view) .product-description {
    display: none;
}

.products-grid.list-view .add-to-cart-btn {
    width: auto;
    align-self: flex-start;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 50px;
}

.pagination-list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 5px;
}

.pagination-item a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 5px;
    background-color: white;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination-item a:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
}

.pagination-item.active a {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.pagination-item.disabled a {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .products-container {
        flex-direction: column;
    }
    
    .filters-sidebar {
        width: 100%;
        padding-right: 0;
        margin-bottom: 30px;
    }
    
    .filter-content.active {
        max-height: 200px;
        overflow-y: auto;
    }
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .products-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .products-sorting {
        width: 100%;
        justify-content: space-between;
    }
    
    .products-grid.list-view .product-card {
        flex-direction: column;
        height: auto;
    }
    
    .products-grid.list-view .product-image {
        width: 100%;
        height: 300px;
    }
}
