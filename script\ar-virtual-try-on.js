// AR Virtual Try-On JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const arPreview = document.querySelector('.ar-preview');
    const arPreviewImage = document.querySelector('.ar-preview-image');
    const arCameraFeed = document.querySelector('.ar-camera-feed');
    const arLoading = document.querySelector('.ar-loading');
    const startARButton = document.getElementById('start-ar');
    const takePhotoButton = document.getElementById('take-photo');
    const resetButton = document.getElementById('reset-ar');
    const clothingOptions = document.querySelectorAll('.clothing-option');
    const accessoryOptions = document.querySelectorAll('.accessory-option');
    const colorOptions = document.querySelectorAll('.color-option');
    
    // State
    let isARActive = false;
    let selectedClothing = null;
    let selectedAccessory = null;
    let selectedColor = null;
    let stream = null;
    
    // Initialize
    function init() {
        // Set default selections
        if (clothingOptions.length > 0) {
            clothingOptions[0].classList.add('active');
            selectedClothing = clothingOptions[0].getAttribute('data-item');
        }
        
        if (accessoryOptions.length > 0) {
            accessoryOptions[0].classList.add('active');
            selectedAccessory = accessoryOptions[0].getAttribute('data-item');
        }
        
        if (colorOptions.length > 0) {
            colorOptions[0].classList.add('active');
            selectedColor = colorOptions[0].getAttribute('data-color');
        }
        
        // Update preview
        updatePreview();
    }
    
    // Update preview image based on selections
    function updatePreview() {
        if (!isARActive) {
            // Construct image path based on selections
            let imagePath = `./img/ar-try-on/${selectedClothing}`;
            
            if (selectedAccessory) {
                imagePath += `-${selectedAccessory}`;
            }
            
            if (selectedColor) {
                imagePath += `-${selectedColor}`;
            }
            
            imagePath += '.jpg';
            
            // Update preview image
            arPreviewImage.src = imagePath;
            
            // Show notification
            showNotification('Xem trước đã được cập nhật', 'success');
        }
    }
    
    // Start AR experience
    function startAR() {
        if (!isARActive) {
            // Show loading
            arLoading.classList.add('active');
            
            // Request camera access
            navigator.mediaDevices.getUserMedia({ video: { facingMode: 'user' } })
                .then(function(mediaStream) {
                    stream = mediaStream;
                    arCameraFeed.srcObject = mediaStream;
                    arCameraFeed.play();
                    
                    // Hide loading and show camera feed
                    setTimeout(function() {
                        arLoading.classList.remove('active');
                        arPreviewImage.style.display = 'none';
                        arCameraFeed.classList.add('active');
                        isARActive = true;
                        
                        // Update button states
                        startARButton.textContent = 'Dừng AR';
                        takePhotoButton.disabled = false;
                        
                        // Show notification
                        showNotification('Đã bắt đầu trải nghiệm AR', 'success');
                        
                        // Apply virtual clothing overlay
                        applyVirtualClothing();
                    }, 2000);
                })
                .catch(function(error) {
                    console.error('Error accessing camera:', error);
                    arLoading.classList.remove('active');
                    showNotification('Không thể truy cập camera. Vui lòng kiểm tra quyền truy cập.', 'error');
                });
        } else {
            // Stop AR
            stopAR();
        }
    }
    
    // Stop AR experience
    function stopAR() {
        if (isARActive) {
            // Stop camera stream
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            
            // Hide camera feed and show preview image
            arCameraFeed.classList.remove('active');
            arPreviewImage.style.display = 'block';
            isARActive = false;
            
            // Update button states
            startARButton.textContent = 'Bắt đầu AR';
            takePhotoButton.disabled = true;
            
            // Show notification
            showNotification('Đã dừng trải nghiệm AR', 'info');
        }
    }
    
    // Take photo
    function takePhoto() {
        if (isARActive) {
            // Create canvas to capture frame
            const canvas = document.createElement('canvas');
            canvas.width = arCameraFeed.videoWidth;
            canvas.height = arCameraFeed.videoHeight;
            const ctx = canvas.getContext('2d');
            
            // Draw video frame to canvas
            ctx.drawImage(arCameraFeed, 0, 0, canvas.width, canvas.height);
            
            // Apply virtual clothing to canvas
            applyVirtualClothingToCanvas(ctx, canvas.width, canvas.height);
            
            // Convert canvas to image data URL
            const imageDataURL = canvas.toDataURL('image/png');
            
            // Create download link
            const downloadLink = document.createElement('a');
            downloadLink.href = imageDataURL;
            downloadLink.download = 'virtual-try-on.png';
            downloadLink.click();
            
            // Show notification
            showNotification('Ảnh đã được lưu', 'success');
        }
    }
    
    // Reset selections
    function resetAR() {
        // Stop AR if active
        if (isARActive) {
            stopAR();
        }
        
        // Reset selections
        clothingOptions.forEach(option => option.classList.remove('active'));
        accessoryOptions.forEach(option => option.classList.remove('active'));
        colorOptions.forEach(option => option.classList.remove('active'));
        
        // Set default selections
        if (clothingOptions.length > 0) {
            clothingOptions[0].classList.add('active');
            selectedClothing = clothingOptions[0].getAttribute('data-item');
        }
        
        if (accessoryOptions.length > 0) {
            accessoryOptions[0].classList.add('active');
            selectedAccessory = accessoryOptions[0].getAttribute('data-item');
        }
        
        if (colorOptions.length > 0) {
            colorOptions[0].classList.add('active');
            selectedColor = colorOptions[0].getAttribute('data-color');
        }
        
        // Update preview
        updatePreview();
        
        // Show notification
        showNotification('Đã đặt lại tùy chọn', 'info');
    }
    
    // Apply virtual clothing overlay (placeholder for AR implementation)
    function applyVirtualClothing() {
        // This would be replaced with actual AR implementation
        console.log('Applying virtual clothing:', {
            clothing: selectedClothing,
            accessory: selectedAccessory,
            color: selectedColor
        });
        
        // For demo purposes, we'll just add a simple overlay
        const overlay = document.createElement('div');
        overlay.className = 'ar-clothing-overlay';
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundImage = `url('./img/ar-try-on/overlay-${selectedClothing}.png')`;
        overlay.style.backgroundSize = 'contain';
        overlay.style.backgroundPosition = 'center';
        overlay.style.backgroundRepeat = 'no-repeat';
        overlay.style.pointerEvents = 'none';
        
        // Remove any existing overlay
        const existingOverlay = arPreview.querySelector('.ar-clothing-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
        
        // Add new overlay
        arPreview.appendChild(overlay);
    }
    
    // Apply virtual clothing to canvas (placeholder for AR implementation)
    function applyVirtualClothingToCanvas(ctx, width, height) {
        // This would be replaced with actual AR implementation
        // For demo purposes, we'll just draw a simple overlay
        const overlay = new Image();
        overlay.src = `./img/ar-try-on/overlay-${selectedClothing}.png`;
        
        // Draw overlay on canvas
        ctx.drawImage(overlay, 0, 0, width, height);
    }
    
    // Event listeners
    if (startARButton) {
        startARButton.addEventListener('click', startAR);
    }
    
    if (takePhotoButton) {
        takePhotoButton.addEventListener('click', takePhoto);
    }
    
    if (resetButton) {
        resetButton.addEventListener('click', resetAR);
    }
    
    // Clothing options
    clothingOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            clothingOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Update selected clothing
            selectedClothing = this.getAttribute('data-item');
            
            // Update preview
            updatePreview();
            
            // Update AR if active
            if (isARActive) {
                applyVirtualClothing();
            }
        });
    });
    
    // Accessory options
    accessoryOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            accessoryOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Update selected accessory
            selectedAccessory = this.getAttribute('data-item');
            
            // Update preview
            updatePreview();
            
            // Update AR if active
            if (isARActive) {
                applyVirtualClothing();
            }
        });
    });
    
    // Color options
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            colorOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Update selected color
            selectedColor = this.getAttribute('data-color');
            
            // Update preview
            updatePreview();
            
            // Update AR if active
            if (isARActive) {
                applyVirtualClothing();
            }
        });
    });
    
    // Initialize
    init();
});
