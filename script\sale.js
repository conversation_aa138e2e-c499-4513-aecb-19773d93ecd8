// Sale Page Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
    });
    
    // Update cart count
    updateCartCount();
    
    // Initialize countdown timer
    initCountdown();
    
    // Load sale products
    loadSaleProducts();
    
    // Handle filters
    document.getElementById('category-filter').addEventListener('change', filterProducts);
    document.getElementById('discount-filter').addEventListener('change', filterProducts);
    document.getElementById('sort-filter').addEventListener('change', filterProducts);
    
    // Handle load more button
    document.getElementById('load-more').addEventListener('click', loadMoreProducts);
    
    // Handle newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            if (email) {
                showNotification('Cảm ơn bạn đã đăng ký nhận thông tin khuyến mãi!', 'success');
                this.reset();
            }
        });
    }
});

// Initialize countdown timer
function initCountdown() {
    // Set the date we're counting down to (7 days from now)
    const countdownDate = new Date();
    countdownDate.setDate(countdownDate.getDate() + 7);
    
    // Update the countdown every 1 second
    const countdownTimer = setInterval(function() {
        // Get current date and time
        const now = new Date().getTime();
        
        // Find the distance between now and the countdown date
        const distance = countdownDate - now;
        
        // Time calculations for days, hours, minutes and seconds
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        // Display the result
        document.getElementById('days').textContent = formatTime(days);
        document.getElementById('hours').textContent = formatTime(hours);
        document.getElementById('minutes').textContent = formatTime(minutes);
        document.getElementById('seconds').textContent = formatTime(seconds);
        
        // If the countdown is finished, clear the interval
        if (distance < 0) {
            clearInterval(countdownTimer);
            document.getElementById('days').textContent = '00';
            document.getElementById('hours').textContent = '00';
            document.getElementById('minutes').textContent = '00';
            document.getElementById('seconds').textContent = '00';
        }
    }, 1000);
}

// Format time to always have 2 digits
function formatTime(time) {
    return time < 10 ? `0${time}` : time;
}

// Sample sale products data
const saleProducts = [
    {
        id: 1,
        name: 'Đầm Ôm Mini Cổ Chữ V Khoét Sâu Đính Đá Phối Tua Rua Có Khóa Cài Dành Cho Bạn Nữ',
        originalPrice: '450.000đ',
        salePrice: '270.000đ',
        discount: 40,
        image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7qvfr-lep6k525bmg8d4@resize_w450_nl.webp',
        category: 'women',
        isNew: true,
        availableSizes: ['S', 'M', 'L', 'XL'],
        availableColors: [ 'black']
    },
    {
        id: 2,
        name: 'Quần JEAN xuông NAD chất liệu cotton co giãn mặc thoải mái,quần bò nam nữ phom Ống Rộng ',
        originalPrice: '550.000đ',
        salePrice: '185.000đ',
        discount: 30,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lykdaghq7ee92d@resize_w450_nl.webp',
        category: 'men',
        isNew: false,
        availableSizes: ['29', '30', '31', '32', '33'],
        availableColors: ['grey', 'black']
    },
    {
        id: 3,
        name: 'Váy Ngủ 2 Dây Chất Liệu Lụa Satin Cao Cấp ',
        originalPrice: '150.000đ',
        salePrice: '85.000đ',
        discount: 50,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lrqukfxo86v834@resize_w450_nl.webp',
        category: 'women',
        isNew: true,
        availableSizes: ['S', 'M', 'L'],
        availableColors: ['red', 'black', 'pink']
    },
    {
        id: 4,
        name: 'Áo polo nam cao cấp',
        originalPrice: '350.000đ',
        salePrice: '110.000đ',
        discount: 40,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m48d0epmvcgf11.webp',
        category: 'men',
        isNew: true,
        availableSizes: ['M', 'L', 'XL', 'XXL'],
        availableColors: [ 'black']
    },
    {
        id: 5,
        name: 'Áo khoác ren croptop tay loe B1 đen/trắng',
        originalPrice: '150.000đ',
        salePrice: '125.000đ',
        discount: 30,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lz8riji906n5b5@resize_w450_nl.webp',
        category: 'women',
        isNew: false,
        availableSizes: ['S', 'M', 'L'],
        availableColors: ['white', 'black']
    },
    {
        id: 6,
        name: 'Quần Kaki Jean - Doki cao cấp co giãn 4 chiều, quần kaki nam màu ghi nhạt trơn dáng ôm chất vải dầy dặn mềm mịn',
        originalPrice: '450.000đ',
        salePrice: '225.000đ',
        discount: 50,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-ljyg1qzwzxys64@resize_w450_nl.webp',
        category: 'men',
        isNew: false,
        availableSizes: ['29', '30', '31', '32', '33'],
        availableColors: ['beige', 'black', 'navy']
    },
    {
        id: 7,
        name: 'Váy xếp ly màu trơn thời trang dành cho nữ',
        originalPrice: '150.000đ',
        salePrice: '90.000đ',
        discount: 40,
        image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m8wrt52z8naqaf.webp',
        category: 'women',
        isNew: true,
        availableSizes: ['S', 'M', 'L'],
        availableColors: ['white', 'black', 'pink']
    },
    {
        id: 8,
        name: 'Áo Sơ Mi Dekace Premium Oxford Shirt Thêu Logo D',
        originalPrice: '450.000đ',
        salePrice: '315.000đ',
        discount: 30,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-lh5rt3ss2tyqc2@resize_w450_nl.webp',
        category: 'men',
        isNew: true,
        availableSizes: ['M', 'L', 'XL'],
        availableColors: ['blue', 'white']
    },
    {
        id: 9,
        name: 'Đầm Công Chúa Bạch Tuyết của Tutupetti',
        originalPrice: '350.000đ',
        salePrice: '175.000đ',
        discount: 50,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-li87fcym5h3md3@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7qukw-li87fcym5h3md3@resize_w450_nl.webp',
        category: 'kids',
        isNew: true,
        availableSizes: ['5-4Y', '5-6Y', '7-15Y'],
        availableColors: ['pink', 'white', 'blue']
    },
    {
        id: 10,
        name: ' Áo thun cho bé hình BUBBLE',
        originalPrice: '250.000đ',
        salePrice: '150.000đ',
        discount: 40,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134201-7r98o-lueq08eiqzoccf@resize_w450_nl.webp',
        category: 'kids',
        isNew: false,
        availableSizes: ['3-4Y', '5-6Y', '7-8Y', '9-10Y'],
        availableColors: ['blue', 'red', 'green']
    },
    {
        id: 11,
        name: 'Quần Legging Thời Trang Ren Đơn Giản Satin Nữ Quần An Toàn Bộ Đồ Ngủ Quần Short',
        originalPrice: '50.000đ',
        salePrice: '25.000đ',
        discount: 50,
        image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rd72-lv17vz3agnrsf3@resize_w450_nl.webp',
        category: 'women',
        isNew: false,
        availableSizes: ['S', 'M', 'L'],
        availableColors: ['pink', 'black','white']
    },
    {
        id: 12,
        name: 'Áo khoác dạ PEALO form boxy cao cấp',
        originalPrice: '750.000đ',
        salePrice: '525.000đ',
        discount: 30,
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m4k3i5rk1wi7b9@resize_w450_nl.webp',
        category: 'men',
        isNew: true,
        availableSizes: ['M', 'L', 'XL'],
        availableColors: ['black', 'navy', 'gray']
    }
];

// Current state
let currentProducts = [...saleProducts];
let visibleProducts = 8;

// Load sale products
function loadSaleProducts() {
    const productsContainer = document.getElementById('products-container');
    
    // Clear container
    productsContainer.innerHTML = '';
    
    // Add products
    for (let i = 0; i < Math.min(visibleProducts, currentProducts.length); i++) {
        const product = currentProducts[i];
        
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        
        productCard.innerHTML = `
            <div class="product-image">
                <span class="sale-badge">-${product.discount}%</span>
                <img src="${product.image}" alt="${product.name}">
                <div class="product-overlay">
                    <button class="btn-quick-view" data-id="${product.id}">Xem nhanh</button>
                    <button class="btn-add-to-cart" data-id="${product.id}">Thêm vào giỏ</button>
                </div>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <div class="product-price">
                    <span class="original-price">${product.originalPrice}</span>
                    <span class="sale-price">${product.salePrice}</span>
                </div>
                <div class="product-colors">
                    ${renderColorOptions(product.availableColors)}
                </div>
            </div>
        `;
        
        productsContainer.appendChild(productCard);
    }
    
    // Hide load more button if all products are visible
    const loadMoreButton = document.getElementById('load-more');
    if (visibleProducts >= currentProducts.length) {
        loadMoreButton.style.display = 'none';
    } else {
        loadMoreButton.style.display = 'block';
    }
    
    // Add event listeners to buttons
    addEventListenersToButtons();
}

// Render color options
function renderColorOptions(colors) {
    if (!colors || !Array.isArray(colors) || colors.length === 0) {
        return '';
    }
    
    return colors.map(color => {
        return `<span class="color-option ${color}" data-color="${color}"></span>`;
    }).join('');
}

// Add event listeners to buttons
function addEventListenersToButtons() {
    // Quick view buttons
    const quickViewButtons = document.querySelectorAll('.btn-quick-view');
    quickViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.id);
            showQuickView(productId);
        });
    });
    
    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.btn-add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.id);
            addToCart(productId);
        });
    });
    
    // Color options
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all color options in the same product
            const productCard = this.closest('.product-card');
            const colorOptions = productCard.querySelectorAll('.color-option');
            colorOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked color option
            this.classList.add('active');
        });
    });
}

// Show quick view
function showQuickView(productId) {
    const product = saleProducts.find(p => p.id === productId);
    if (!product) return;
    
    // In a real application, this would open a modal with product details
    // For this demo, we'll just show a notification
    showNotification(`Xem nhanh: ${product.name}`, 'info');
}

// Add to cart
function addToCart(productId) {
    const product = saleProducts.find(p => p.id === productId);
    if (!product) return;
    
    // Get selected color (if any)
    const productCard = document.querySelector(`.btn-add-to-cart[data-id="${productId}"]`).closest('.product-card');
    const activeColor = productCard.querySelector('.color-option.active');
    const color = activeColor ? activeColor.dataset.color : product.availableColors[0];
    
    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Add product to cart
    cart.push({
        id: product.id,
        name: product.name,
        price: product.salePrice,
        image: product.image,
        color: color,
        size: product.availableSizes[0], // Default to first size
        quantity: 1
    });
    
    // Save cart to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart count
    updateCartCount();
    
    // Show notification
    showNotification('Đã thêm sản phẩm vào giỏ hàng', 'success');
}

// Filter products
function filterProducts() {
    const categoryFilter = document.getElementById('category-filter').value;
    const discountFilter = parseInt(document.getElementById('discount-filter').value);
    const sortFilter = document.getElementById('sort-filter').value;
    
    // Filter by category and discount
    let filteredProducts = saleProducts.filter(product => {
        // Category filter
        if (categoryFilter !== 'all' && product.category !== categoryFilter) {
            return false;
        }
        
        // Discount filter
        if (!isNaN(discountFilter) && product.discount < discountFilter) {
            return false;
        }
        
        return true;
    });
    
    // Sort products
    filteredProducts = sortProducts(filteredProducts, sortFilter);
    
    // Update current products
    currentProducts = filteredProducts;
    visibleProducts = 8;
    
    // Reload products
    loadSaleProducts();
}

// Sort products
function sortProducts(products, sortBy) {
    const sortedProducts = [...products];
    
    switch (sortBy) {
        case 'discount-desc':
            sortedProducts.sort((a, b) => b.discount - a.discount);
            break;
        case 'price-asc':
            sortedProducts.sort((a, b) => {
                const priceA = parseInt(a.salePrice.replace(/\D/g, ''));
                const priceB = parseInt(b.salePrice.replace(/\D/g, ''));
                return priceA - priceB;
            });
            break;
        case 'price-desc':
            sortedProducts.sort((a, b) => {
                const priceA = parseInt(a.salePrice.replace(/\D/g, ''));
                const priceB = parseInt(b.salePrice.replace(/\D/g, ''));
                return priceB - priceA;
            });
            break;
        case 'newest':
            sortedProducts.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0));
            break;
    }
    
    return sortedProducts;
}

// Load more products
function loadMoreProducts() {
    visibleProducts += 4;
    loadSaleProducts();
}

// Update cart count
function updateCartCount() {
    const cartCountElement = document.querySelector('.cart-count');
    if (!cartCountElement) return;
    
    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Update cart count
    cartCountElement.textContent = cart.length;
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }
    
    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;
    
    // Add notification to container
    container.appendChild(notification);
    
    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
