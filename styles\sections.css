/* CSS cho các phần nội dung */
@import url('style.css');

/* Categories Section */
.categories {
    background-color: var(--secondary-color);
    padding: var(--spacing-xxl) 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.category-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    height: 400px;
    box-shadow: var(--box-shadow);
    transition: transform var(--transition-medium);
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: var(--spacing-lg);
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: var(--light-text);
    text-align: center;
    transition: padding var(--transition-medium);
}

.category-card:hover .category-content {
    padding-bottom: calc(var(--spacing-lg) + 10px);
}

.category-content h3 {
    margin-bottom: var(--spacing-md);
    font-size: 1.6rem;
    font-family: var(--heading-font);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.category-content .btn-shop {
    color: var(--light-text);
    border: 1px solid var(--light-text);
    padding: 8px 20px;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    display: inline-block;
}

.category-content .btn-shop:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--light-text);
}

/* Featured Products Section */
.featured-products {
    padding: var(--spacing-xxl) 0;
}

/* Trending Section */
.trending {
    padding: var(--spacing-xxl) 0;
    background-color: var(--secondary-color);
}

.trending-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto auto;
    gap: var(--spacing-lg);
}

.trending-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    height: 300px;
    transition: transform var(--transition-medium);
}

.trending-item:hover {
    transform: translateY(-5px);
}

.trending-item.large {
    grid-column: span 2;
    height: 400px;
}

.trending-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.trending-item:hover img {
    transform: scale(1.05);
}

.trending-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: var(--spacing-lg);
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: var(--light-text);
    transition: padding var(--transition-medium);
}

.trending-item:hover .trending-content {
    padding-bottom: calc(var(--spacing-lg) + 10px);
}

.trending-content h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 1.5rem;
    font-family: var(--heading-font);
}

.trending-content p {
    margin-bottom: var(--spacing-md);
    font-size: 0.95rem;
    opacity: 0.9;
    max-width: 80%;
}

.btn-read {
    color: var(--light-text);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 5px;
    display: inline-block;
}

.btn-read::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform var(--transition-medium);
}

.btn-read:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Newsletter Section */
.newsletter {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: var(--spacing-xxl) 0;
    position: relative;
    overflow: hidden;
}

.newsletter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../assets/pattern.png');
    opacity: 0.05;
    z-index: 1;
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.newsletter-content h2 {
    font-family: var(--heading-font);
    margin-bottom: var(--spacing-sm);
    font-size: 2.2rem;
    color: var(--light-text);
}

.newsletter-content p {
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
    font-size: 1.1rem;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem 1.2rem;
    border: none;
    font-family: var(--body-font);
    font-size: 0.95rem;
}

.newsletter-form input:focus {
    outline: none;
}

.btn-subscribe {
    padding: 0 1.8rem;
    background-color: var(--accent-color);
    color: var(--light-text);
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.btn-subscribe:hover {
    background-color: var(--accent-hover);
}

/* Responsive */
@media (max-width: 992px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .trending-grid {
        grid-template-columns: 1fr;
    }
    
    .trending-item.large {
        grid-column: span 1;
    }
    
    .category-card, .trending-item {
        height: 350px;
    }
    
    .trending-item.large {
        height: 350px;
    }
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .newsletter-form input {
        width: 100%;
        border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    }
    
    .btn-subscribe {
        width: 100%;
        padding: 1rem;
        border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
    }
    
    .category-card, .trending-item, .trending-item.large {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .category-card, .trending-item, .trending-item.large {
        height: 250px;
    }
    
    .category-content h3, .trending-content h3 {
        font-size: 1.3rem;
    }
    
    .trending-content p {
        font-size: 0.85rem;
        max-width: 100%;
    }
    
    .newsletter-content h2 {
        font-size: 1.8rem;
    }
    
    .newsletter-content p {
        font-size: 0.95rem;
    }
}
