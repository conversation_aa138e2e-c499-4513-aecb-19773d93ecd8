// Auth Scripts (Login & Register)

document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle eye icon
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    });

    // Username suggestion based on first name and last name
    const firstNameInput = document.getElementById('first-name');
    const lastNameInput = document.getElementById('last-name');
    const usernameInput = document.getElementById('username');

    if (firstNameInput && lastNameInput && usernameInput) {
        const updateUsernameSuggestion = function() {
            const firstName = firstNameInput.value.trim().toLowerCase();
            const lastName = lastNameInput.value.trim().toLowerCase();

            if (firstName && lastName) {
                // Create username suggestions
                const suggestion1 = `${firstName}.${lastName}`;
                const suggestion2 = `${firstName}${lastName}`;
                const suggestion3 = `${firstName}_${lastName}`;
                const randomNum = Math.floor(Math.random() * 1000);
                const suggestion4 = `${firstName}${lastName}${randomNum}`;

                // Update suggestion text
                const suggestionText = document.querySelector('.suggestion-text');
                if (suggestionText) {
                    suggestionText.innerHTML = `Gợi ý: <strong>${suggestion1}</strong>, <strong>${suggestion2}</strong>, <strong>${suggestion3}</strong>, <strong>${suggestion4}</strong>`;
                    suggestionText.style.cursor = 'pointer';

                    // Make suggestions clickable
                    const suggestions = suggestionText.querySelectorAll('strong');
                    suggestions.forEach(suggestion => {
                        suggestion.addEventListener('click', function() {
                            usernameInput.value = this.textContent;
                        });
                    });
                }
            }
        };

        firstNameInput.addEventListener('input', updateUsernameSuggestion);
        lastNameInput.addEventListener('input', updateUsernameSuggestion);
    }

    // Password suggestion button
    const suggestPasswordBtn = document.getElementById('suggest-password');
    const passwordInput = document.getElementById('password');

    if (suggestPasswordBtn && passwordInput) {
        suggestPasswordBtn.addEventListener('click', function() {
            const generatedPassword = generateStrongPassword();
            passwordInput.value = generatedPassword;
            passwordInput.type = 'text';

            // Update strength meter
            const strength = calculatePasswordStrength(generatedPassword);
            updatePasswordStrengthMeter(strength);

            // If there's a confirm password field, update it too
            const confirmPasswordInput = document.getElementById('confirm-password');
            if (confirmPasswordInput) {
                confirmPasswordInput.value = generatedPassword;
                confirmPasswordInput.type = 'text';
            }

            // Show notification
            showNotification('Đã tạo mật khẩu mạnh!', 'success');

            // Change eye icon
            const togglePasswordIcons = document.querySelectorAll('.toggle-password i');
            togglePasswordIcons.forEach(icon => {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            });

            // Set timeout to hide password after 5 seconds
            setTimeout(() => {
                passwordInput.type = 'password';
                if (confirmPasswordInput) {
                    confirmPasswordInput.type = 'password';
                }

                // Change eye icon back
                togglePasswordIcons.forEach(icon => {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                });
            }, 5000);
        });
    }

    // Password strength meter (for register page)
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            updatePasswordStrengthMeter(strength);
        });
    }

    // Login form submission
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            // Simple validation
            if (!email || !password) {
                showNotification('Vui lòng nhập đầy đủ thông tin', 'error');
                return;
            }

            // Simulate login (in a real app, this would be an API call)
            simulateLogin(email, password, remember);
        });
    }

    // Register form submission
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const firstName = document.getElementById('first-name').value;
            const lastName = document.getElementById('last-name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const terms = document.getElementById('terms').checked;

            // Simple validation
            if (!firstName || !lastName || !email || !phone || !password || !confirmPassword) {
                showNotification('Vui lòng nhập đầy đủ thông tin', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showNotification('Mật khẩu xác nhận không khớp', 'error');
                return;
            }

            if (!terms) {
                showNotification('Vui lòng đồng ý với điều khoản dịch vụ', 'error');
                return;
            }

            // Simulate registration (in a real app, this would be an API call)
            simulateRegistration(firstName, lastName, email, phone, password);
        });
    }

    // Social login buttons
    const socialButtons = document.querySelectorAll('.social-btn');
    socialButtons.forEach(button => {
        button.addEventListener('click', function() {
            const provider = this.classList.contains('facebook') ? 'Facebook' : 'Google';

            // Show loading notification
            showNotification(`Đang kết nối với ${provider}...`, 'info');

            // Simulate API call delay
            setTimeout(() => {
                // For demo purposes, social login always succeeds
                const user = {
                    name: `Người dùng ${provider}`,
                    email: `user@${provider.toLowerCase()}.com`,
                    isLoggedIn: true,
                    socialProvider: provider
                };

                // Save user to localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Show success notification
                showNotification(`Đăng nhập thành công qua ${provider}!`, 'success');

                // Redirect to account page
                setTimeout(() => {
                    window.location.href = 'account.html';
                }, 1000);
            }, 1500);
        });
    });
});

// Generate strong password
function generateStrongPassword() {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const symbols = '!@#$%^&*()_+[]{}|;:,.<>?';

    const allChars = lowercase + uppercase + numbers + symbols;
    let password = '';

    // Ensure at least one of each character type
    password += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
    password += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
    password += numbers.charAt(Math.floor(Math.random() * numbers.length));
    password += symbols.charAt(Math.floor(Math.random() * symbols.length));

    // Fill the rest with random characters
    for (let i = 0; i < 8; i++) {
        password += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

    // Shuffle the password
    return password.split('').sort(() => 0.5 - Math.random()).join('');
}

// Calculate password strength (0-4)
function calculatePasswordStrength(password) {
    let strength = 0;

    // Length check
    if (password.length >= 8) {
        strength += 1;
    }

    // Contains lowercase letters
    if (/[a-z]/.test(password)) {
        strength += 1;
    }

    // Contains uppercase letters
    if (/[A-Z]/.test(password)) {
        strength += 1;
    }

    // Contains numbers
    if (/[0-9]/.test(password)) {
        strength += 1;
    }

    // Contains special characters
    if (/[^a-zA-Z0-9]/.test(password)) {
        strength += 1;
    }

    return Math.min(4, strength);
}

// Update password strength meter
function updatePasswordStrengthMeter(strength) {
    const strengthMeterFill = document.querySelector('.strength-meter-fill');
    const strengthText = document.querySelector('.strength-text');

    if (!strengthMeterFill || !strengthText) return;

    strengthMeterFill.setAttribute('data-strength', strength);

    // Update strength text
    switch (strength) {
        case 0:
            strengthText.textContent = 'Mật khẩu yếu';
            break;
        case 1:
            strengthText.textContent = 'Mật khẩu yếu';
            break;
        case 2:
            strengthText.textContent = 'Mật khẩu trung bình';
            break;
        case 3:
            strengthText.textContent = 'Mật khẩu mạnh';
            break;
        case 4:
            strengthText.textContent = 'Mật khẩu rất mạnh';
            break;
    }
}

// Save activity to localStorage
function saveActivity(activity) {
    let activities = JSON.parse(localStorage.getItem('userActivities')) || [];
    activities.unshift(activity); // Add to beginning of array

    // Keep only the last 10 activities
    if (activities.length > 10) {
        activities = activities.slice(0, 10);
    }

    localStorage.setItem('userActivities', JSON.stringify(activities));
}

// Simulate login (in a real app, this would be an API call)
function simulateLogin(email, password, remember) {
    // Show loading state
    showNotification('Đang đăng nhập...', 'info');

    // Simulate API call delay
    setTimeout(() => {
        // For demo purposes, any email/password combination works
        const user = {
            email: email,
            name: email.split('@')[0],
            isLoggedIn: true,
            lastLogin: new Date().toISOString()
        };

        // Save user to localStorage
        localStorage.setItem('user', JSON.stringify(user));

        // Save login activity
        const activity = {
            type: 'login',
            title: 'Đăng nhập thành công',
            time: new Date().toISOString(),
            device: navigator.userAgent
        };
        saveActivity(activity);

        // Show success notification
        showNotification('Đăng nhập thành công!', 'success');

        // Check if there's a redirect URL
        const redirectUrl = localStorage.getItem('redirectAfterLogin');

        // Redirect to appropriate page
        setTimeout(() => {
            if (redirectUrl) {
                localStorage.removeItem('redirectAfterLogin'); // Clear the redirect URL
                window.location.href = redirectUrl;
            } else {
                window.location.href = '../index.html'; // Default redirect
            }
        }, 1000);
    }, 1500);
}

// Simulate registration (in a real app, this would be an API call)
function simulateRegistration(firstName, lastName, email, phone, password) {
    // Show loading state
    showNotification('Đang đăng ký...', 'info');

    // Simulate API call delay
    setTimeout(() => {
        // For demo purposes, registration always succeeds
        const user = {
            firstName: firstName,
            lastName: lastName,
            email: email,
            phone: phone,
            name: `${firstName} ${lastName}`,
            password: password, // In a real app, this would be hashed
            isLoggedIn: true,
            registrationDate: new Date().toISOString()
        };

        // Save user to localStorage
        localStorage.setItem('user', JSON.stringify(user));

        // Save registration activity
        const activity = {
            type: 'register',
            title: 'Đăng ký tài khoản mới',
            time: new Date().toISOString(),
            device: navigator.userAgent
        };
        saveActivity(activity);

        // Show success notification
        showNotification('Đăng ký thành công!', 'success');

        // Check if there's a redirect URL
        const redirectUrl = localStorage.getItem('redirectAfterLogin');

        // Redirect to appropriate page
        setTimeout(() => {
            if (redirectUrl) {
                localStorage.removeItem('redirectAfterLogin'); // Clear the redirect URL
                window.location.href = redirectUrl;
            } else {
                window.location.href = './pages/login.html'; // Default redirect to login page
            }
        }, 1000);
    }, 1500);
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}


