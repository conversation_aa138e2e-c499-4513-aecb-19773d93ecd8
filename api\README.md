# Fashion Store API

API server for Fashion Store e-commerce website.

## Setup

1. Install dependencies:
```
npm install
```

2. Start the server:
```
npm start
```

For development with auto-restart:
```
npm run dev
```

## API Endpoints

### Products

- `GET /api/products` - Get all products
  - Query parameters:
    - `category` - Filter by category (e.g., women, men, underwear)
    - `subcategory` - Filter by subcategory (e.g., shirt, pants, dress)
    - `search` - Search by name
    - `sort` - Sort by (price-asc, price-desc, newest, bestseller, rating)
    - `limit` - Limit number of results

- `GET /api/products/:id` - Get product by ID
- `GET /api/products/:id/related` - Get related products

### Users

- `POST /api/users/login` - Login
  - Body: `{ username, password }`

- `POST /api/users/register` - Register
  - Body: `{ username, email, password, fullName, phone }`

- `GET /api/users/profile` - Get user profile (requires authentication)
- `PUT /api/users/profile` - Update user profile (requires authentication)
  - Body: `{ fullName, phone, address, gender, birthDate }`

### Cart

- `GET /api/cart` - Get cart (requires authentication)
- `POST /api/cart/add` - Add item to cart (requires authentication)
  - Body: `{ productId, quantity, color, size }`

- `PUT /api/cart/update/:itemId` - Update cart item (requires authentication)
  - Body: `{ quantity, color, size }`

- `DELETE /api/cart/remove/:itemId` - Remove item from cart (requires authentication)
- `DELETE /api/cart/clear` - Clear cart (requires authentication)

### Wishlist

- `GET /api/wishlist` - Get wishlist (requires authentication)
- `POST /api/wishlist/add` - Add item to wishlist (requires authentication)
  - Body: `{ productId }`

- `POST /api/wishlist/toggle` - Toggle item in wishlist (requires authentication)
  - Body: `{ productId }`

- `DELETE /api/wishlist/remove/:itemId` - Remove item from wishlist (requires authentication)
- `DELETE /api/wishlist/clear` - Clear wishlist (requires authentication)

## Authentication

Most endpoints require authentication. To authenticate, include the JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

You can get a token by logging in or registering.
