/**
 * User Display Script
 * This script handles displaying the username instead of the user icon when logged in
 * It works across all pages of the website
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check login status and display username
    checkLoginAndDisplayUsername();

    // Listen for storage events (when localStorage changes in other tabs)
    window.addEventListener('storage', function(e) {
        if (e.key === 'user' || e.key === 'isLoggedIn' || e.key === 'username') {
            checkLoginAndDisplayUsername();
        }
    });
});

/**
 * Check if user is logged in and display username
 */
function checkLoginAndDisplayUsername() {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    const username = localStorage.getItem('username');
    const user = JSON.parse(localStorage.getItem('user'));

    // Get the account icon
    const accountIcon = document.querySelector('.nav-icon[title="Tài kho<PERSON>n"]');

    if (isLoggedIn && username && accountIcon) {
        // Replace icon with username
        accountIcon.innerHTML = `<i class="fas fa-user-circle"></i> <span class="username-display">${username}</span>`;
        accountIcon.title = `Xin chào, ${username}`;
        accountIcon.classList.add('user-logged-in');

        // Add direct redirect to account page
        accountIcon.addEventListener('click', function(e) {
            e.preventDefault();
            // Check if we're in pages directory
            const isInPagesDir = window.location.pathname.includes('/pages/');
            const accountUrl = isInPagesDir ? './account.html' : './pages/account.html';
            window.location.href = accountUrl;
        });
    }
}

/**
 * Show account menu when clicking on username
 */
function showAccountMenu(element) {
    // Remove existing menu if any
    const existingMenu = document.querySelector('.account-menu');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }

    // Create account menu
    const accountMenu = document.createElement('div');
    accountMenu.className = 'account-menu';

    // Get user data
    const username = localStorage.getItem('username');
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const email = user.email || '<EMAIL>';

    // Set menu content
    accountMenu.innerHTML = `
        <div class="account-menu-header">
            <div class="account-menu-user">
                <div class="account-menu-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="account-menu-info">
                    <div class="account-menu-name">${username}</div>
                    <div class="account-menu-email">${email}</div>
                </div>
            </div>
        </div>
        <div class="account-menu-body">
            <a href="${getRelativePath()}pages/account.html" class="account-menu-item">
                <i class="fas fa-user"></i> Tài khoản của tôi
            </a>
            <a href="${getRelativePath()}order-tracking.html" class="account-menu-item">
                <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
            </a>
            <a href="${getRelativePath()}pages/wishlist.html" class="account-menu-item">
                <i class="fas fa-heart"></i> Danh sách yêu thích
            </a>
            <div class="account-menu-divider"></div>
            <a href="#" class="account-menu-item logout-btn">
                <i class="fas fa-sign-out-alt"></i> Đăng xuất
            </a>
        </div>
    `;

    // Add styles to the menu
    accountMenu.style.position = 'absolute';
    accountMenu.style.top = '100%';
    accountMenu.style.right = '0';
    accountMenu.style.backgroundColor = '#fff';
    accountMenu.style.borderRadius = '4px';
    accountMenu.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
    accountMenu.style.width = '250px';
    accountMenu.style.zIndex = '1000';
    accountMenu.style.overflow = 'hidden';
    accountMenu.style.opacity = '0';
    accountMenu.style.transform = 'translateY(10px)';
    accountMenu.style.transition = 'opacity 0.3s, transform 0.3s';

    // Position the menu relative to the user icon
    element.style.position = 'relative';
    element.appendChild(accountMenu);

    // Show with animation
    setTimeout(() => {
        accountMenu.style.opacity = '1';
        accountMenu.style.transform = 'translateY(0)';
    }, 10);

    // Add event listener to logout button
    const logoutBtn = accountMenu.querySelector('.logout-btn');
    logoutBtn.addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });

    // Close menu when clicking outside
    document.addEventListener('click', function closeMenu(e) {
        if (!accountMenu.contains(e.target) && e.target !== element) {
            accountMenu.style.opacity = '0';
            accountMenu.style.transform = 'translateY(10px)';
            setTimeout(() => {
                accountMenu.remove();
            }, 300);
            document.removeEventListener('click', closeMenu);
        }
    });

    // Add styles for menu items
    const menuItems = accountMenu.querySelectorAll('.account-menu-item');
    menuItems.forEach(item => {
        item.style.display = 'flex';
        item.style.alignItems = 'center';
        item.style.padding = '12px 15px';
        item.style.color = '#333';
        item.style.textDecoration = 'none';
        item.style.transition = 'background-color 0.3s';

        item.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#f5f5f5';
        });

        item.addEventListener('mouseout', function() {
            this.style.backgroundColor = 'transparent';
        });

        const icon = item.querySelector('i');
        if (icon) {
            icon.style.marginRight = '10px';
            icon.style.width = '20px';
            icon.style.textAlign = 'center';
        }
    });

    // Style for menu header
    const menuHeader = accountMenu.querySelector('.account-menu-header');
    if (menuHeader) {
        menuHeader.style.padding = '15px';
        menuHeader.style.borderBottom = '1px solid #eee';
        menuHeader.style.backgroundColor = '#f9f9f9';
    }

    // Style for user info
    const menuUser = accountMenu.querySelector('.account-menu-user');
    if (menuUser) {
        menuUser.style.display = 'flex';
        menuUser.style.alignItems = 'center';
    }

    const menuAvatar = accountMenu.querySelector('.account-menu-avatar');
    if (menuAvatar) {
        menuAvatar.style.fontSize = '2rem';
        menuAvatar.style.marginRight = '10px';
        menuAvatar.style.color = '#2c3e50';
    }

    const menuName = accountMenu.querySelector('.account-menu-name');
    if (menuName) {
        menuName.style.fontWeight = 'bold';
        menuName.style.fontSize = '1rem';
    }

    const menuEmail = accountMenu.querySelector('.account-menu-email');
    if (menuEmail) {
        menuEmail.style.fontSize = '0.8rem';
        menuEmail.style.color = '#777';
    }

    // Style for menu divider
    const menuDivider = accountMenu.querySelector('.account-menu-divider');
    if (menuDivider) {
        menuDivider.style.height = '1px';
        menuDivider.style.backgroundColor = '#eee';
        menuDivider.style.margin = '5px 0';
    }

    // Style for logout button
    const logoutButton = accountMenu.querySelector('.logout-btn');
    if (logoutButton) {
        logoutButton.style.color = '#e74c3c';
    }
}

/**
 * Logout function
 */
function logout() {
    // Update localStorage
    localStorage.setItem('isLoggedIn', 'false');

    // Show notification if function exists
    if (typeof showNotification === 'function') {
        showNotification('Đăng xuất thành công', 'success');
    }

    // Reset user icon
    const accountIcon = document.querySelector('.nav-icon.user-logged-in');
    if (accountIcon) {
        accountIcon.innerHTML = '<i class="fas fa-user"></i>';
        accountIcon.title = 'Tài khoản';
        accountIcon.classList.remove('user-logged-in');
    }

    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = getRelativePath() + 'index.html';
    }, 1500);
}

/**
 * Get relative path to root
 * This helps with navigation from different directory levels
 */
function getRelativePath() {
    const path = window.location.pathname;
    if (path.includes('/pages/')) {
        return '../';
    } else {
        return '';
    }
}

/**
 * Show notification if not defined elsewhere
 */
if (typeof showNotification !== 'function') {
    function showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                           type === 'error' ? '#F44336' :
                                           type === 'warning' ? '#FF9800' : '#2196F3';
        notification.style.color = '#fff';
        notification.style.padding = '15px 20px';
        notification.style.marginBottom = '10px';
        notification.style.borderRadius = '4px';
        notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(50px)';
        notification.style.transition = 'opacity 0.3s, transform 0.3s';

        // Icon based on notification type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
                break;
            default:
                icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
        }

        // Set notification content
        notification.innerHTML = `${icon} ${message}`;

        // Add to container
        container.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(50px)';

            // Remove from DOM after animation completes
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
}
