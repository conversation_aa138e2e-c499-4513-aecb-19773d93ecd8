/* Search Styles */
.search-bar {
    position: relative;
    flex: 1;
    max-width: 500px;
}

.search-bar form {
    display: flex;
    width: 100%;
}

.search-bar input {
    flex: 1;
    height: 40px;
    padding: 0 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
}

.search-bar input:focus {
    border-color: #ff6b6b;
}

.search-bar button {
    width: 40px;
    height: 40px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-bar button:hover {
    background-color: #ff5252;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: white;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    display: none;
}

.search-result-item {
    display: flex;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-result-item:hover {
    background-color: #f9f9f9;
}

.search-result-item img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    margin-right: 15px;
    border-radius: 4px;
}

.search-result-info {
    flex: 1;
}

.search-result-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.search-result-price {
    color: #ff6b6b;
    font-weight: 600;
}

.search-result-empty {
    padding: 15px;
    text-align: center;
    color: #666;
}

.search-result-more {
    padding: 15px;
    text-align: center;
    background-color: #f9f9f9;
    color: #ff6b6b;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-result-more:hover {
    background-color: #f0f0f0;
}

/* Search Results Page */
.search-results-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.search-results-header {
    margin-bottom: 30px;
}

.search-results-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.search-results-count {
    color: #666;
}

.search-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
}

.search-filter {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-filter-options {
    display: flex;
    gap: 15px;
}

.filter-option {
    padding: 8px 15px;
    background-color: #f0f0f0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-option:hover, .filter-option.active {
    background-color: #ff6b6b;
    color: white;
}

.search-sort {
    display: flex;
    align-items: center;
}

.search-sort label {
    margin-right: 10px;
    color: #666;
}

.search-sort select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    outline: none;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .search-filter {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .search-filter-options {
        flex-wrap: wrap;
    }
}
