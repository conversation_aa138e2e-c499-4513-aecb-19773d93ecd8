/* Marquee Promotion Banner Styles */
.marquee-container {
    position: relative;
    width: 100%;
    background: linear-gradient(90deg, #3498db, #2c3e50);
    color: white;
    overflow: hidden;
    height: 40px;
    z-index: 100;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.marquee-content {
    display: flex;
    align-items: center;
    position: absolute;
    white-space: nowrap;
    will-change: transform;
    height: 100%;
    animation: marquee 30s linear infinite;
}

.marquee-content:hover {
    animation-play-state: paused;
}

.marquee-item {
    display: flex;
    align-items: center;
    padding: 0 25px;
    font-weight: 500;
    position: relative;
}

.marquee-item:not(:last-child)::after {
    content: '•';
    position: absolute;
    right: 0;
    opacity: 0.7;
    font-size: 1.2rem;
}

.marquee-item i {
    margin-right: 10px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Hiệu ứng hover cho các item */
.marquee-item {
    transition: transform 0.3s, color 0.3s;
}

.marquee-item:hover {
    transform: translateY(-2px);
    color: #ffeb3b;
}

/* Responsive styles */
@media (max-width: 768px) {
    .marquee-container {
        height: 35px;
    }

    .marquee-item {
        padding: 0 15px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .marquee-container {
        height: 30px;
    }

    .marquee-item {
        padding: 0 10px;
        font-size: 12px;
    }
}
