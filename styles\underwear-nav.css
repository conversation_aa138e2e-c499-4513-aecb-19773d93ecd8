/* Underwear Navigation Styles */

.underwear-nav {
    background-color: var(--bg-light);
    padding: 15px 0;
    margin-bottom: 30px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.underwear-nav-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.underwear-nav-item {
    position: relative;
    padding: 10px 20px;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 5px;
}

.underwear-nav-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--accent-color);
}

.underwear-nav-item.active {
    color: var(--accent-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.underwear-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 3px;
}

/* Responsive */
@media (max-width: 768px) {
    .underwear-nav-container {
        flex-direction: row;
        overflow-x: auto;
        justify-content: flex-start;
        padding: 0 15px;
    }
    
    .underwear-nav-item {
        white-space: nowrap;
        padding: 10px 15px;
    }
}
