// Kids Page Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
    });

    // Update cart count
    updateCartCount();

    // Load products
    loadProducts();

    // Handle category tabs
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Filter products by category
            const category = this.dataset.category;
            filterProductsByCategory(category);
        });
    });

    // Handle filters
    document.getElementById('age-filter').addEventListener('change', filterProducts);
    document.getElementById('price-filter').addEventListener('change', filterProducts);
    document.getElementById('color-filter').addEventListener('change', filterProducts);
    document.getElementById('sort-filter').addEventListener('change', filterProducts);

    // Handle load more button
    document.getElementById('load-more').addEventListener('click', loadMoreProducts);

    // Handle size guide modal
    const sizeGuideBtn = document.getElementById('size-guide-btn');
    const sizeGuideModal = document.getElementById('size-guide-modal');
    const closeModal = document.querySelector('.close-modal');

    sizeGuideBtn.addEventListener('click', function(e) {
        e.preventDefault();
        sizeGuideModal.style.display = 'flex';
    });

    closeModal.addEventListener('click', function() {
        sizeGuideModal.style.display = 'none';
    });

    window.addEventListener('click', function(e) {
        if (e.target === sizeGuideModal) {
            sizeGuideModal.style.display = 'none';
        }
    });

    // Check URL parameters for category filter
    const urlParams = new URLSearchParams(window.location.search);
    const categoryParam = urlParams.get('category');

    if (categoryParam) {
        // Find the tab button with the matching category
        const tabButton = document.querySelector(`.tab-btn[data-category="${categoryParam}"]`);

        if (tabButton) {
            // Simulate click on the tab button
            tabButton.click();
        }
    }
});

// Sample kids products data
const kidsProducts = [
    {
        id: 1,
        name: 'Đầm công chúa NNJXD không tay vải tuyn thời trang mùa hè xinh xắn dành cho bé gái',
        price: '350.000đ',
        image: 'https://down-vn.img.susercontent.com/file/sg-11134201-23030-0jw28paq2mov5a@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/sg-11134201-23030-0jw28paq2mov5a@resize_w450_nl.webp',
        category: 'girls',
        age: '3-5',
        colors: ['pink', 'blue', 'yellow'],
        isNew: true,
        availableSizes: ['3Y', '4Y', '5Y', '6Y']
    },
    {
        id: 2,
        name: 'Áo thun ngắn tay hoạt hình mùa hè cho trẻ em',
        price: '250.000đ',
        image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7repq-m8edlhox3n2pff@resize_w450_nl.webp',
        category: 'boys',
        age: '6-8',
        colors: ['blue', 'red', 'green'],
        isNew: true,
        availableSizes: ['6Y', '7Y', '8Y']
    },
    {
        id: 3,
        name: 'Quần Legging Lửng MÈO Bé Gái Cực Thoải Mái Và Sang Chảnh,Quần Thun Ôm Lửng Bé Gái',
        price: '320.000đ',
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lyxd6wuyp5ltdf@resize_w450_nl.webp',
        category: 'girls',
        age: '6-8',
        colors: ['blue', 'pink'],
        isNew: false,
        availableSizes: ['6Y', '7Y', '8Y', '9Y']
    },
    {
        id: 4,
        name: 'Bộ đồ thể thao sát nach cho bé trai',
        price: '420.000đ',
        image: 'https://down-vn.img.susercontent.com/file/vn-11134201-7r98o-luenropq3d5c4b@resize_w450_nl.webp',
        category: 'boys',
        age: '9-12',
        colors: ['black', 'blue', 'red'],
        isNew: true,
        availableSizes: ['9Y', '10Y', '11Y', '12Y']
    },
    {
        id: 5,
        name: 'Đầm váy công chúa cho bé gái Tutupetti không tay xòe bồng xinh xắn',
        price: '550.000đ',
        image: '../img womens/women 11.webp',
        category: 'girls',
        age: '3-5',
        colors: ['pink', 'purple', 'white'],
        isNew: true,
        availableSizes: ['3Y', '4Y', '5Y']
    },
    {
        id: 6,
        name: 'Áo khoác chống nắng cho bé Beemo Chất liệu thun lạnh cao cấp siêu nhẹ co giãn thoáng khí',
        price: '450.000đ',
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m8mt7ehhjfr609@resize_w450_nl.webp',
        category: 'boys',
        age: '6-8',
        colors: ['navy', 'gray', 'black'],
        isNew: false,
        availableSizes: ['6Y', '7Y', '8Y']
    },
    {
        id: 7,
        name: 'Combo 4 cái bodysuit sơ sinh bé trai & bé gái ',
        price: '180.000đ',
        image: 'https://down-vn.img.susercontent.com/file/sg-11134201-22110-hmfql2zx6xjvce@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/sg-11134201-22110-hmfql2zx6xjvce@resize_w450_nl.webp',
        category: 'baby',
        age: '0-2',
        colors: ['white', 'yellow', 'green'],
        isNew: true,
        availableSizes: ['0-3M', '3-6M', '6-12M', '12-18M']
    },
    {
        id: 8,
        name: 'Mũ Mùa Đông Dễ Thương Len Màu Trơn Dệt Kim Bonnet Bebe Bé Sơ Sinh ',
        price: '150.000đ',
        image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rdxq-lzk41hloxx2v20.webp',
        category: 'accessories',
        age: '3-5',
        colors: ['red', 'blue', 'yellow', 'pink'],
        isNew: false,
        availableSizes: ['S', 'M', 'L']
    },
    {
        id: 9,
        name: 'Set sơ mi kèm cavart cho bé trai Beemo, Chất liệu thô Hàn, quần kaki mềm mại',
        price: '280.000đ',
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m9oa8ekzfxqa70@resize_w450_nl.webp',
        category: 'boys',
        age: '9-12',
        colors: ['white', 'blue', 'pink'],
        isNew: true,
        availableSizes: ['9Y', '10Y', '11Y', '12Y']
    },
    {
        id: 10,
        name: 'Thời Trang Hàn Quốc Bé Gái Cao Cấp Váy Học Viện Phong Cách Jk Slim Xếp Ly Váy Trượt Ván Quần Tennis ',
        price: '220.000đ',
        image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m34dz2nqqsqqaf@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m34dz2nqqsqqaf@resize_w450_nl.webp',
        category: 'girls',
        age: '6-15',
        colors: ['black', 'pink', 'purple'],
        isNew: false,
        availableSizes: ['6Y', '7Y', '8Y']
    },
    {
        id: 11,
        name: 'Ba Lô Nhỏ Đi Học Họa Tiết Hoạt Hình Dễ Thương Cho Bé Mẫu Giáo',
        price: '320.000đ',
        image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7qukw-lgdkslydavlq01@resize_w450_nl.webp',
        category: 'accessories',
        age: '3-5',
        colors: ['pink', 'blue', 'yellow'],
        isNew: true,
        availableSizes: ['One Size']
    },
    {
        id: 12,
        name: 'Đồ bộ bé gái BabyloveGO bộ quần áo Disney mùa hè chất thun cotton thoáng mát cho bé',
        price: '280.000đ',
        image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7ngzjvepuiu85@resize_w450_nl.webp',
        category: 'girls',
        age: '3-5',
        colors: ['pink', 'purple', 'blue'],
        isNew: false,
        availableSizes: ['3Y', '4Y', '5Y', '6Y']
    }
];

// Current state
let currentProducts = [...kidsProducts];
let visibleProducts = 8;

// Load products
function loadProducts() {
    const productsContainer = document.getElementById('products-container');

    // Clear container
    productsContainer.innerHTML = '';

    // Add products
    for (let i = 0; i < Math.min(visibleProducts, currentProducts.length); i++) {
        const product = currentProducts[i];

        const productCard = document.createElement('div');
        productCard.className = 'product-card';

        productCard.innerHTML = `
            <div class="product-image">
                ${product.isNew ? '<span class="new-badge">Mới</span>' : ''}
                <img src="${product.image}" alt="${product.name}">
                <div class="product-overlay">
                    <button class="btn-quick-view" data-id="${product.id}">Xem nhanh</button>
                    <button class="btn-add-to-cart" data-id="${product.id}">Thêm vào giỏ</button>
                </div>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-price">${product.price}</p>
                <div class="product-colors">
                    ${renderColorOptions(product.colors)}
                </div>
            </div>
        `;

        productsContainer.appendChild(productCard);
    }

    // Hide load more button if all products are visible
    const loadMoreButton = document.getElementById('load-more');
    if (visibleProducts >= currentProducts.length) {
        loadMoreButton.style.display = 'none';
    } else {
        loadMoreButton.style.display = 'block';
    }

    // Add event listeners to buttons
    addEventListenersToButtons();
}

// Render color options
function renderColorOptions(colors) {
    if (!colors || !Array.isArray(colors) || colors.length === 0) {
        return '';
    }

    return colors.map(color => {
        return `<span class="color-option ${color}" data-color="${color}"></span>`;
    }).join('');
}

// Add event listeners to buttons
function addEventListenersToButtons() {
    // Quick view buttons
    const quickViewButtons = document.querySelectorAll('.btn-quick-view');
    quickViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.id);
            showQuickView(productId);
        });
    });

    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.btn-add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.id);
            addToCart(productId);
        });
    });

    // Color options
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all color options in the same product
            const productCard = this.closest('.product-card');
            const colorOptions = productCard.querySelectorAll('.color-option');
            colorOptions.forEach(opt => opt.classList.remove('active'));

            // Add active class to clicked color option
            this.classList.add('active');
        });
    });
}

// Show quick view
function showQuickView(productId) {
    const product = kidsProducts.find(p => p.id === productId);
    if (!product) return;

    // In a real application, this would open a modal with product details
    // For this demo, we'll just show a notification
    showNotification(`Xem nhanh: ${product.name}`, 'info');
}

// Add to cart
function addToCart(productId) {
    const product = kidsProducts.find(p => p.id === productId);
    if (!product) return;

    // Kiểm tra đăng nhập
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || !user.isLoggedIn) {
        // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
        localStorage.setItem('redirectAfterLogin', window.location.href);

        // Hiển thị hộp thoại đăng nhập
        showLoginPrompt();
        return;
    }

    // Get selected color (if any)
    const productCard = document.querySelector(`.btn-add-to-cart[data-id="${productId}"]`).closest('.product-card');
    const activeColor = productCard.querySelector('.color-option.active');
    const color = activeColor ? activeColor.dataset.color : product.colors[0];

    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Add product to cart
    cart.push({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        image1: product.image, // Thêm image1 để tương thích với AddCart.html
        color: color,
        size: product.availableSizes[0], // Default to first size
        quantity: 1
    });

    // Save cart to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart count
    updateCartCount();

    // Show notification
    showNotification('Đã thêm sản phẩm vào giỏ hàng', 'success');
}

// Filter products by category
function filterProductsByCategory(category) {
    if (category === 'all') {
        currentProducts = [...kidsProducts];
    } else {
        currentProducts = kidsProducts.filter(product => product.category === category);
    }

    visibleProducts = 8;
    loadProducts();
}

// Filter products
function filterProducts() {
    const ageFilter = document.getElementById('age-filter').value;
    const priceFilter = document.getElementById('price-filter').value;
    const colorFilter = document.getElementById('color-filter').value;
    const sortFilter = document.getElementById('sort-filter').value;

    // Get active category
    const activeTab = document.querySelector('.tab-btn.active');
    const categoryFilter = activeTab ? activeTab.dataset.category : 'all';

    // Filter by category, age, price, and color
    let filteredProducts = kidsProducts.filter(product => {
        // Category filter
        if (categoryFilter !== 'all' && product.category !== categoryFilter) {
            return false;
        }

        // Age filter
        if (ageFilter !== 'all' && product.age !== ageFilter) {
            return false;
        }

        // Price filter
        if (priceFilter !== 'all') {
            const price = parseInt(product.price.replace(/\D/g, ''));

            if (priceFilter === '0-200000' && price > 200000) {
                return false;
            } else if (priceFilter === '200000-500000' && (price < 200000 || price > 500000)) {
                return false;
            } else if (priceFilter === '500000+' && price < 500000) {
                return false;
            }
        }

        // Color filter
        if (colorFilter !== 'all' && !product.colors.includes(colorFilter)) {
            return false;
        }

        return true;
    });

    // Sort products
    filteredProducts = sortProducts(filteredProducts, sortFilter);

    // Update current products
    currentProducts = filteredProducts;
    visibleProducts = 8;

    // Reload products
    loadProducts();
}

// Sort products
function sortProducts(products, sortBy) {
    const sortedProducts = [...products];

    switch (sortBy) {
        case 'newest':
            sortedProducts.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0));
            break;
        case 'price-asc':
            sortedProducts.sort((a, b) => {
                const priceA = parseInt(a.price.replace(/\D/g, ''));
                const priceB = parseInt(b.price.replace(/\D/g, ''));
                return priceA - priceB;
            });
            break;
        case 'price-desc':
            sortedProducts.sort((a, b) => {
                const priceA = parseInt(a.price.replace(/\D/g, ''));
                const priceB = parseInt(b.price.replace(/\D/g, ''));
                return priceB - priceA;
            });
            break;
        case 'name-asc':
            sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'name-desc':
            sortedProducts.sort((a, b) => b.name.localeCompare(a.name));
            break;
    }

    return sortedProducts;
}

// Load more products
function loadMoreProducts() {
    visibleProducts += 4;
    loadProducts();
}

// Update cart count
function updateCartCount() {
    const cartCountElement = document.querySelector('.cart-count');
    if (!cartCountElement) return;

    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Update cart count
    cartCountElement.textContent = cart.length;
}

// Show login prompt
function showLoginPrompt() {
    // Create login prompt container
    const loginPrompt = document.createElement('div');
    loginPrompt.className = 'login-prompt-overlay';
    loginPrompt.style.position = 'fixed';
    loginPrompt.style.top = '0';
    loginPrompt.style.left = '0';
    loginPrompt.style.width = '100%';
    loginPrompt.style.height = '100%';
    loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    loginPrompt.style.display = 'flex';
    loginPrompt.style.alignItems = 'center';
    loginPrompt.style.justifyContent = 'center';
    loginPrompt.style.zIndex = '9999';
    loginPrompt.style.opacity = '0';
    loginPrompt.style.transition = 'opacity 0.3s ease';

    loginPrompt.innerHTML = `
        <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
            <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
            </div>
            <div class="login-prompt-body" style="padding: 20px;">
                <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                    <a href="../pages/login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                    <a href="../pages/register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                </div>
            </div>
        </div>
    `;

    // Add to body
    document.body.appendChild(loginPrompt);

    // Show with animation
    setTimeout(() => {
        loginPrompt.style.opacity = '1';
    }, 10);

    // Close button event
    const closeButton = loginPrompt.querySelector('.login-prompt-close');
    closeButton.addEventListener('click', function() {
        loginPrompt.style.opacity = '0';
        setTimeout(() => {
            loginPrompt.remove();
        }, 300);
    });
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
