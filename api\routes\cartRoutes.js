const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const products = require('../data/products');

// Secret key for JWT
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify token
const verifyToken = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      status: 'fail',
      message: 'No token provided'
    });
  }
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.userId = decoded.id;
    next();
  } catch (error) {
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid token'
    });
  }
};

// In-memory storage for carts (in a real app, this would be a database)
const carts = {};

// Get cart
router.get('/', verifyToken, (req, res) => {
  const userId = req.userId;
  const cart = carts[userId] || [];
  
  res.json({
    status: 'success',
    results: cart.length,
    data: cart
  });
});

// Add item to cart
router.post('/add', verifyToken, (req, res) => {
  const userId = req.userId;
  const { productId, quantity = 1, color, size } = req.body;
  
  // Find product
  const product = products.find(p => p.id === productId);
  
  if (!product) {
    return res.status(404).json({
      status: 'fail',
      message: 'Product not found'
    });
  }
  
  // Initialize cart if it doesn't exist
  if (!carts[userId]) {
    carts[userId] = [];
  }
  
  // Check if product already exists in cart
  const existingItemIndex = carts[userId].findIndex(item => 
    item.product.id === productId && 
    item.color === color && 
    item.size === size
  );
  
  if (existingItemIndex !== -1) {
    // Update quantity
    carts[userId][existingItemIndex].quantity += quantity;
  } else {
    // Add new item
    carts[userId].push({
      id: Date.now().toString(),
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image
      },
      quantity,
      color: color || product.colors[0],
      size: size || product.sizes[0],
      addedAt: new Date().toISOString()
    });
  }
  
  res.json({
    status: 'success',
    results: carts[userId].length,
    data: carts[userId]
  });
});

// Update cart item
router.put('/update/:itemId', verifyToken, (req, res) => {
  const userId = req.userId;
  const { itemId } = req.params;
  const { quantity, color, size } = req.body;
  
  // Check if cart exists
  if (!carts[userId]) {
    return res.status(404).json({
      status: 'fail',
      message: 'Cart not found'
    });
  }
  
  // Find item
  const itemIndex = carts[userId].findIndex(item => item.id === itemId);
  
  if (itemIndex === -1) {
    return res.status(404).json({
      status: 'fail',
      message: 'Item not found in cart'
    });
  }
  
  // Update item
  carts[userId][itemIndex] = {
    ...carts[userId][itemIndex],
    quantity: quantity || carts[userId][itemIndex].quantity,
    color: color || carts[userId][itemIndex].color,
    size: size || carts[userId][itemIndex].size
  };
  
  res.json({
    status: 'success',
    data: carts[userId][itemIndex]
  });
});

// Remove item from cart
router.delete('/remove/:itemId', verifyToken, (req, res) => {
  const userId = req.userId;
  const { itemId } = req.params;
  
  // Check if cart exists
  if (!carts[userId]) {
    return res.status(404).json({
      status: 'fail',
      message: 'Cart not found'
    });
  }
  
  // Remove item
  carts[userId] = carts[userId].filter(item => item.id !== itemId);
  
  res.json({
    status: 'success',
    results: carts[userId].length,
    data: carts[userId]
  });
});

// Clear cart
router.delete('/clear', verifyToken, (req, res) => {
  const userId = req.userId;
  
  // Clear cart
  carts[userId] = [];
  
  res.json({
    status: 'success',
    message: 'Cart cleared',
    data: []
  });
});

module.exports = router;
