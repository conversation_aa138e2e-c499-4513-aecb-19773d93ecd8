/* Sexy Products Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --accent-light: rgba(231, 76, 60, 0.1);
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

/* Hero Section */
.product-hero {
    height: 70vh;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
}

.product-hero-content {
    max-width: 800px;
    padding: 0 20px;
    z-index: 1;
}

.product-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInDown 1s ease;
}

.product-hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease 0.3s forwards;
    opacity: 0;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Featured Collection */
.featured-collection {
    padding: 80px 0;
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.featured-collection::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background-color: var(--accent-light);
    border-radius: 50%;
    z-index: 0;
}

.featured-collection::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background-color: var(--accent-light);
    border-radius: 50%;
    z-index: 0;
}

.section-title {
    position: relative;
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: var(--primary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
}

.featured-slider {
    margin-top: 40px;
}

.featured-slide {
    padding: 0 15px;
}

.featured-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featured-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.featured-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.featured-card:hover .featured-image img {
    transform: scale(1.1);
}

.featured-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.featured-card:hover .featured-overlay {
    opacity: 1;
}

.btn-view {
    display: inline-block;
    padding: 12px 25px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.featured-card:hover .btn-view {
    transform: translateY(0);
}

.btn-view:hover {
    background-color: var(--accent-hover);
}

.featured-content {
    padding: 20px;
}

.featured-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

.featured-content p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Product Categories */
.product-categories {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.product-categories::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -100px;
    width: 200px;
    height: 200px;
    background-color: var(--accent-light);
    border-radius: 50%;
    z-index: -1;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.category-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 300px;
    box-shadow: var(--box-shadow);
}

.category-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.category-card:hover img {
    transform: scale(1.1);
}

.category-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: white;
    transition: transform 0.3s ease;
}

.category-card:hover .category-content {
    transform: translateY(-10px);
}

.category-content h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.btn-shop {
    display: inline-block;
    padding: 8px 20px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-shop:hover {
    background-color: var(--accent-hover);
}

/* Best Sellers */
.best-sellers {
    padding: 80px 0;
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.best-sellers::before {
    content: '';
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 250px;
    height: 250px;
    background-color: var(--accent-light);
    border-radius: 50%;
    z-index: 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.product-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
}

.badge.hot {
    background-color: var(--accent-color);
    color: white;
}

.badge.sale {
    background-color: var(--warning-color);
    color: white;
}

.badge.new {
    background-color: var(--success-color);
    color: white;
}

.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.btn-wishlist, .btn-quickview {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-wishlist:hover, .btn-quickview:hover {
    background-color: var(--accent-color);
    color: white;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--text-color);
    height: 2.4em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.current-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-color);
}

.old-price {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.product-colors {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.color-option:hover {
    transform: scale(1.2);
}

.btn-add-to-cart {
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-add-to-cart:hover {
    background-color: var(--accent-color);
}

.text-center {
    text-align: center;
    margin-top: 40px;
}

.btn-view-all {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-view-all:hover {
    background-color: var(--accent-color);
}

/* Testimonials */
.testimonials {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    background-color: #fff;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 200px;
    background-color: var(--accent-light);
    border-radius: 50%;
    z-index: 0;
}

.testimonials-slider {
    margin-top: 40px;
}

.testimonial-slide {
    padding: 0 15px;
}

.testimonial-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--box-shadow);
    height: 100%;
}

.testimonial-rating {
    color: #ffc107;
    margin-bottom: 15px;
}

.testimonial-text {
    font-style: italic;
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 20px;
    min-height: 100px;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
}

.testimonial-author h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: var(--text-color);
}

.testimonial-author p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Newsletter */
.newsletter {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.newsletter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/pattern.png');
    opacity: 0.05;
    z-index: 0;
}

.newsletter-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 1;
}

.newsletter-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.newsletter-content p {
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 5px 0 0 5px;
    font-size: 1rem;
}

.newsletter-form button {
    padding: 0 30px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.newsletter-form button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.newsletter-form button:hover::before {
    left: 100%;
}

.newsletter-form button:hover {
    background-color: var(--accent-hover);
}

/* Responsive */
@media (max-width: 992px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .product-hero-content h1 {
        font-size: 2.5rem;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-form input {
        border-radius: 5px;
        margin-bottom: 10px;
    }

    .newsletter-form button {
        border-radius: 5px;
        width: 100%;
        padding: 15px;
    }
}
