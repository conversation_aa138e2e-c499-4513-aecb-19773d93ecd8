/* Women's Page Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

/* Hero Section */
.women-hero {
    height: 70vh;
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../img womens/women-hero.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
}

.women-hero-content {
    max-width: 800px;
    padding: 0 20px;
    z-index: 1;
}

.women-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.women-hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.women-hero-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.hero-btn {
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.hero-btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.hero-btn-primary:hover {
    background-color: var(--accent-hover);
}

.hero-btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.hero-btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Categories Section */
.women-categories {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: var(--primary-color);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
}

.categories-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.category-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease;
}

.category-item:hover {
    transform: translateY(-10px);
}

.category-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.category-item:hover .category-image {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: white;
}

.category-overlay h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.category-overlay p {
    font-size: 0.9rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.category-btn {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.category-btn:hover {
    background-color: var(--accent-hover);
}

/* Featured Products Section */
.featured-products {
    padding: 80px 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.product-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
}

.product-badge.new {
    background-color: var(--success-color);
    color: white;
}

.product-badge.sale {
    background-color: var(--accent-color);
    color: white;
}

.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.product-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-action-btn:hover {
    background-color: var(--accent-color);
    color: white;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--text-color);
    height: 2.4em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.current-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-color);
}

.old-price {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.product-colors {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.color-option:hover {
    transform: scale(1.2);
}

.add-to-cart-btn {
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.add-to-cart-btn:hover {
    background-color: var(--accent-color);
}

.view-all-btn {
    display: block;
    width: 200px;
    margin: 40px auto 0;
    padding: 12px 0;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.view-all-btn:hover {
    background-color: var(--accent-color);
}

/* Lookbook Section */
.lookbook {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.lookbook-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 300px);
    gap: 20px;
}

.lookbook-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.lookbook-item:first-child {
    grid-column: 1 / 3;
    grid-row: 1 / 3;
}

.lookbook-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.lookbook-item:hover .lookbook-image {
    transform: scale(1.1);
}

.lookbook-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lookbook-item:hover .lookbook-overlay {
    opacity: 1;
}

.lookbook-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
    text-align: center;
}

.lookbook-btn {
    padding: 10px 20px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.lookbook-btn:hover {
    background-color: var(--accent-hover);
}

/* Responsive */
@media (max-width: 992px) {
    .categories-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .lookbook-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(3, 300px);
    }
    
    .lookbook-item:first-child {
        grid-column: 1;
        grid-row: 1;
    }
}

@media (max-width: 768px) {
    .women-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .women-hero-buttons {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .categories-container {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
}
