/* Smart Features Styles */

/* Personalized Recommendations Section */
.personalized-recommendations {
    padding: 60px 0;
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.personalized-recommendations::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/pattern-bg.svg');
    background-size: cover;
    opacity: 0.05;
    z-index: 0;
}

.personalized-recommendations .container {
    position: relative;
    z-index: 1;
}

.personalized-recommendations .section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 40px;
}

.personalized-recommendations .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: #ff6b6b;
}

.personalized-recommendations .section-title::before {
    content: '\f0eb';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #ff6b6b;
    margin-right: 10px;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.recommendation-header .recommendation-info {
    font-size: 0.9rem;
    color: #868e96;
    max-width: 60%;
}

.recommendation-header .recommendation-actions {
    display: flex;
    gap: 15px;
}

.recommendation-header .recommendation-actions button {
    padding: 8px 15px;
    border-radius: 20px;
    background-color: transparent;
    border: 1px solid #dee2e6;
    color: #343a40;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.recommendation-header .recommendation-actions button:hover {
    background-color: #f1f3f5;
    border-color: #ced4da;
}

.recommendation-header .recommendation-actions button i {
    font-size: 0.8rem;
}

/* Complementary Products Section */
.complementary-products {
    padding: 40px 0;
    background-color: #ffffff;
}

.complementary-products .section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.complementary-products .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: #339af0;
}

.complementary-products .section-title::before {
    content: '\f074';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #339af0;
    margin-right: 10px;
}

/* Smart Notifications */
.smart-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-width: calc(100% - 40px);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.smart-notification {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    transform: translateX(120%);
    transition: transform 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    position: relative;
    overflow: hidden;
}

.smart-notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #339af0;
}

.smart-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.smart-notification.urgent::before {
    background-color: #fa5252;
}

.smart-notification.high::before {
    background-color: #ff6b6b;
}

.smart-notification.medium::before {
    background-color: #339af0;
}

.smart-notification.low::before {
    background-color: #40c057;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(51, 154, 240, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #339af0;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.smart-notification.urgent .notification-icon {
    background-color: rgba(250, 82, 82, 0.1);
    color: #fa5252;
}

.smart-notification.high .notification-icon {
    background-color: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
}

.smart-notification.medium .notification-icon {
    background-color: rgba(51, 154, 240, 0.1);
    color: #339af0;
}

.smart-notification.low .notification-icon {
    background-color: rgba(64, 192, 87, 0.1);
    color: #40c057;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 1rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 5px;
}

.notification-message {
    font-size: 0.9rem;
    color: #868e96;
    margin-bottom: 10px;
    line-height: 1.4;
}

.notification-action {
    display: inline-block;
    padding: 5px 12px;
    background-color: #339af0;
    color: white;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.notification-action:hover {
    background-color: #228be6;
}

.smart-notification.urgent .notification-action {
    background-color: #fa5252;
}

.smart-notification.urgent .notification-action:hover {
    background-color: #e03131;
}

.smart-notification.high .notification-action {
    background-color: #ff6b6b;
}

.smart-notification.high .notification-action:hover {
    background-color: #fa5252;
}

.smart-notification.low .notification-action {
    background-color: #40c057;
}

.smart-notification.low .notification-action:hover {
    background-color: #37b24d;
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #adb5bd;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.notification-close:hover {
    color: #495057;
}

/* Smart Product Card Enhancements */
.product-card {
    position: relative;
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.recommended {
    background-color: #ff6b6b;
    color: white;
}

.product-badge.trending {
    background-color: #339af0;
    color: white;
}

.product-badge.sale {
    background-color: #fa5252;
    color: white;
}

.product-badge.new {
    background-color: #40c057;
    color: white;
}

.product-badge.almost-gone {
    background-color: #fab005;
    color: white;
}

.product-match {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 700;
    color: #343a40;
    z-index: 2;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.product-match.high {
    color: #40c057;
}

.product-match.medium {
    color: #339af0;
}

.product-match.low {
    color: #ff6b6b;
}

.product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 8px;
}

.product-tag {
    padding: 3px 8px;
    border-radius: 3px;
    background-color: #f1f3f5;
    color: #495057;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .smart-notification-container {
        width: calc(100% - 40px);
        top: auto;
        bottom: 20px;
    }
    
    .recommendation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .recommendation-header .recommendation-info {
        max-width: 100%;
    }
}
