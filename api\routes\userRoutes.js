const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const users = require('../data/users');

// Secret key for JWT
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify token
const verifyToken = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      status: 'fail',
      message: 'No token provided'
    });
  }
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.userId = decoded.id;
    next();
  } catch (error) {
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid token'
    });
  }
};

// Login
router.post('/login', (req, res) => {
  const { username, password } = req.body;
  
  // Find user
  const user = users.find(u => u.username === username);
  
  if (!user) {
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid username or password'
    });
  }
  
  // Check password
  const isPasswordValid = bcrypt.compareSync(password, user.password);
  
  if (!isPasswordValid) {
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid username or password'
    });
  }
  
  // Create token
  const token = jwt.sign({ id: user.id }, JWT_SECRET, {
    expiresIn: '1d'
  });
  
  // Update last login
  user.lastLogin = new Date().toISOString();
  
  // Return user info (without password)
  const { password: _, ...userWithoutPassword } = user;
  
  res.json({
    status: 'success',
    token,
    data: userWithoutPassword
  });
});

// Register
router.post('/register', (req, res) => {
  const { username, email, password, fullName, phone } = req.body;
  
  // Check if username or email already exists
  if (users.some(u => u.username === username)) {
    return res.status(400).json({
      status: 'fail',
      message: 'Username already exists'
    });
  }
  
  if (users.some(u => u.email === email)) {
    return res.status(400).json({
      status: 'fail',
      message: 'Email already exists'
    });
  }
  
  // Create new user
  const newUser = {
    id: users.length + 1,
    username,
    email,
    password: bcrypt.hashSync(password, 10),
    fullName,
    phone,
    address: '',
    gender: '',
    birthDate: '',
    avatar: '../img/avatars/default.jpg',
    role: 'user',
    createdAt: new Date().toISOString(),
    lastLogin: new Date().toISOString()
  };
  
  // Add to users array
  users.push(newUser);
  
  // Create token
  const token = jwt.sign({ id: newUser.id }, JWT_SECRET, {
    expiresIn: '1d'
  });
  
  // Return user info (without password)
  const { password: _, ...userWithoutPassword } = newUser;
  
  res.status(201).json({
    status: 'success',
    token,
    data: userWithoutPassword
  });
});

// Get user profile
router.get('/profile', verifyToken, (req, res) => {
  const user = users.find(u => u.id === req.userId);
  
  if (!user) {
    return res.status(404).json({
      status: 'fail',
      message: 'User not found'
    });
  }
  
  // Return user info (without password)
  const { password: _, ...userWithoutPassword } = user;
  
  res.json({
    status: 'success',
    data: userWithoutPassword
  });
});

// Update user profile
router.put('/profile', verifyToken, (req, res) => {
  const { fullName, phone, address, gender, birthDate } = req.body;
  
  const userIndex = users.findIndex(u => u.id === req.userId);
  
  if (userIndex === -1) {
    return res.status(404).json({
      status: 'fail',
      message: 'User not found'
    });
  }
  
  // Update user
  users[userIndex] = {
    ...users[userIndex],
    fullName: fullName || users[userIndex].fullName,
    phone: phone || users[userIndex].phone,
    address: address || users[userIndex].address,
    gender: gender || users[userIndex].gender,
    birthDate: birthDate || users[userIndex].birthDate
  };
  
  // Return user info (without password)
  const { password: _, ...userWithoutPassword } = users[userIndex];
  
  res.json({
    status: 'success',
    data: userWithoutPassword
  });
});

module.exports = router;
