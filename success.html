<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">

    <title>Đặt hàng thành công | Fashion Store</title>
    <style>
        /* Success Page Styles */
        .success-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 0 15px;
            text-align: center;
        }

        .success-icon {
            font-size: 80px;
            color: #4CAF50;
            margin-bottom: 20px;
        }

        .success-title {
            font-size: 32px;
            color: #333;
            margin-bottom: 15px;
        }

        .success-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .order-number {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 30px;
            font-size: 18px;
            color: #333;
        }

        .order-number span {
            font-weight: 600;
            color: #ff6b6b;
        }

        .success-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 25px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #ff6b6b;
            color: white;
        }

        .btn-primary:hover {
            background-color: #ff5252;
        }

        .btn-secondary {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
        }

        .success-info {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: left;
        }

        .success-info h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 15px;
        }

        .info-item i {
            color: #ff6b6b;
            margin-top: 3px;
        }

        .info-item p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }

        @media (max-width: 576px) {
            .success-actions {
                flex-direction: column;
                gap: 15px;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Success Content -->
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>

        <h1 class="success-title">Đặt hàng thành công!</h1>

        <p class="success-message">
            Cảm ơn bạn đã mua sắm tại Fashion Store. Đơn hàng của bạn đã được tiếp nhận và đang được xử lý.<br>
            Chúng tôi sẽ gửi email xác nhận đơn hàng cho bạn trong thời gian sớm nhất.
        </p>

        <div class="order-number">
            Mã đơn hàng: <span id="order-number"></span>
        </div>

        <div class="success-actions">
            <a href="index.html" class="btn btn-primary">
                <i class="fas fa-home"></i> Tiếp tục mua sắm
            </a>

            <a href="#" class="btn btn-secondary" id="view-order">
                <i class="fas fa-clipboard-list"></i> Xem đơn hàng
            </a>
        </div>

        <div class="success-info">
            <h3>Thông tin hữu ích</h3>

            <div class="info-item">
                <i class="fas fa-truck"></i>
                <p>Đơn hàng của bạn sẽ được giao trong vòng 3-5 ngày làm việc. Bạn sẽ nhận được thông báo khi đơn hàng được giao.</p>
            </div>

            <div class="info-item">
                <i class="fas fa-phone"></i>
                <p>Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi qua số điện thoại <strong>1900 1234</strong> hoặc email <strong><EMAIL></strong>.</p>
            </div>

            <div class="info-item">
                <i class="fas fa-undo"></i>
                <p>Chính sách đổi trả: Bạn có thể đổi trả sản phẩm trong vòng 30 ngày kể từ ngày nhận hàng nếu sản phẩm còn nguyên tem mác và chưa qua sử dụng.</p>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Hiển thị mã đơn hàng
        document.addEventListener('DOMContentLoaded', function() {
            // Kiểm tra đăng nhập
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user || !user.isLoggedIn) {
                // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
                localStorage.setItem('redirectAfterLogin', window.location.href);

                // Chuyển hướng đến trang đăng nhập
                window.location.href = './pages/login.html';
                return;
            }

            // Lấy thông tin đơn hàng từ localStorage
            const orderHistory = JSON.parse(localStorage.getItem("order_history")) || [];

            if (orderHistory.length > 0) {
                // Lấy đơn hàng mới nhất
                const latestOrder = orderHistory[orderHistory.length - 1];

                // Hiển thị mã đơn hàng
                document.getElementById('order-number').textContent = latestOrder.orderNumber || 'ORD' + Date.now();

                // Xử lý nút xem đơn hàng
                document.getElementById('view-order').addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('Chức năng xem đơn hàng đang được phát triển. Vui lòng quay lại sau!');
                });
            } else {
                // Nếu không có thông tin đơn hàng, hiển thị mã đơn hàng ngẫu nhiên
                document.getElementById('order-number').textContent = 'ORD' + Date.now();
            }

            // Xóa thông tin đơn hàng khỏi localStorage
            localStorage.removeItem("order_info");
        });
    </script>
</body>
</html>
