// Style Quiz Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Quiz state
    const quizState = {
        currentStep: 1,
        totalSteps: 5,
        answers: {
            gender: '',
            age: '',
            height: 165,
            weight: 60,
            styles: [],
            colors: [],
            occasions: [],
            budget: ''
        }
    };

    // Sample product recommendations
    const productRecommendations = {
        'casual': [
            {
                name: '<PERSON><PERSON> thun trắng cơ bản',
                price: '250.000đ',
                image: '../img womens/women 5.webp'
            },
            {
                name: 'Quần jean xanh đậm',
                price: '450.000đ',
                image: '../img womens/women 2.webp'
            },
            {
                name: '<PERSON><PERSON> kho<PERSON> denim',
                price: '650.000đ',
                image: '../img womens/women 4.webp'
            },
            {
                name: '<PERSON><PERSON><PERSON>y sneaker trắng',
                price: '550.000đ',
                image: '../img/shoes/sneakers.jpg'
            }
        ],
        'formal': [
            {
                name: '<PERSON><PERSON> sơ mi trắng',
                price: '350.000đ',
                image: '../img womens/women 25.webp'
            },
            {
                name: 'Quần tây đen',
                price: '480.000đ',
                image: '../img/bottoms/black-pants.jpg'
            },
            {
                name: 'Áo vest đen',
                price: '850.000đ',
                image: '../img/outerwear/black-blazer.jpg'
            },
            {
                name: 'Giày cao gót đen',
                price: '650.000đ',
                image: '../img/shoes/black-heels.jpg'
            }
        ],
        'streetwear': [
            {
                name: 'Áo hoodie đen',
                price: '450.000đ',
                image: '../img/tops/black-hoodie.jpg'
            },
            {
                name: 'Quần jogger xám',
                price: '380.000đ',
                image: '../img/bottoms/gray-joggers.jpg'
            },
            {
                name: 'Áo khoác bomber',
                price: '750.000đ',
                image: '../img mens/men 3.jpg'
            },
            {
                name: 'Giày sneaker high-top',
                price: '650.000đ',
                image: '../img/shoes/high-top-sneakers.jpg'
            }
        ],
        'vintage': [
            {
                name: 'Áo sơ mi họa tiết',
                price: '380.000đ',
                image: '../img/tops/patterned-shirt.jpg'
            },
            {
                name: 'Quần ống rộng',
                price: '420.000đ',
                image: '../img/bottoms/wide-leg-pants.jpg'
            },
            {
                name: 'Váy midi hoa',
                price: '550.000đ',
                image: '../img womens/women 3.webp'
            },
            {
                name: 'Giày loafer nâu',
                price: '580.000đ',
                image: '../img/shoes/brown-loafers.jpg'
            }
        ],
        'minimalist': [
            {
                name: 'Áo thun đen cơ bản',
                price: '250.000đ',
                image: '../img/tops/black-tshirt.jpg'
            },
            {
                name: 'Quần kaki beige',
                price: '420.000đ',
                image: '../img womens/women 6.webp'
            },
            {
                name: 'Áo khoác dáng dài',
                price: '850.000đ',
                image: '../img/outerwear/long-coat.jpg'
            },
            {
                name: 'Giày mules trắng',
                price: '480.000đ',
                image: '../img/shoes/white-mules.jpg'
            }
        ],
        'bohemian': [
            {
                name: 'Áo kiểu thêu hoa',
                price: '380.000đ',
                image: '../img/tops/embroidered-top.jpg'
            },
            {
                name: 'Váy maxi họa tiết',
                price: '580.000đ',
                image: '../img/dresses/maxi-dress.jpg'
            },
            {
                name: 'Áo khoác kimono',
                price: '650.000đ',
                image: '../img/outerwear/kimono.jpg'
            },
            {
                name: 'Giày sandal da',
                price: '350.000đ',
                image: '../img/shoes/leather-sandals.jpg'
            }
        ]
    };

    // Style tips
    const styleTips = {
        'casual': [
            'Kết hợp áo thun trắng với quần jean xanh đậm cho vẻ ngoài đơn giản nhưng tinh tế',
            'Thêm áo khoác denim để tạo điểm nhấn cho trang phục',
            'Chọn giày sneaker trắng để dễ dàng phối với nhiều trang phục khác nhau',
            'Phụ kiện đơn giản như đồng hồ hoặc vòng tay sẽ làm tăng vẻ cá tính'
        ],
        'formal': [
            'Áo sơ mi trắng là item không thể thiếu trong tủ đồ công sở',
            'Chọn quần tây có độ dài vừa phải, không quá ngắn hoặc quá dài',
            'Đầu tư vào một chiếc áo vest chất lượng tốt để sử dụng lâu dài',
            'Giày cao gót đen là lựa chọn an toàn và dễ phối đồ'
        ],
        'streetwear': [
            'Kết hợp áo hoodie với quần jogger cho vẻ ngoài năng động',
            'Chọn áo khoác bomber oversized để tạo điểm nhấn',
            'Phối layer nhiều lớp áo để tạo chiều sâu cho trang phục',
            'Giày sneaker là yếu tố quan trọng trong phong cách streetwear'
        ],
        'vintage': [
            'Tìm kiếm những item có họa tiết retro hoặc cổ điển',
            'Kết hợp các item hiện đại với item vintage để tạo sự cân bằng',
            'Phụ kiện như khăn quàng cổ, mũ beret sẽ làm tăng vẻ vintage',
            'Chọn màu sắc pastel hoặc earth tone để tạo cảm giác hoài cổ'
        ],
        'minimalist': [
            'Ưu tiên chất lượng hơn số lượng khi mua sắm',
            'Chọn những item có màu sắc trung tính như đen, trắng, be, xám',
            'Tập trung vào form dáng và chất liệu của trang phục',
            'Phụ kiện tối giản nhưng tinh tế sẽ làm tăng vẻ sang trọng'
        ],
        'bohemian': [
            'Kết hợp các họa tiết và texture khác nhau để tạo sự phong phú',
            'Chọn trang phục có form rộng, thoải mái',
            'Phụ kiện như vòng cổ statement, khuyên tai to sẽ làm tăng vẻ bohemian',
            'Ưu tiên chất liệu tự nhiên như cotton, linen, da'
        ]
    };

    // DOM elements
    const quizSteps = document.querySelectorAll('.quiz-step');
    const progressFill = document.querySelector('.progress-fill');
    const steps = document.querySelectorAll('.step');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const heightSlider = document.getElementById('height-slider');
    const weightSlider = document.getElementById('weight-slider');
    const heightValue = document.getElementById('height-value');
    const weightValue = document.getElementById('weight-value');
    const optionButtons = document.querySelectorAll('.option-btn');
    const styleOptions = document.querySelectorAll('.style-option');
    const colorOptions = document.querySelectorAll('.color-option');
    const occasionOptions = document.querySelectorAll('.occasion-option');
    const budgetOptions = document.querySelectorAll('.budget-option');
    const loadingResults = document.querySelector('.loading-results');
    const quizResults = document.querySelector('.quiz-results');
    const styleName = document.getElementById('style-name');
    const styleDescription = document.getElementById('style-description');
    const recommendationGrid = document.querySelector('.recommendation-grid');
    const styleTipsList = document.getElementById('style-tips-list');
    const saveResultsBtn = document.getElementById('save-results');
    const shareResultsBtn = document.getElementById('share-results');
    const shopRecommendationsBtn = document.getElementById('shop-recommendations');

    // Initialize
    function init() {
        updateProgressBar();
        setupEventListeners();
    }

    // Update progress bar
    function updateProgressBar() {
        const progress = ((quizState.currentStep - 1) / quizState.totalSteps) * 100;
        progressFill.style.width = `${progress}%`;
        
        // Update steps
        steps.forEach((step, index) => {
            const stepNumber = parseInt(step.dataset.step);
            
            if (stepNumber < quizState.currentStep) {
                step.classList.add('completed');
                step.innerHTML = '<i class="fas fa-check"></i>';
            } else if (stepNumber === quizState.currentStep) {
                step.classList.add('active');
                step.classList.remove('completed');
                step.textContent = stepNumber;
            } else {
                step.classList.remove('active', 'completed');
                step.textContent = stepNumber;
            }
        });
    }

    // Go to next step
    function nextStep() {
        if (quizState.currentStep < quizState.totalSteps) {
            quizState.currentStep++;
            showCurrentStep();
            updateProgressBar();
            
            // Enable prev button
            prevBtn.disabled = false;
            
            // Change next button text on last step
            if (quizState.currentStep === quizState.totalSteps) {
                nextBtn.innerHTML = 'Xem kết quả <i class="fas fa-check"></i>';
            }
        } else {
            // Show results
            showResults();
        }
    }

    // Go to previous step
    function prevStep() {
        if (quizState.currentStep > 1) {
            quizState.currentStep--;
            showCurrentStep();
            updateProgressBar();
            
            // Disable prev button on first step
            if (quizState.currentStep === 1) {
                prevBtn.disabled = true;
            }
            
            // Reset next button text
            nextBtn.innerHTML = 'Tiếp theo <i class="fas fa-arrow-right"></i>';
        }
    }

    // Show current step
    function showCurrentStep() {
        quizSteps.forEach(step => {
            step.classList.remove('active');
        });
        
        const currentStep = document.querySelector(`.quiz-step[data-step="${quizState.currentStep}"]`);
        if (currentStep) {
            currentStep.classList.add('active');
        }
    }

    // Show results
    function showResults() {
        // Hide all steps
        quizSteps.forEach(step => {
            step.classList.remove('active');
        });
        
        // Show result step
        const resultStep = document.querySelector('.quiz-step[data-step="result"]');
        if (resultStep) {
            resultStep.classList.add('active');
        }
        
        // Hide navigation buttons
        document.querySelector('.quiz-navigation').style.display = 'none';
        
        // Simulate loading
        setTimeout(() => {
            loadingResults.style.display = 'none';
            quizResults.style.display = 'block';
            
            // Generate results
            generateResults();
        }, 2000);
    }

    // Generate results
    function generateResults() {
        // Determine dominant style
        let dominantStyle = '';
        if (quizState.answers.styles.length > 0) {
            dominantStyle = quizState.answers.styles[0];
        } else {
            // Default to casual if no style selected
            dominantStyle = 'casual';
        }
        
        // Update style name
        const styleNames = {
            'casual': 'Casual Chic',
            'formal': 'Business Elegance',
            'streetwear': 'Urban Street Style',
            'vintage': 'Retro Vintage',
            'minimalist': 'Minimalist Chic',
            'bohemian': 'Bohemian Spirit'
        };
        
        styleName.textContent = styleNames[dominantStyle] || 'Personal Style';
        
        // Update style description
        const styleDescriptions = {
            'casual': 'Phong cách của bạn là sự kết hợp giữa sự thoải mái và tinh tế. Bạn thích những trang phục đơn giản, dễ phối nhưng vẫn toát lên vẻ chỉn chu và hiện đại.',
            'formal': 'Phong cách của bạn là sự kết hợp giữa sự chuyên nghiệp và thanh lịch. Bạn thích những trang phục lịch sự, trang trọng và luôn muốn tạo ấn tượng tốt với người đối diện.',
            'streetwear': 'Phong cách của bạn là sự kết hợp giữa sự năng động và cá tính. Bạn thích những trang phục mang đậm dấu ấn đường phố, thể hiện cá tính riêng và không ngại thử nghiệm.',
            'vintage': 'Phong cách của bạn là sự kết hợp giữa nét cổ điển và hiện đại. Bạn thích những trang phục mang hơi hướng hoài cổ, độc đáo và khác biệt với số đông.',
            'minimalist': 'Phong cách của bạn là sự kết hợp giữa sự đơn giản và tinh tế. Bạn thích những trang phục tối giản về màu sắc và thiết kế nhưng vẫn toát lên vẻ sang trọng và hiện đại.',
            'bohemian': 'Phong cách của bạn là sự kết hợp giữa sự tự do và nghệ thuật. Bạn thích những trang phục phóng khoáng, nhiều họa tiết và màu sắc, thể hiện tinh thần tự do và gần gũi với thiên nhiên.'
        };
        
        styleDescription.textContent = styleDescriptions[dominantStyle] || 'Phong cách của bạn là sự kết hợp độc đáo giữa nhiều yếu tố khác nhau, tạo nên một cá tính thời trang riêng biệt.';
        
        // Load product recommendations
        loadRecommendations(dominantStyle);
        
        // Load style tips
        loadStyleTips(dominantStyle);
    }

    // Load product recommendations
    function loadRecommendations(style) {
        recommendationGrid.innerHTML = '';
        
        const recommendations = productRecommendations[style] || productRecommendations['casual'];
        
        recommendations.forEach(product => {
            const productElement = document.createElement('div');
            productElement.className = 'recommendation-item';
            
            productElement.innerHTML = `
                <div class="recommendation-image">
                    <img src="${product.image}" alt="${product.name}">
                </div>
                <div class="recommendation-info">
                    <div class="recommendation-name">${product.name}</div>
                    <div class="recommendation-price">${product.price}</div>
                </div>
            `;
            
            recommendationGrid.appendChild(productElement);
        });
    }

    // Load style tips
    function loadStyleTips(style) {
        styleTipsList.innerHTML = '';
        
        const tips = styleTips[style] || styleTips['casual'];
        
        tips.forEach(tip => {
            const tipElement = document.createElement('li');
            tipElement.textContent = tip;
            styleTipsList.appendChild(tipElement);
        });
    }

    // Setup event listeners
    function setupEventListeners() {
        // Navigation buttons
        if (prevBtn) {
            prevBtn.addEventListener('click', prevStep);
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', nextStep);
        }
        
        // Height slider
        if (heightSlider && heightValue) {
            heightSlider.addEventListener('input', function() {
                const value = this.value;
                heightValue.textContent = value;
                quizState.answers.height = parseInt(value);
            });
        }
        
        // Weight slider
        if (weightSlider && weightValue) {
            weightSlider.addEventListener('input', function() {
                const value = this.value;
                weightValue.textContent = value;
                quizState.answers.weight = parseInt(value);
            });
        }
        
        // Option buttons (gender, age)
        optionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const value = this.dataset.value;
                const parent = this.parentElement;
                
                // Remove selected class from siblings
                parent.querySelectorAll('.option-btn').forEach(btn => {
                    btn.classList.remove('selected');
                });
                
                // Add selected class to clicked button
                this.classList.add('selected');
                
                // Update quiz state
                if (parent.previousElementSibling.textContent.includes('Giới tính')) {
                    quizState.answers.gender = value;
                } else if (parent.previousElementSibling.textContent.includes('Độ tuổi')) {
                    quizState.answers.age = value;
                }
            });
        });
        
        // Style options
        styleOptions.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                
                // Toggle selected class
                this.classList.toggle('selected');
                
                // Update quiz state
                if (this.classList.contains('selected')) {
                    // Add to array if not already included
                    if (!quizState.answers.styles.includes(value)) {
                        quizState.answers.styles.push(value);
                    }
                } else {
                    // Remove from array
                    quizState.answers.styles = quizState.answers.styles.filter(style => style !== value);
                }
            });
        });
        
        // Color options
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                
                // Toggle selected class
                this.classList.toggle('selected');
                
                // Update quiz state
                if (this.classList.contains('selected')) {
                    // Add to array if not already included
                    if (!quizState.answers.colors.includes(value)) {
                        quizState.answers.colors.push(value);
                    }
                } else {
                    // Remove from array
                    quizState.answers.colors = quizState.answers.colors.filter(color => color !== value);
                }
            });
        });
        
        // Occasion options
        occasionOptions.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                
                // Toggle selected class
                this.classList.toggle('selected');
                
                // Update quiz state
                if (this.classList.contains('selected')) {
                    // Add to array if not already included
                    if (!quizState.answers.occasions.includes(value)) {
                        quizState.answers.occasions.push(value);
                    }
                } else {
                    // Remove from array
                    quizState.answers.occasions = quizState.answers.occasions.filter(occasion => occasion !== value);
                }
            });
        });
        
        // Budget options
        budgetOptions.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                
                // Remove selected class from siblings
                budgetOptions.forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Update quiz state
                quizState.answers.budget = value;
            });
        });
        
        // Result action buttons
        if (saveResultsBtn) {
            saveResultsBtn.addEventListener('click', function() {
                showNotification('Đã lưu kết quả vào tài khoản của bạn!', 'success');
            });
        }
        
        if (shareResultsBtn) {
            shareResultsBtn.addEventListener('click', function() {
                showNotification('Đã sao chép đường dẫn chia sẻ!', 'success');
            });
        }
        
        if (shopRecommendationsBtn) {
            shopRecommendationsBtn.addEventListener('click', function() {
                window.location.href = '../womenproducts.html';
            });
        }
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        // Icon based on notification type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle notification-icon"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
                break;
            default:
                icon = '<i class="fas fa-info-circle notification-icon"></i>';
        }
        
        // Set notification content
        notification.innerHTML = `
            ${icon}
            <div class="notification-message">${message}</div>
        `;
        
        // Add notification to container
        container.appendChild(notification);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            
            // Remove from DOM after animation completes
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Initialize the app
    init();
});
