<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fashion Store | Thời trang cao cấp cho Nam và Nữ</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/product-card.css">
    <link rel="stylesheet" type="text/css" href="./styles/popup.css">
    <link rel="stylesheet" type="text/css" href="./styles/slider.css">
    <link rel="stylesheet" type="text/css" href="./styles/special-features.css">
    <link rel="stylesheet" type="text/css" href="./styles/membership.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Slick Slider CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>
    <link rel="stylesheet" type="text/css" href="./styles/slider.css"/>

    <style>
        /* Custom styles for index page */

        .hero-btn-secondary {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }

        .hero-btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
        }

        /* Categories Grid Styles */
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        .category-card {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .category-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .category-card:hover img {
            transform: scale(1.1);
        }

        .category-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 20px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
            color: white;
            text-align: center;
        }

        /* Section Title Styles */
        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            color: #333;
            font-weight: 700;
        }

        .section-title::after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #e74c3c;
            margin: 15px auto 0;
            border-radius: 2px;
        }

        /* Featured Products Styles */
        .featured-products {
            padding: 80px 0;
            background-color: #f9f9f9;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
        }

        .product-card {
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .product-image {
            position: relative;
            height: 280px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 2;
        }

        .new {
            background-color: #4CAF50;
            color: white;
        }

        .sale {
            background-color: #e74c3c;
            color: white;
        }

        .product-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
        }

        .product-card:hover .product-actions {
            opacity: 1;
            transform: translateX(0);
        }

        .product-action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .product-action-btn:hover {
            background-color: #e74c3c;
            color: white;
        }

        .product-info {
            padding: 20px;
        }

        .product-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            height: 48px;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .current-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #e74c3c;
        }

        .old-price {
            font-size: 0.9rem;
            color: #999;
            text-decoration: line-through;
        }

        .product-colors {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }

        .color-option {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .color-option:hover {
            transform: scale(1.2);
        }

        .add-to-cart-btn {
            width: 100%;
            padding: 12px 0;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-to-cart-btn:hover {
            background-color: #e74c3c;
        }

        .view-all-btn {
            display: block;
            width: 200px;
            margin: 40px auto 0;
            padding: 15px 0;
            background-color: transparent;
            color: #333;
            text-align: center;
            border: 2px solid #e74c3c;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .view-all-btn:hover {
            background-color: #e74c3c;
            color: white;
            transform: translateY(-5px);
        }

        /* Lookbook Styles */
        .lookbook {
            padding: 80px 0;
            background-color: #fff;
        }

        .lookbook-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 30px;
        }

        .lookbook-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            height: 400px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .lookbook-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .lookbook-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .lookbook-item:hover .lookbook-image {
            transform: scale(1.1);
        }

        .lookbook-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .lookbook-item:hover .lookbook-overlay {
            opacity: 1;
        }

        .lookbook-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            padding: 0 20px;
        }

        .lookbook-btn {
            padding: 12px 25px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 30px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .lookbook-btn:hover {
            background-color: #c0392b;
            transform: translateY(-3px);
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 992px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .lookbook-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .lookbook-item:last-child {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                height: 500px;
            }

            .hero-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .categories-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .lookbook-grid {
                grid-template-columns: 1fr;
            }

            .lookbook-item:last-child {
                grid-column: auto;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .categories-grid {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
        </div>
    </div>
    <!-- Hero Slider Section -->
    <section class="hero-slider">
        <div class="slide">
            <img src="./img banner/img banner 1.avif" alt="Banner 1">
            <div class="slide-content">
                <h1>Thời Trang Cao Cấp</h1>
                <p>Khám phá bộ sưu tập mới nhất với những thiết kế độc đáo và phong cách</p>
                <div class="slide-buttons">
                    <a href="./women-new.html" class="btn btn-primary">Thời trang nữ</a>
                    <a href="./men-new.html" class="btn btn-secondary">Thời trang nam</a>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="./img banner/banner 2.avif" alt="Banner 2">
            <div class="slide-content">
                <h1>Bộ Sưu Tập Mùa Hè 2024</h1>
                <p>Thoải mái và năng động với những thiết kế mới nhất cho mùa hè</p>
                <div class="slide-buttons">
                    <a href="./pages/sale.html" class="btn btn-primary">Khuyến mãi</a>
                    <a href="./womenproducts-new.html" class="btn btn-secondary">Xem ngay</a>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="./img banner/img banner 3.avif" alt="Banner 3">
            <div class="slide-content">
                <h1>Ưu Đãi Đặc Biệt</h1>
                <p>Giảm giá lên đến 50% cho tất cả sản phẩm trong tuần này</p>
                <div class="slide-buttons">
                    <a href="./pages/sale.html" class="btn btn-primary">Mua ngay</a>
                    <a href="./membership.html" class="btn btn-secondary">Thành viên</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Danh mục nổi bật</h2>
            <div class="categories-grid">
                <div class="category-card" data-aos="fade-up" data-aos-delay="100">
                    <img src="./img womens/women 15.webp" alt="Women's Fashion">
                    <div class="category-content">
                        <h3>Thời trang nữ</h3>
                        <a href="./women-new.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
                <div class="category-card" data-aos="fade-up" data-aos-delay="200">
                    <img src="./img mens/men 10.jpg" alt="Men's Fashion">
                    <div class="category-content">
                        <h3>Thời trang nam</h3>
                        <a href="./men-new.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
                <div class="category-card" data-aos="fade-up" data-aos-delay="300">
                    <img src="./img womens/women 12.webp" alt="Kids Fashion">
                    <div class="category-content">
                        <h3>Thời trang trẻ em</h3>
                        <a href="./pages/kids.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="featured-products">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Sản Phẩm Nổi Bật</h2>
            <div class="products-grid">
                <!-- Product 1 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="product-image">
                        <span class="product-badge new">Mới</span>
                        <img src="./img womens/img women 1.webp" alt="Thời trang công sở trẻ trung">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Thời trang công sở trẻ trung nhất</h3>
                        <div class="product-price">
                            <span class="current-price">350.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="product-image">
                        <span class="product-badge sale">-20%</span>
                        <img src="./img womens/img women 2.webp" alt="Bộ đồ thời trang mùa hè">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Bộ đồ thời trang mùa hè</h3>
                        <div class="product-price">
                            <span class="current-price">450.000đ</span>
                            <span class="old-price">560.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="product-image">
                        <img src="./img womens/women 3.webp" alt="Áo sơ mi thời trang phong cách Hồng Kông">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo sơ mi thời trang phong cách Hồng Kông</h3>
                        <div class="product-price">
                            <span class="current-price">380.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #f8f9fa;" title="Kem"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="product-image">
                        <img src="./img mens/men 4.webp" alt="Quần dài nam nữ">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Quần dài nam nữ túi hộp Form suông ống rộng</h3>
                        <div class="product-price">
                            <span class="current-price">520.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>
            </div>
            <a href="./womenproducts-new.html" class="view-all-btn" data-aos="fade-up">Xem tất cả sản phẩm</a>
        </div>
    </section>


    <!-- Lookbook Section -->
    <section class="lookbook">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Bộ Sưu Tập Mới</h2>
            <div class="lookbook-grid">
                <div class="lookbook-item" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lwjfhjfn23ojcb@resize_w450_nl.webp" alt="Summer Collection" class="lookbook-image">
                    <div class="lookbook-overlay">
                        <h3 class="lookbook-title">Bộ Sưu Tập Mùa Hè 2024</h3>
                        <a href="./womenproducts-new.html" class="lookbook-btn">Khám phá ngay</a>
                    </div>
                </div>
                <div class="lookbook-item" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lwavav435sd7dd@resize_w450_nl.webp" alt="Office Wear" class="lookbook-image">
                    <div class="lookbook-overlay">
                        <h3 class="lookbook-title">Thời Trang Công Sở</h3>
                        <a href="./womenproducts-new.html" class="lookbook-btn">Khám phá ngay</a>
                    </div>
                </div>
                <div class="lookbook-item" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvvcga0urf15f3@resize_w450_nl.webp" alt="Casual Collection" class="lookbook-image">
                    <div class="lookbook-overlay">
                        <h3 class="lookbook-title">Thời Trang Dạo Phố</h3>
                        <a href="./womenproducts-new.html" class="lookbook-btn">Khám phá ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

  <!-- Cart Debug Section -->
    <section class="cart-debug" id="cart-debug-section" style="display: none;">
        <div class="container">
            <div class="cart-debug-content">
                <h2>Công cụ quản lý giỏ hàng</h2>
                <p>Dùng để kiểm tra và sửa lỗi giỏ hàng</p>
                <div class="cart-debug-buttons">
                    <button id="clearCartBtn" class="btn-clear-cart">Xóa giỏ hàng</button>
                    <button id="createSampleCartBtn" class="btn-create-sample">Tạo giỏ hàng mẫu</button>
                    <button id="toggleCartDebugBtn" class="btn-toggle-debug">Ẩn công cụ</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
     <section class="newsletter">
        <div class="container">
            <div class="newsletter-content">
                <h2>Đăng ký nhận tin</h2>
                <p>Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
                <form class="newsletter-form">
                    <input type="email" placeholder="Địa chỉ email của bạn" required>
                    <button type="submit" class="btn-subscribe">Đăng ký</button>
                </form>
                <div class="debug-tools">
                    <button id="showCartDebugBtn" class="btn-show-debug">Công cụ giỏ hàng</button>
                </div>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Popup Overlays -->
    <!-- Newsletter Popup -->
    <div class="popup-overlay newsletter-popup" id="newsletter-popup">
        <div class="popup">
            <div class="popup-header">
                <h3>Đăng ký nhận tin</h3>
                <button class="popup-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="popup-body">
                <div class="popup-image-container">
                    <img src="./img banner/banner 2.avif" alt="Newsletter" class="popup-image">
                </div>
                <div class="popup-content">
                    <h3>Nhận ưu đãi đặc biệt</h3>
                    <p>Đăng ký nhận bản tin của chúng tôi để nhận thông tin về sản phẩm mới, khuyến mãi đặc biệt và các sự kiện độc quyền.</p>

                    <form class="newsletter-form">
                        <input type="email" placeholder="Email của bạn" required>
                        <button type="submit">Đăng ký ngay</button>
                    </form>

                    <div class="dont-show">
                        <input type="checkbox" id="dont-show-again">
                        <label for="dont-show-again">Không hiển thị lại</label>
                    </div>

                    <div class="benefits">
                        <div class="benefit-item">
                            <div class="benefit-icon"><i class="fas fa-tag"></i></div>
                            <div class="benefit-text">Ưu đãi độc quyền dành cho thành viên</div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon"><i class="fas fa-gift"></i></div>
                            <div class="benefit-text">Quà tặng sinh nhật đặc biệt</div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon"><i class="fas fa-bell"></i></div>
                            <div class="benefit-text">Thông báo sớm về các bộ sưu tập mới</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Popup -->
    <div class="popup-overlay login-popup" id="login-popup">
        <div class="popup">
            <div class="popup-header">
                <h3>Đăng nhập</h3>
                <button class="popup-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="popup-body">
                <div class="login-image">
                    <div class="login-image-content">
                        <h3>Chào mừng trở lại!</h3>
                        <p>Đăng nhập để trải nghiệm mua sắm tốt nhất và nhận nhiều ưu đãi hấp dẫn.</p>
                    </div>
                </div>
                <div class="login-form-container">
                    <form>
                        <div class="form-group">
                            <label for="login-email">Email</label>
                            <input type="email" id="login-email" placeholder="Nhập email của bạn" required>
                        </div>
                        <div class="form-group">
                            <label for="login-password">Mật khẩu</label>
                            <input type="password" id="login-password" placeholder="Nhập mật khẩu" required>
                            <a href="#" class="forgot-password">Quên mật khẩu?</a>
                        </div>
                        <button type="submit" class="login-btn">Đăng nhập</button>
                    </form>
                    <div class="social-login">
                        <p>Hoặc đăng nhập với</p>
                        <div class="social-buttons">
                            <a href="#" class="social-btn facebook"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-btn google"><i class="fab fa-google"></i></a>
                        </div>
                    </div>
                    <div class="register-link">
                        Chưa có tài khoản? <a href="./pages/register.html">Đăng ký ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick View Popup -->
    <div class="popup-overlay quickview-popup" id="quickview-popup">
        <div class="popup">
            <div class="popup-header">
                <h3>Xem nhanh sản phẩm</h3>
                <button class="popup-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="popup-body">
                <div class="product-image-container">
                    <div class="product-badges">
                        <div class="product-badge badge-new">Mới</div>
                        <div class="product-badge badge-sale">-20%</div>
                    </div>
                    <div class="product-actions">
                        <div class="product-action-btn" title="Phóng to"><i class="fas fa-search-plus"></i></div>
                        <div class="product-action-btn" title="Chia sẻ"><i class="fas fa-share-alt"></i></div>
                    </div>
                    <div class="product-image">
                        <img src="./img womens/women 15.webp" alt="Product" id="quickview-image">
                    </div>
                </div>
                <div class="product-details">
                    <h2 class="product-title" id="quickview-title">Áo sơ mi nữ dài tay</h2>

                    <div class="product-price-container">
                        <div class="product-price" id="quickview-price">350.000đ</div>
                        <div class="product-old-price">450.000đ</div>
                        <div class="product-discount">-20%</div>
                    </div>

                    <div class="product-rating">
                        <div class="rating-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                        <div class="rating-count">(120 đánh giá)</div>
                    </div>

                    <div class="product-description">
                        <p>Áo sơ mi nữ dài tay chất liệu cotton cao cấp, thiết kế hiện đại, phù hợp với nhiều phong cách khác nhau. Sản phẩm có độ bền cao và thoáng mát, thích hợp cho cả mùa hè và mùa thu.</p>
                    </div>

                    <div class="product-options">
                        <label class="option-label">Màu sắc:</label>
                        <div class="color-options">
                            <div class="color-option" style="background-color: black;" data-color="Đen"></div>
                            <div class="color-option" style="background-color: white; border: 1px solid #ddd;" data-color="Trắng"></div>
                            <div class="color-option" style="background-color: #0000FF;" data-color="Xanh dương"></div>
                            <div class="color-option" style="background-color: #FF0000;" data-color="Đỏ"></div>
                        </div>

                        <label class="option-label">Kích thước:</label>
                        <div class="size-options">
                            <div class="size-option" data-size="S">S</div>
                            <div class="size-option" data-size="M">M</div>
                            <div class="size-option" data-size="L">L</div>
                            <div class="size-option" data-size="XL">XL</div>
                            <div class="size-option" data-size="XXL">XXL</div>
                        </div>
                    </div>

                    <div class="product-meta">
                        <div class="meta-item">
                            <div class="meta-label">Mã sản phẩm:</div>
                            <div class="meta-value">SM-2024-001</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Danh mục:</div>
                            <div class="meta-value">Áo sơ mi, Thời trang nữ</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Tình trạng:</div>
                            <div class="meta-value">Còn hàng</div>
                        </div>
                    </div>

                    <div class="quantity-selector">
                        <button class="quantity-btn minus"><i class="fas fa-minus"></i></button>
                        <input type="text" class="quantity-input" value="1" readonly>
                        <button class="quantity-btn plus"><i class="fas fa-plus"></i></button>
                    </div>

                    <div class="product-actions-container">
                        <button class="add-to-cart"><i class="fas fa-shopping-cart"></i> Thêm vào giỏ hàng</button>
                        <button class="wishlist-btn"><i class="far fa-heart"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Quảng cáo Banner -->
    <!-- <section class="advertisement-banner">
        <div class="container">
            <div class="ad-banner-container">
                <div class="ad-banner" data-aos="fade-up">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-lj9lfzxvxnzr9d" alt="Quảng cáo thời trang">
                    <div class="ad-banner-content">
                        <h3>SALE LỚN - GIẢM ĐẾN 70%</h3>
                        <p>Chỉ trong tuần này - Số lượng có hạn</p>
                        <a href="./pages/sale.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- Quảng cáo Thương hiệu -->
    <!-- <section class="brands-advertisement">
        <div class="container">
            <h2 class="section-title">Thương hiệu nổi bật</h2>
            <div class="brands-slider" data-aos="fade-up">
                <div class="brand-item">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lnvbvl4rnlkn4d" alt="Thương hiệu 1">
                </div>
                <div class="brand-item">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lnvbvl4rnlkn4d" alt="Thương hiệu 2">
                </div>
                <div class="brand-item">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lnvbvl4rnlkn4d" alt="Thương hiệu 3">
                </div>
                <div class="brand-item">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lnvbvl4rnlkn4d" alt="Thương hiệu 4">
                </div>
                <div class="brand-item">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lnvbvl4rnlkn4d" alt="Thương hiệu 5">
                </div>
            </div>
        </div>
    </section> -->

    <section class="google-map">
        <div class="container">
            <h2 class="section-title">Địa chỉ cửa hàng</h2>
            <p>Bạn có thể ghé thăm trực tiếp tại đây:</p>
            <div class="map-embed">
                <!-- Dán iframe ở đây -->
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.600281784634!2d106.6622763!3d10.7652575!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f2bc7be6363%3A0xf83610cfe8f2ff88!2sMindX%20Technology%20School!5e0!3m2!1svi!2s!4v1740648946327!5m2!1svi!2s"
                    width="100%" height="400" style="border:0;"
                    allowfullscreen="" loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
        </div>

    </section>


    <iframe
    id="youtube-player"
    width="0" height="0"
    src="https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=1"
    frameborder="0"
    allow="autoplay; encrypted-media">
</iframe>

<!-- Chatbot Liên hệ -->
<div id="chat-bot">
    <div class="chat-header">💬 Liên hệ với chúng tôi <span id="chat-close">×</span></div>
    <div class="chat-body">
      <div class="chat-message bot">👋 Xin chào! Bạn cần hỗ trợ gì?</div>
      <input type="text" id="chat-input" placeholder="Nhập tin nhắn..." />
    </div>
  </div>

  <!-- Nút mở chat -->
  <div id="chat-toggle"><i class="fas fa-comments"></i></div>





<!-- Wheel Spin Popup -->
<div class="spin-popup hide-spin">
    <div class="spin-container">
      <span class="close-spin">&times;</span>
      <h2>🎉 Quay vòng may mắn để nhận mã giảm giá!</h2>
      <canvas id="wheel" width="300" height="300"></canvas>
      <button id="spin-btn">Quay ngay</button>
      <div id="spin-result"></div>
    </div>
  </div>




    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/popup.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/lucky-wheel.js"></script>
    <script src="./script/chatbot.js"></script>
    <script src="./script/reviews.js"></script>
    <script src="./script/smart-recommendations.js"></script>
    <script src="./script/smart-notifications.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>
    <script src="./script/social-sharing.js"></script>
    <script src="./script/product-alerts.js"></script>
    <script src="./script/user-display.js"></script>
    <script src="./script/marquee.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import footer from "./components/footer.js"

        // Custom navbar for index.html (different paths)
        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = `
            <!-- Top Announcement Bar -->
            <div class="announcement-bar">
                <p><i class="fas fa-truck"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ</p>
            </div>

            <!-- Main Navigation -->
            <nav class="main-nav">
                <div class="container">
                    <div class="nav-wrapper">
                        <!-- Mobile Menu Toggle -->
                        <div class="menu-toggle">
                            <i class="fas fa-bars"></i>
                        </div>

                        <!-- Logo -->
                        <div class="logo">
                            <a href="./index.html">
                                <img src="./img logo/logo 1.jpg" alt="Fashion Store Logo">
                            </a>
                        </div>

                        <!-- Navigation Menu -->
                        <ul class="nav-menu">
                            <li class="nav-item has-dropdown">
                                <a href="./women-new.html">Nữ <i class="fas fa-chevron-down"></i></a>
                                <div class="dropdown-menu">
                                    <div class="dropdown-column">
                                        <h3>Danh mục</h3>
                                        <ul>
                                            <li><a href="./womenproducts-new.html">Áo sơ mi</a></li>
                                            <li><a href="./womenproducts-new.html">Áo thun</a></li>
                                            <li><a href="./womenproducts-new.html">Quần jean</a></li>
                                            <li><a href="./womenproducts-new.html">Váy & Đầm</a></li>
                                            <li><a href="./womenproducts-new.html">Áo khoác</a></li>
                                            <li><a href="./womenproducts-new.html">Đồ thể thao</a></li>
                                            <li><a href="./womenproducts-new.html">Đồ ngủ</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column">
                                        <h3>Bộ sưu tập</h3>
                                        <ul>
                                            <li><a href="./womenproducts-new.html">Mùa hè 2024</a></li>
                                            <li><a href="./womenproducts-new.html">Công sở thanh lịch</a></li>
                                            <li><a href="./womenproducts-new.html">Dạo phố năng động</a></li>
                                            <li><a href="./womenproducts-new.html">Dự tiệc sang trọng</a></li>
                                            <li><a href="./sexy-women.html">Đồ sexy</a></li>
                                            <li><a href="./underwear-women.html">Nội y</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column dropdown-featured">
                                        <img src="./img womens/women 15.webp" alt="Women's Collection">
                                        <h4>Bộ sưu tập mới</h4>
                                        <a href="./womenproducts-new.html" class="btn-shop">Mua ngay</a>
                                    </div>
                                </div>
                            </li>
                            <li class="nav-item has-dropdown">
                                <a href="./men-new.html">Nam <i class="fas fa-chevron-down"></i></a>
                                <div class="dropdown-menu">
                                    <div class="dropdown-column">
                                        <h3>Danh mục</h3>
                                        <ul>
                                            <li><a href="./menproducts-new.html">Áo sơ mi</a></li>
                                            <li><a href="./menproducts-new.html">Áo thun</a></li>
                                            <li><a href="./menproducts-new.html">Quần jean</a></li>
                                            <li><a href="./menproducts-new.html">Quần kaki</a></li>
                                            <li><a href="./menproducts-new.html">Áo khoác</a></li>
                                            <li><a href="./menproducts-new.html">Đồ thể thao</a></li>
                                            <li><a href="./underwear-men.html">Nội y</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column">
                                        <h3>Bộ sưu tập</h3>
                                        <ul>
                                            <li><a href="./menproducts-new.html">Mùa hè 2024</a></li>
                                            <li><a href="./menproducts-new.html">Công sở lịch lãm</a></li>
                                            <li><a href="./menproducts-new.html">Thể thao năng động</a></li>
                                            <li><a href="./menproducts-new.html">Dạo phố cá tính</a></li>
                                            <li><a href="./menproducts-new.html">Hàng mới về</a></li>
                                            <li><a href="./menproducts-new.html">Bán chạy nhất</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column dropdown-featured">
                                        <img src="./img mens/men 10.jpg" alt="Men's Collection">
                                        <h4>Bộ sưu tập mới</h4>
                                        <a href="./menproducts-new.html" class="btn-shop">Mua ngay</a>
                                    </div>
                                </div>
                            </li>
                            <li class="nav-item has-dropdown">
                                <a href="./pages/kids.html">Trẻ em <i class="fas fa-chevron-down"></i></a>
                                <div class="dropdown-menu">
                                    <div class="dropdown-column">
                                        <h3>Bé gái</h3>
                                        <ul>
                                            <li><a href="./pages/kids.html?category=girls">Áo</a></li>
                                            <li><a href="./pages/kids.html?category=girls">Quần</a></li>
                                            <li><a href="./pages/kids.html?category=girls">Váy đầm</a></li>
                                            <li><a href="./pages/kids.html?category=girls">Đồ ngủ</a></li>
                                            <li><a href="./pages/kids.html?category=girls">Đồ thể thao</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column">
                                        <h3>Bé trai</h3>
                                        <ul>
                                            <li><a href="./pages/kids.html?category=boys">Áo</a></li>
                                            <li><a href="./pages/kids.html?category=boys">Quần</a></li>
                                            <li><a href="./pages/kids.html?category=boys">Đồ ngủ</a></li>
                                            <li><a href="./pages/kids.html?category=boys">Đồ thể thao</a></li>
                                            <li><a href="./pages/kids.html?category=boys">Bộ quần áo</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column dropdown-featured">
                                        <img src="./img womens/women 12.webp" alt="Kids Collection">
                                        <h4>Bộ sưu tập mới cho bé</h4>
                                        <a href="./pages/kids.html" class="btn-shop">Mua ngay</a>
                                    </div>
                                </div>
                            </li>
                            <li class="nav-item">
                                <a href="./pages/sale.html">Khuyến mãi</a>
                            </li>
                            <li class="nav-item">
                                <a href="./blog.html">Blog</a>
                            </li>
                            <li class="nav-item">
                                <a href="./about.html">Giới thiệu</a>
                            </li>
                            <li class="nav-item has-dropdown">
                                <a href="#">Trang khác <i class="fas fa-chevron-down"></i></a>
                                <div class="dropdown-menu">
                                    <div class="dropdown-column">
                                        <h3>Thông tin</h3>
                                        <ul>
                                            <li><a href="./pages/contact.html">Liên hệ</a></li>
                                            <li><a href="./pages/faq.html">Câu hỏi thường gặp</a></li>
                                            <li><a href="./pages/stores.html">Hệ thống cửa hàng</a></li>
                                            <li><a href="./pages/careers.html">Tuyển dụng</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column">
                                        <h3>Dịch vụ</h3>
                                        <ul>
                                            <li><a href="./pages/shipping.html">Vận chuyển</a></li>
                                            <li><a href="./pages/returns.html">Đổi trả</a></li>
                                            <li><a href="./pages/size-guide.html">Hướng dẫn chọn size</a></li>
                                            <li><a href="./order-tracking.html">Theo dõi đơn hàng</a></li>
                                            <li><a href="./pages/payment.html">Thanh toán</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-column">
                                        <h3>Tài khoản</h3>
                                        <ul>
                                            <li><a href="./pages/login.html">Đăng nhập</a></li>
                                            <li><a href="./pages/register.html">Đăng ký</a></li>
                                            <li><a href="./pages/account.html">Tài khoản của tôi</a></li>
                                            <li><a href="./pages/wishlist.html">Danh sách yêu thích</a></li>
                                            <li><a href="./AddCart.html">Giỏ hàng</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </li>
                        </ul>

                        <!-- Right Navigation -->
                        <div class="nav-right">
                            <div class="search-box">
                                <input type="text" id="search-input" placeholder="Tìm kiếm...">
                                <button id="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <div class="nav-icons">
                                <a href="./pages/account.html" class="nav-icon" title="Tài khoản">
                                    <i class="fas fa-user"></i>
                                </a>
                                <a href="./pages/wishlist.html" class="nav-icon wishlist-icon" title="Yêu thích">
                                    <i class="fas fa-heart"></i>
                                    <span class="wishlist-count">0</span>
                                </a>
                                <a href="./AddCart.html" class="nav-icon cart-icon" title="Giỏ hàng">
                                    <i class="fas fa-shopping-bag"></i>
                                    <span class="cart-count">0</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        `;

        let footerbox = document.getElementById("footerbox");
        if (footerbox) {
            footerbox.innerHTML = footer();
        }
    </script>

    <script>
        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Thêm class fade-in cho các phần tử cần animation
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.classList.add('fade-in');
            });

            const categoryCards = document.querySelectorAll('.category-card');
            categoryCards.forEach((card, index) => {
                card.setAttribute('data-aos', 'fade-up');
                card.setAttribute('data-aos-delay', (index * 100).toString());
            });

            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach((card, index) => {
                card.setAttribute('data-aos', 'fade-up');
                card.setAttribute('data-aos-delay', (index * 50).toString());
            });

            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach((item, index) => {
                item.setAttribute('data-aos', 'fade-up');
                item.setAttribute('data-aos-delay', (index * 100).toString());
            });
        });






        const chatToggle = document.getElementById('chat-toggle');
  const chatBot = document.getElementById('chat-bot');
  const chatClose = document.getElementById('chat-close');
  const chatInput = document.getElementById('chat-input');
  const chatBody = document.querySelector('.chat-body');

  chatToggle.addEventListener('click', () => {
    chatBot.style.display = 'flex';
    chatToggle.style.display = 'none';
  });

  chatClose.addEventListener('click', () => {
    chatBot.style.display = 'none';
    chatToggle.style.display = 'block';
  });

  chatInput.addEventListener('keypress', function (e) {
    if (e.key === 'Enter' && chatInput.value.trim() !== '') {
      const userMsg = document.createElement('div');
      userMsg.className = 'chat-message';
      userMsg.textContent = chatInput.value;
      chatBody.insertBefore(userMsg, chatInput);

      // Giả lập phản hồi
      const botReply = document.createElement('div');
      botReply.className = 'chat-message bot';
      botReply.textContent = 'Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm!';
      setTimeout(() => {
        chatBody.insertBefore(botReply, chatInput);
      }, 1000);

      chatInput.value = '';
    }
  });





  document.addEventListener("click", function() {
        let youtubePlayer = document.getElementById("youtube-player");
        if (youtubePlayer.src.includes("mute=1")) {
            youtubePlayer.src = "https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=0";
            console.log("✅ Đã bật tiếng cho YouTube!");
        }
    }, { once: true }); // Chỉ chạy một lần

    // Xử lý các nút debug giỏ hàng
    document.addEventListener('DOMContentLoaded', function() {
        // Hiển thị số lượng sản phẩm trong giỏ hàng
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = cart.length;
        }

        // Xử lý nút hiển thị công cụ debug
        const showCartDebugBtn = document.getElementById('showCartDebugBtn');
        const cartDebugSection = document.getElementById('cart-debug-section');
        const toggleCartDebugBtn = document.getElementById('toggleCartDebugBtn');

        if (showCartDebugBtn && cartDebugSection && toggleCartDebugBtn) {
            showCartDebugBtn.addEventListener('click', function() {
                cartDebugSection.style.display = 'block';
                showCartDebugBtn.style.display = 'none';
            });

            toggleCartDebugBtn.addEventListener('click', function() {
                cartDebugSection.style.display = 'none';
                showCartDebugBtn.style.display = 'block';
            });
        }

        // Xử lý nút xóa giỏ hàng
        const clearCartBtn = document.getElementById('clearCartBtn');
        if (clearCartBtn) {
            clearCartBtn.addEventListener('click', function() {
                if (confirm('Bạn có chắc chắn muốn xóa toàn bộ giỏ hàng?')) {
                    localStorage.removeItem('cart');
                    localStorage.removeItem('cart_total');
                    localStorage.removeItem('cart_final');
                    localStorage.removeItem('cart_discount');
                    localStorage.removeItem('promo_code');
                    localStorage.removeItem('discount_percent');

                    // Cập nhật số lượng giỏ hàng
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        cartCount.textContent = '0';
                    }

                    // Hiển thị thông báo
                    alert('Đã xóa toàn bộ giỏ hàng');
                }
            });
        }

        // Xử lý nút tạo giỏ hàng mẫu
        const createSampleCartBtn = document.getElementById('createSampleCartBtn');
        if (createSampleCartBtn) {
            createSampleCartBtn.addEventListener('click', function() {
                // Tạo giỏ hàng mẫu
                const sampleCart = [
                    {
                        id: 'sample-1',
                        name: 'Áo sơ mi nữ dài tay',
                        price: '350000đ',
                        image: './img womens/women 15.webp',
                        image1: './img womens/women 15.webp',
                        quantity: 1
                    },
                    {
                        id: 'sample-2',
                        name: 'Quần Jean Đen Nam Ống Rộng',
                        price: '450000đ',
                        image: './img mens/men 5.webp',
                        image1: './img mens/men 5.webp',
                        quantity: 2
                    },
                    {
                        id: 'sample-3',
                        name: 'Váy đầm công chúa cho bé gái',
                        price: '320000đ',
                        image: './img womens/women 11.webp',
                        image1: './img womens/women 11.webp',
                        quantity: 1
                    }
                ];

                // Lưu giỏ hàng mẫu vào localStorage
                localStorage.setItem('cart', JSON.stringify(sampleCart));

                // Cập nhật số lượng giỏ hàng
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = sampleCart.length;
                }

                // Hiển thị thông báo
                alert('Đã tạo giỏ hàng mẫu với 3 sản phẩm');
            });
        }
    });


    </script>
</body>
</html>