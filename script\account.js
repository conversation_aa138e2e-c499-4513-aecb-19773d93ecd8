// Account Page Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const isLoggedIn = user && user.isLoggedIn;

    // Get elements
    const notLoggedInSection = document.getElementById('not-logged-in');
    const dashboardSection = document.getElementById('dashboard-section');
    const userNameElement = document.getElementById('user-name');
    const userStatusElement = document.getElementById('user-status');
    const dashboardNameElement = document.getElementById('dashboard-name');
    const logoutButton = document.getElementById('logout-btn');

    // Update UI based on login status
    if (isLoggedIn) {
        // User is logged in
        if (notLoggedInSection) notLoggedInSection.style.display = 'none';
        if (dashboardSection) dashboardSection.style.display = 'block';

        // Update user info
        if (userNameElement) userNameElement.textContent = user.name || 'Người dùng';
        if (userStatusElement) userStatusElement.textContent = 'Đã đăng nhập';
        if (dashboardNameElement) dashboardNameElement.textContent = user.name || 'Người dùng';

        // Update cart count
        updateCartCount();

        // Load user profile data
        loadUserProfile();

        // Load user addresses
        loadUserAddresses();

        // Load login history
        loadLoginHistory();

        // Check if we need to show a specific section (from menu navigation)
        const activeSection = localStorage.getItem('activeAccountSection');
        if (activeSection) {
            // Find the section to show
            const sectionToShow = document.getElementById(`${activeSection}`);
            if (sectionToShow) {
                // Hide all sections
                const sections = document.querySelectorAll('.account-section');
                sections.forEach(section => {
                    section.style.display = 'none';
                });

                // Show the selected section
                sectionToShow.style.display = 'block';

                // Update active menu item
                const menuItems = document.querySelectorAll('.account-menu-item');
                menuItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.getAttribute('href') && item.getAttribute('href').includes(activeSection)) {
                        item.classList.add('active');
                    }
                });

                // Scroll to the section
                sectionToShow.scrollIntoView({ behavior: 'smooth' });

                // Clear the active section from localStorage
                localStorage.removeItem('activeAccountSection');
            }
        }
    } else {
        // User is not logged in
        if (notLoggedInSection) notLoggedInSection.style.display = 'flex';
        if (dashboardSection) dashboardSection.style.display = 'none';

        // Update user info
        if (userNameElement) userNameElement.textContent = 'Khách';
        if (userStatusElement) userStatusElement.textContent = 'Bạn chưa đăng nhập';
    }

    // Handle logout
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Add logout record to login history
            addLoginHistoryRecord(false);

            // Update user data
            user.isLoggedIn = false;
            localStorage.setItem('user', JSON.stringify(user));

            // Show notification
            showNotification('Đăng xuất thành công!', 'success');

            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);
        });
    }

    // Handle account menu navigation
    const menuItems = document.querySelectorAll('.account-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Skip for logout button
            if (this.id === 'logout-btn') return;

            e.preventDefault();

            // Remove active class from all items
            menuItems.forEach(i => i.classList.remove('active'));

            // Add active class to clicked item
            this.classList.add('active');

            // Get section ID from href
            const sectionId = this.querySelector('a').getAttribute('href').substring(1);

            // Hide all sections
            const sections = document.querySelectorAll('.account-section');
            sections.forEach(section => {
                section.style.display = 'none';
            });

            // Show selected section
            const selectedSection = document.getElementById(`${sectionId}-section`);
            if (selectedSection) {
                selectedSection.style.display = 'block';
            }
        });
    });

    // Profile Form Submission
    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const name = document.getElementById('profile-name').value;
            const phone = document.getElementById('profile-phone').value;
            const gender = document.getElementById('profile-gender').value;
            const dob = document.getElementById('profile-dob').value;

            // Update user data
            user.name = name;
            user.phone = phone;
            user.gender = gender;
            user.dob = dob;

            // Save to localStorage
            localStorage.setItem('user', JSON.stringify(user));

            // Update UI
            if (userNameElement) userNameElement.textContent = name || 'Người dùng';
            if (dashboardNameElement) dashboardNameElement.textContent = name || 'Người dùng';

            // Show notification
            showNotification('Thông tin cá nhân đã được cập nhật!', 'success');
        });
    }

    // Avatar Upload
    const changeAvatarBtn = document.getElementById('change-avatar-btn');
    const avatarUpload = document.getElementById('avatar-upload');
    const avatarContainer = document.querySelector('.avatar-container');

    if (changeAvatarBtn && avatarUpload && avatarContainer) {
        // Click on avatar container also triggers file upload
        avatarContainer.addEventListener('click', function() {
            avatarUpload.click();
        });

        // Click on change avatar button triggers file upload
        changeAvatarBtn.addEventListener('click', function() {
            avatarUpload.click();
        });

        // Handle file upload
        avatarUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update avatar image
                    document.getElementById('user-avatar').src = e.target.result;

                    // Save to localStorage
                    user.avatar = e.target.result;
                    localStorage.setItem('user', JSON.stringify(user));

                    // Show notification
                    showNotification('Ảnh đại diện đã được cập nhật!', 'success');
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Address Management
    const addAddressBtn = document.getElementById('add-address-btn');
    const addressModal = document.getElementById('address-modal');
    const addressForm = document.getElementById('address-form');
    const modalClose = document.querySelector('.modal-close');
    const modalCancel = document.querySelector('.modal-cancel');

    if (addAddressBtn && addressModal) {
        // Open modal when clicking add address button
        addAddressBtn.addEventListener('click', function() {
            // Reset form
            if (addressForm) addressForm.reset();

            // Set modal title
            document.getElementById('address-modal-title').textContent = 'Thêm địa chỉ mới';

            // Clear address ID
            document.getElementById('address-id').value = '';

            // Show modal
            addressModal.classList.add('show');
        });

        // Close modal when clicking close button
        if (modalClose) {
            modalClose.addEventListener('click', function() {
                addressModal.classList.remove('show');
            });
        }

        // Close modal when clicking cancel button
        if (modalCancel) {
            modalCancel.addEventListener('click', function() {
                addressModal.classList.remove('show');
            });
        }

        // Handle form submission
        if (addressForm) {
            addressForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const addressId = document.getElementById('address-id').value;
                const name = document.getElementById('address-name').value;
                const phone = document.getElementById('address-phone').value;
                const province = document.getElementById('address-province').value;
                const district = document.getElementById('address-district').value;
                const ward = document.getElementById('address-ward').value;
                const detail = document.getElementById('address-detail').value;
                const isDefault = document.getElementById('address-default').checked;

                // Create address object
                const address = {
                    id: addressId || Date.now().toString(),
                    name,
                    phone,
                    province,
                    district,
                    ward,
                    detail,
                    isDefault
                };

                // Get existing addresses
                const addresses = user.addresses || [];

                // If this is set as default, remove default from other addresses
                if (isDefault) {
                    addresses.forEach(addr => {
                        addr.isDefault = false;
                    });
                }

                // Check if this is an edit or a new address
                const existingIndex = addresses.findIndex(addr => addr.id === addressId);
                if (existingIndex !== -1) {
                    // Update existing address
                    addresses[existingIndex] = address;
                } else {
                    // Add new address
                    addresses.push(address);
                }

                // Save to localStorage
                user.addresses = addresses;
                localStorage.setItem('user', JSON.stringify(user));

                // Update UI
                renderAddresses(addresses);

                // Close modal
                addressModal.classList.remove('show');

                // Show notification
                showNotification('Địa chỉ đã được lưu!', 'success');
            });
        }
    }

    // Password Management
    const passwordForm = document.getElementById('password-form');
    const newPasswordInput = document.getElementById('new-password');
    const confirmPasswordInput = document.getElementById('confirm-password');
    const togglePasswordBtns = document.querySelectorAll('.toggle-password');

    // Toggle password visibility
    if (togglePasswordBtns) {
        togglePasswordBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const targetInput = document.getElementById(targetId);

                if (targetInput.type === 'password') {
                    targetInput.type = 'text';
                    this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    targetInput.type = 'password';
                    this.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });
    }

    // Password strength meter
    if (newPasswordInput) {
        newPasswordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);

            // Update strength meter
            const strengthMeter = document.querySelector('.strength-meter-fill');
            const strengthText = document.querySelector('.strength-text');

            if (strengthMeter && strengthText) {
                strengthMeter.setAttribute('data-strength', strength);

                let strengthLabel = 'Yếu';
                switch (strength) {
                    case 1:
                        strengthLabel = 'Yếu';
                        break;
                    case 2:
                        strengthLabel = 'Trung bình';
                        break;
                    case 3:
                        strengthLabel = 'Khá';
                        break;
                    case 4:
                        strengthLabel = 'Mạnh';
                        break;
                    default:
                        strengthLabel = 'Rất yếu';
                }

                strengthText.textContent = `Độ mạnh: ${strengthLabel}`;
            }
        });
    }

    // Password form submission
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // Validate current password
            if (currentPassword !== user.password) {
                showNotification('Mật khẩu hiện tại không đúng!', 'error');
                return;
            }

            // Validate new password
            if (newPassword !== confirmPassword) {
                showNotification('Mật khẩu mới không khớp!', 'error');
                return;
            }

            // Check password strength
            const strength = calculatePasswordStrength(newPassword);
            if (strength < 2) {
                showNotification('Mật khẩu mới quá yếu! Vui lòng chọn mật khẩu mạnh hơn.', 'warning');
                return;
            }

            // Update user data
            user.password = newPassword;

            // Add password change to login history
            addLoginHistoryRecord(true, 'Đổi mật khẩu');

            // Save to localStorage
            localStorage.setItem('user', JSON.stringify(user));

            // Reset form
            this.reset();

            // Reset strength meter
            const strengthMeter = document.querySelector('.strength-meter-fill');
            const strengthText = document.querySelector('.strength-text');

            if (strengthMeter && strengthText) {
                strengthMeter.setAttribute('data-strength', '0');
                strengthText.textContent = 'Độ mạnh: Yếu';
            }

            // Show notification
            showNotification('Mật khẩu đã được thay đổi!', 'success');
        });
    }

    // Security Options
    const lockAccountBtn = document.getElementById('lock-account-btn');
    const logoutAllBtn = document.getElementById('logout-all-btn');

    if (lockAccountBtn) {
        lockAccountBtn.addEventListener('click', function() {
            if (confirm('Bạn có chắc chắn muốn khóa tài khoản? Bạn sẽ không thể đăng nhập cho đến khi liên hệ với bộ phận hỗ trợ để mở khóa.')) {
                // Update user data
                user.isLocked = true;
                user.isLoggedIn = false;

                // Add lock account to login history
                addLoginHistoryRecord(false, 'Khóa tài khoản');

                // Save to localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Show notification
                showNotification('Tài khoản đã bị khóa!', 'warning');

                // Redirect to login page after a short delay
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        });
    }

    if (logoutAllBtn) {
        logoutAllBtn.addEventListener('click', function() {
            if (confirm('Bạn có chắc chắn muốn đăng xuất khỏi tất cả các thiết bị?')) {
                // Update user data
                user.isLoggedIn = false;
                user.sessions = [];

                // Add logout all to login history
                addLoginHistoryRecord(false, 'Đăng xuất tất cả thiết bị');

                // Save to localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Show notification
                showNotification('Đã đăng xuất khỏi tất cả các thiết bị!', 'success');

                // Redirect to login page after a short delay
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        });
    }

    // Login History Filtering
    const filterHistoryBtn = document.getElementById('filter-history-btn');

    if (filterHistoryBtn) {
        filterHistoryBtn.addEventListener('click', function() {
            const fromDate = document.getElementById('history-from').value;
            const toDate = document.getElementById('history-to').value;

            // Load filtered login history
            loadLoginHistory(fromDate, toDate);
        });
    }
});

// Load user profile data
function loadUserProfile() {
    const user = JSON.parse(localStorage.getItem('user')) || {};

    // Set profile form values
    const nameInput = document.getElementById('profile-name');
    const emailInput = document.getElementById('profile-email');
    const phoneInput = document.getElementById('profile-phone');
    const genderSelect = document.getElementById('profile-gender');
    const dobInput = document.getElementById('profile-dob');
    const avatarImg = document.getElementById('user-avatar');

    if (nameInput) nameInput.value = user.name || '';
    if (emailInput) emailInput.value = user.email || '';
    if (phoneInput) phoneInput.value = user.phone || '';
    if (genderSelect) genderSelect.value = user.gender || '';
    if (dobInput) dobInput.value = user.dob || '';

    // Set avatar image
    if (avatarImg && user.avatar) {
        avatarImg.src = user.avatar;
    }
}

// Load user addresses
function loadUserAddresses() {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const addresses = user.addresses || [];

    // Render addresses
    renderAddresses(addresses);

    // Load provinces for address form
    loadProvinces();
}

// Render addresses
function renderAddresses(addresses) {
    const addressList = document.getElementById('address-list');
    if (!addressList) return;

    // Clear address list
    addressList.innerHTML = '';

    if (addresses.length === 0) {
        // Show empty message
        addressList.innerHTML = `
            <div class="empty-address">
                <p>Bạn chưa có địa chỉ nào</p>
            </div>
        `;
        return;
    }

    // Render each address
    addresses.forEach(address => {
        const addressItem = document.createElement('div');
        addressItem.className = `address-item${address.isDefault ? ' default' : ''}`;

        // Create address HTML
        addressItem.innerHTML = `
            ${address.isDefault ? '<div class="default-badge">Mặc định</div>' : ''}
            <div class="address-name">${address.name}</div>
            <div class="address-phone">${address.phone}</div>
            <div class="address-full">
                ${address.detail}, ${address.ward}, ${address.district}, ${address.province}
            </div>
            <div class="address-actions">
                <button class="btn btn-outline edit-address-btn" data-id="${address.id}">Sửa</button>
                <button class="btn btn-outline delete-address-btn" data-id="${address.id}">Xóa</button>
                ${!address.isDefault ? `<button class="btn btn-outline set-default-btn" data-id="${address.id}">Đặt làm mặc định</button>` : ''}
            </div>
        `;

        // Add to address list
        addressList.appendChild(addressItem);
    });

    // Add event listeners for address actions
    addAddressEventListeners();
}

// Add event listeners for address actions
function addAddressEventListeners() {
    // Edit address buttons
    const editButtons = document.querySelectorAll('.edit-address-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const addressId = this.getAttribute('data-id');
            editAddress(addressId);
        });
    });

    // Delete address buttons
    const deleteButtons = document.querySelectorAll('.delete-address-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const addressId = this.getAttribute('data-id');
            deleteAddress(addressId);
        });
    });

    // Set default address buttons
    const defaultButtons = document.querySelectorAll('.set-default-btn');
    defaultButtons.forEach(button => {
        button.addEventListener('click', function() {
            const addressId = this.getAttribute('data-id');
            setDefaultAddress(addressId);
        });
    });
}

// Edit address
function editAddress(addressId) {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const addresses = user.addresses || [];
    const address = addresses.find(addr => addr.id === addressId);

    if (!address) return;

    // Set form values
    document.getElementById('address-id').value = address.id;
    document.getElementById('address-name').value = address.name;
    document.getElementById('address-phone').value = address.phone;
    document.getElementById('address-province').value = address.province;

    // Load districts based on province
    loadDistricts(address.province, address.district);

    // Load wards based on district
    loadWards(address.district, address.ward);

    document.getElementById('address-detail').value = address.detail;
    document.getElementById('address-default').checked = address.isDefault;

    // Set modal title
    document.getElementById('address-modal-title').textContent = 'Sửa địa chỉ';

    // Show modal
    document.getElementById('address-modal').classList.add('show');
}

// Delete address
function deleteAddress(addressId) {
    if (!confirm('Bạn có chắc chắn muốn xóa địa chỉ này?')) return;

    const user = JSON.parse(localStorage.getItem('user')) || {};
    let addresses = user.addresses || [];

    // Remove address
    addresses = addresses.filter(addr => addr.id !== addressId);

    // Save to localStorage
    user.addresses = addresses;
    localStorage.setItem('user', JSON.stringify(user));

    // Update UI
    renderAddresses(addresses);

    // Show notification
    showNotification('Địa chỉ đã được xóa!', 'success');
}

// Set default address
function setDefaultAddress(addressId) {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const addresses = user.addresses || [];

    // Update default status
    addresses.forEach(addr => {
        addr.isDefault = addr.id === addressId;
    });

    // Save to localStorage
    user.addresses = addresses;
    localStorage.setItem('user', JSON.stringify(user));

    // Update UI
    renderAddresses(addresses);

    // Show notification
    showNotification('Đã đặt làm địa chỉ mặc định!', 'success');
}

// Load provinces
function loadProvinces() {
    const provinceSelect = document.getElementById('address-province');
    if (!provinceSelect) return;

    // Clear options
    provinceSelect.innerHTML = '<option value="">Chọn Tỉnh/Thành phố</option>';

    // Add provinces
    const provinces = [
        'Hà Nội', 'TP. Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
        'An Giang', 'Bà Rịa - Vũng Tàu', 'Bắc Giang', 'Bắc Kạn', 'Bạc Liêu',
        'Bắc Ninh', 'Bến Tre', 'Bình Định', 'Bình Dương', 'Bình Phước',
        'Bình Thuận', 'Cà Mau', 'Cao Bằng', 'Đắk Lắk', 'Đắk Nông',
        'Điện Biên', 'Đồng Nai', 'Đồng Tháp', 'Gia Lai', 'Hà Giang',
        'Hà Nam', 'Hà Tĩnh', 'Hải Dương', 'Hậu Giang', 'Hòa Bình',
        'Hưng Yên', 'Khánh Hòa', 'Kiên Giang', 'Kon Tum', 'Lai Châu',
        'Lâm Đồng', 'Lạng Sơn', 'Lào Cai', 'Long An', 'Nam Định',
        'Nghệ An', 'Ninh Bình', 'Ninh Thuận', 'Phú Thọ', 'Phú Yên',
        'Quảng Bình', 'Quảng Nam', 'Quảng Ngãi', 'Quảng Ninh', 'Quảng Trị',
        'Sóc Trăng', 'Sơn La', 'Tây Ninh', 'Thái Bình', 'Thái Nguyên',
        'Thanh Hóa', 'Thừa Thiên Huế', 'Tiền Giang', 'Trà Vinh', 'Tuyên Quang',
        'Vĩnh Long', 'Vĩnh Phúc', 'Yên Bái'
    ];

    provinces.forEach(province => {
        const option = document.createElement('option');
        option.value = province;
        option.textContent = province;
        provinceSelect.appendChild(option);
    });

    // Add change event listener
    provinceSelect.addEventListener('change', function() {
        loadDistricts(this.value);
    });
}

// Load districts
function loadDistricts(province, selectedDistrict = '') {
    const districtSelect = document.getElementById('address-district');
    if (!districtSelect) return;

    // Clear options
    districtSelect.innerHTML = '<option value="">Chọn Quận/Huyện</option>';

    if (!province) return;

    // Add districts (simplified example)
    const districts = {
        'Hà Nội': ['Ba Đình', 'Hoàn Kiếm', 'Hai Bà Trưng', 'Đống Đa', 'Tây Hồ', 'Cầu Giấy', 'Thanh Xuân', 'Hoàng Mai', 'Long Biên', 'Bắc Từ Liêm', 'Nam Từ Liêm', 'Hà Đông'],
        'TP. Hồ Chí Minh': ['Quận 1', 'Quận 3', 'Quận 4', 'Quận 5', 'Quận 6', 'Quận 7', 'Quận 8', 'Quận 10', 'Quận 11', 'Quận 12', 'Bình Tân', 'Bình Thạnh', 'Gò Vấp', 'Phú Nhuận', 'Tân Bình', 'Tân Phú', 'Thủ Đức'],
        'Đà Nẵng': ['Hải Châu', 'Thanh Khê', 'Sơn Trà', 'Ngũ Hành Sơn', 'Liên Chiểu', 'Cẩm Lệ'],
        // Add more districts for other provinces as needed
    };

    // Default districts for other provinces
    const defaultDistricts = ['Quận/Huyện 1', 'Quận/Huyện 2', 'Quận/Huyện 3', 'Quận/Huyện 4', 'Quận/Huyện 5'];

    // Get districts for selected province
    const provinceDistricts = districts[province] || defaultDistricts;

    // Add districts to select
    provinceDistricts.forEach(district => {
        const option = document.createElement('option');
        option.value = district;
        option.textContent = district;
        districtSelect.appendChild(option);
    });

    // Set selected district if provided
    if (selectedDistrict) {
        districtSelect.value = selectedDistrict;
    }

    // Add change event listener
    districtSelect.addEventListener('change', function() {
        loadWards(this.value);
    });
}

// Load wards
function loadWards(district, selectedWard = '') {
    const wardSelect = document.getElementById('address-ward');
    if (!wardSelect) return;

    // Clear options
    wardSelect.innerHTML = '<option value="">Chọn Phường/Xã</option>';

    if (!district) return;

    // Add wards (simplified example)
    const wards = ['Phường/Xã 1', 'Phường/Xã 2', 'Phường/Xã 3', 'Phường/Xã 4', 'Phường/Xã 5'];

    // Add wards to select
    wards.forEach(ward => {
        const option = document.createElement('option');
        option.value = ward;
        option.textContent = ward;
        wardSelect.appendChild(option);
    });

    // Set selected ward if provided
    if (selectedWard) {
        wardSelect.value = selectedWard;
    }
}

// Load login history
function loadLoginHistory(fromDate = '', toDate = '') {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    let loginHistory = user.loginHistory || [];

    // Filter by date if provided
    if (fromDate || toDate) {
        loginHistory = loginHistory.filter(record => {
            const recordDate = new Date(record.timestamp);

            if (fromDate && toDate) {
                return recordDate >= new Date(fromDate) && recordDate <= new Date(toDate);
            } else if (fromDate) {
                return recordDate >= new Date(fromDate);
            } else if (toDate) {
                return recordDate <= new Date(toDate);
            }

            return true;
        });
    }

    // Sort by timestamp (newest first)
    loginHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Render login history
    renderLoginHistory(loginHistory);
}

// Render login history
function renderLoginHistory(loginHistory) {
    const historyBody = document.getElementById('login-history-body');
    if (!historyBody) return;

    // Clear history body
    historyBody.innerHTML = '';

    if (loginHistory.length === 0) {
        // Show empty message
        historyBody.innerHTML = `
            <tr>
                <td colspan="5" class="empty-history">Không có dữ liệu đăng nhập</td>
            </tr>
        `;
        return;
    }

    // Render each record
    loginHistory.forEach(record => {
        const row = document.createElement('tr');

        // Format date
        const date = new Date(record.timestamp);
        const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;

        // Create row HTML
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${record.device || 'Không xác định'}</td>
            <td>${record.browser || 'Không xác định'}</td>
            <td>${record.ip || 'Không xác định'}</td>
            <td class="${record.success ? 'status-success' : 'status-failed'}">${record.action || (record.success ? 'Đăng nhập thành công' : 'Đăng xuất')}</td>
        `;

        // Add to history body
        historyBody.appendChild(row);
    });
}

// Add login history record
function addLoginHistoryRecord(success, action = '') {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const loginHistory = user.loginHistory || [];

    // Create record
    const record = {
        timestamp: new Date().toISOString(),
        device: getDeviceInfo(),
        browser: getBrowserInfo(),
        ip: '127.0.0.1', // Placeholder IP
        success,
        action
    };

    // Add to history
    loginHistory.push(record);

    // Limit history to 50 records
    if (loginHistory.length > 50) {
        loginHistory.shift();
    }

    // Save to localStorage
    user.loginHistory = loginHistory;
    localStorage.setItem('user', JSON.stringify(user));
}

// Get device info
function getDeviceInfo() {
    const userAgent = navigator.userAgent;

    if (/Android/i.test(userAgent)) {
        return 'Android';
    } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
        return 'iOS';
    } else if (/Windows/i.test(userAgent)) {
        return 'Windows';
    } else if (/Mac/i.test(userAgent)) {
        return 'Mac';
    } else if (/Linux/i.test(userAgent)) {
        return 'Linux';
    } else {
        return 'Không xác định';
    }
}

// Get browser info
function getBrowserInfo() {
    const userAgent = navigator.userAgent;

    if (/Chrome/i.test(userAgent)) {
        return 'Chrome';
    } else if (/Firefox/i.test(userAgent)) {
        return 'Firefox';
    } else if (/Safari/i.test(userAgent)) {
        return 'Safari';
    } else if (/Edge/i.test(userAgent)) {
        return 'Edge';
    } else if (/Opera|OPR/i.test(userAgent)) {
        return 'Opera';
    } else if (/MSIE|Trident/i.test(userAgent)) {
        return 'Internet Explorer';
    } else {
        return 'Không xác định';
    }
}

// Calculate password strength
function calculatePasswordStrength(password) {
    if (!password) return 0;

    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;

    // Character type checks
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^a-zA-Z0-9]/.test(password)) strength += 1;

    // Return strength level (0-4)
    return Math.min(4, Math.floor(strength / 2));
}

// Update cart count
function updateCartCount() {
    const cartCountElement = document.querySelector('.cart-count');
    if (!cartCountElement) return;

    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Update cart count
    cartCountElement.textContent = cart.length;
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
