/**
 * <PERSON><PERSON><PERSON> thị thông báo đẹp mắt
 * @param {string} message - <PERSON>ội dung thông báo
 * @param {string} type - <PERSON><PERSON><PERSON> thông báo: 'success', 'error', 'warning', 'info'
 * @param {number} duration - Th<PERSON><PERSON> gian hiển thị (ms)
 */
function showNotification(message, type = 'info', duration = 3000) {
    // Tạo container nếu chưa có
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    
    // Tạo thông báo
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                       type === 'error' ? '#F44336' :
                                       type === 'warning' ? '#FF9800' : '#2196F3';
    notification.style.color = '#fff';
    notification.style.padding = '15px 20px';
    notification.style.marginBottom = '10px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(50px)';
    notification.style.transition = 'opacity 0.3s, transform 0.3s';
    
    // Icon dựa trên loại thông báo
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
    }
    
    // Nội dung thông báo
    notification.innerHTML = `${icon} ${message}`;
    
    // Thêm vào container
    container.appendChild(notification);
    
    // Hiển thị thông báo với animation
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Xóa thông báo sau thời gian đã định
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(50px)';
        
        // Xóa khỏi DOM sau khi animation hoàn tất
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);
}

// Thêm vào global scope
window.showNotification = showNotification;
