<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/size-guide.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />

    <title>Hướng dẫn chọn size | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Size Guide Content -->
    <div class="size-guide-container">
        <div class="container">
            <div class="page-header" data-aos="fade-up">
                <h1>Hướng dẫn chọn size</h1>
                <p>Tìm size phù hợp nhất cho bạn với bảng hướng dẫn chi tiết của chúng tôi</p>
            </div>

            <div class="size-guide-content" data-aos="fade-up" data-aos-delay="200">
                <!-- Size Guide Navigation -->
                <div class="size-guide-nav">
                    <button class="size-nav-btn active" data-category="women">Nữ</button>
                    <button class="size-nav-btn" data-category="men">Nam</button>
                    <button class="size-nav-btn" data-category="kids">Trẻ em</button>
                </div>

                <!-- How to Measure -->
                <div class="how-to-measure" data-aos="fade-up">
                    <h2>Cách đo kích thước</h2>
                    <p>Để chọn được size phù hợp nhất, bạn nên đo các số đo cơ thể theo hướng dẫn dưới đây:</p>

                    <div class="measure-guide">
                        <div class="measure-image">
                            <img src="./img/measure-guide.jpg" alt="Hướng dẫn đo kích thước">
                        </div>
                        <div class="measure-steps">
                            <div class="measure-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h3>Vòng ngực</h3>
                                    <p>Đo vòng ngực tại điểm rộng nhất, giữ thước đo ngang và song song với mặt đất.</p>
                                </div>
                            </div>
                            <div class="measure-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h3>Vòng eo</h3>
                                    <p>Đo vòng eo tại điểm nhỏ nhất, thường là khoảng 2-3cm trên rốn.</p>
                                </div>
                            </div>
                            <div class="measure-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h3>Vòng hông</h3>
                                    <p>Đo vòng hông tại điểm rộng nhất, thường là khoảng 20cm dưới vòng eo.</p>
                                </div>
                            </div>
                            <div class="measure-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h3>Chiều dài chân</h3>
                                    <p>Đo từ đáy chân đến mắt cá chân để xác định chiều dài quần.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Size Tables -->
                <div class="size-tables-container">
                    <!-- Women's Size Tables -->
                    <div class="size-category active" id="women-sizes" data-aos="fade-up">
                        <h2>Bảng size nữ</h2>

                        <div class="size-table-section">
                            <h3>Áo</h3>
                            <div class="size-table-wrapper">
                                <table class="size-table">
                                    <thead>
                                        <tr>
                                            <th>Size</th>
                                            <th>VN/EU</th>
                                            <th>US</th>
                                            <th>UK</th>
                                            <th>Vòng ngực (cm)</th>
                                            <th>Vòng eo (cm)</th>
                                            <th>Vòng hông (cm)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>XS</td>
                                            <td>34</td>
                                            <td>0-2</td>
                                            <td>4-6</td>
                                            <td>76-81</td>
                                            <td>58-63</td>
                                            <td>84-89</td>
                                        </tr>
                                        <tr>
                                            <td>S</td>
                                            <td>36</td>
                                            <td>4-6</td>
                                            <td>8-10</td>
                                            <td>82-87</td>
                                            <td>64-69</td>
                                            <td>90-95</td>
                                        </tr>
                                        <tr>
                                            <td>M</td>
                                            <td>38</td>
                                            <td>8-10</td>
                                            <td>12-14</td>
                                            <td>88-93</td>
                                            <td>70-75</td>
                                            <td>96-101</td>
                                        </tr>
                                        <tr>
                                            <td>L</td>
                                            <td>40</td>
                                            <td>12-14</td>
                                            <td>16-18</td>
                                            <td>94-99</td>
                                            <td>76-81</td>
                                            <td>102-107</td>
                                        </tr>
                                        <tr>
                                            <td>XL</td>
                                            <td>42</td>
                                            <td>16-18</td>
                                            <td>20-22</td>
                                            <td>100-105</td>
                                            <td>82-87</td>
                                            <td>108-113</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="size-table-section">
                            <h3>Quần & Váy</h3>
                            <div class="size-table-wrapper">
                                <table class="size-table">
                                    <thead>
                                        <tr>
                                            <th>Size</th>
                                            <th>VN/EU</th>
                                            <th>US</th>
                                            <th>UK</th>
                                            <th>Vòng eo (cm)</th>
                                            <th>Vòng hông (cm)</th>
                                            <th>Chiều dài chân (cm)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>XS</td>
                                            <td>34</td>
                                            <td>0-2</td>
                                            <td>4-6</td>
                                            <td>58-63</td>
                                            <td>84-89</td>
                                            <td>75-78</td>
                                        </tr>
                                        <tr>
                                            <td>S</td>
                                            <td>36</td>
                                            <td>4-6</td>
                                            <td>8-10</td>
                                            <td>64-69</td>
                                            <td>90-95</td>
                                            <td>78-80</td>
                                        </tr>
                                        <tr>
                                            <td>M</td>
                                            <td>38</td>
                                            <td>8-10</td>
                                            <td>12-14</td>
                                            <td>70-75</td>
                                            <td>96-101</td>
                                            <td>80-83</td>
                                        </tr>
                                        <tr>
                                            <td>L</td>
                                            <td>40</td>
                                            <td>12-14</td>
                                            <td>16-18</td>
                                            <td>76-81</td>
                                            <td>102-107</td>
                                            <td>83-85</td>
                                        </tr>
                                        <tr>
                                            <td>XL</td>
                                            <td>42</td>
                                            <td>16-18</td>
                                            <td>20-22</td>
                                            <td>82-87</td>
                                            <td>108-113</td>
                                            <td>85-88</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>
    <script>
        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Xử lý chuyển đổi tab size
        document.addEventListener('DOMContentLoaded', function() {
            const sizeNavBtns = document.querySelectorAll('.size-nav-btn');

            sizeNavBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Xóa active class từ tất cả các nút
                    sizeNavBtns.forEach(b => b.classList.remove('active'));

                    // Thêm active class cho nút được click
                    this.classList.add('active');

                    // Hiển thị bảng size tương ứng
                    const category = this.dataset.category;

                    // Trong demo này, chỉ hiển thị bảng size nữ
                    // Trong thực tế, bạn sẽ hiển thị bảng size tương ứng
                    showNotification(`Đã chọn bảng size ${category === 'women' ? 'nữ' : category === 'men' ? 'nam' : 'trẻ em'}`, 'info');
                });
            });

            // Cập nhật số lượng giỏ hàng
            let cart = JSON.parse(localStorage.getItem("cart")) || [];
            let countBag = document.querySelector(".cart-count");
            if (countBag) {
                countBag.textContent = cart.length;
            }
        });
    </script>
</body>
</html>
