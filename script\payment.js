// Payment Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Payment method selection
    const paymentOptions = document.querySelectorAll('.payment-option');
    const paymentForms = document.querySelectorAll('.payment-form');

    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            paymentOptions.forEach(opt => opt.classList.remove('active'));

            // Add active class to clicked option
            this.classList.add('active');

            // Hide all payment forms
            paymentForms.forEach(form => form.classList.remove('active'));

            // Show the selected payment form
            const method = this.getAttribute('data-method');
            document.getElementById(`${method}-payment`).classList.add('active');
        });
    });

    // Credit card form functionality
    const cardNumberInput = document.getElementById('card-number');
    const cardHolderInput = document.getElementById('card-holder');
    const expiryDateInput = document.getElementById('expiry-date');
    const cvvInput = document.getElementById('cvv');

    const cardNumberDisplay = document.querySelector('.card-number');
    const cardHolderDisplay = document.querySelector('.card-holder .value');
    const expiryDateDisplay = document.querySelector('.card-expires .value');
    const cvvDisplay = document.querySelector('.card-cvv .value');

    const cardPreview = document.querySelector('.card-preview');

    // Format card number input (add spaces every 4 digits)
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            let formattedValue = '';

            for (let i = 0; i < value.length; i++) {
                if (i > 0 && i % 4 === 0) {
                    formattedValue += ' ';
                }
                formattedValue += value[i];
            }

            e.target.value = formattedValue;

            // Update card preview
            updateCardNumberDisplay(value);

            // Detect card type
            detectCardType(value);
        });
    }

    // Update card holder name
    if (cardHolderInput) {
        cardHolderInput.addEventListener('input', function(e) {
            const value = e.target.value.toUpperCase();
            cardHolderDisplay.textContent = value || 'TÊN CHỦ THẺ';
        });
    }

    // Format expiry date (MM/YY)
    if (expiryDateInput) {
        expiryDateInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');

            if (value.length > 0) {
                value = value.substring(0, 4);
                const month = parseInt(value.substring(0, 2), 10);

                if (value.length > 2) {
                    value = (month > 12 ? '12' : value.substring(0, 2)) + '/' + value.substring(2);
                } else {
                    value = (month > 12 ? '12' : value);
                }
            }

            e.target.value = value;
            expiryDateDisplay.textContent = value || 'MM/YY';
        });
    }

    // Handle CVV input
    if (cvvInput) {
        cvvInput.addEventListener('focus', function() {
            cardPreview.classList.add('flipped');
        });

        cvvInput.addEventListener('blur', function() {
            cardPreview.classList.remove('flipped');
        });

        cvvInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
            cvvDisplay.textContent = value || 'XXX';
        });
    }

    // Update card number display
    function updateCardNumberDisplay(value) {
        const groups = [];
        for (let i = 0; i < 4; i++) {
            const start = i * 4;
            const group = value.substring(start, start + 4);
            groups.push(group.padEnd(4, 'X'));
        }

        const numberGroups = cardNumberDisplay.querySelectorAll('.number-group');
        numberGroups.forEach((group, index) => {
            group.textContent = groups[index];
        });
    }

    // Detect card type based on first digits
    function detectCardType(number) {
        const visaLogo = document.querySelector('.card-logo.visa');
        const mastercardLogo = document.querySelector('.card-logo.mastercard');
        const jcbLogo = document.querySelector('.card-logo.jcb');

        // Reset all logos
        [visaLogo, mastercardLogo, jcbLogo].forEach(logo => {
            logo.classList.remove('active');
        });

        // Detect card type
        if (/^4/.test(number)) {
            visaLogo.classList.add('active');
        } else if (/^5[1-5]/.test(number)) {
            mastercardLogo.classList.add('active');
        } else if (/^35/.test(number)) {
            jcbLogo.classList.add('active');
        } else if (number.length > 0) {
            visaLogo.classList.add('active'); // Default to Visa
        }
    }

    // E-wallet selection
    const ewalletItems = document.querySelectorAll('.ewallet-item');

    ewalletItems.forEach(item => {
        item.addEventListener('click', function() {
            ewalletItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');

            // In a real implementation, this would update the QR code and instructions
            const walletName = this.querySelector('span').textContent;
            const qrInstructions = document.querySelector('.qr-instructions');

            if (qrInstructions) {
                qrInstructions.innerHTML = `
                    <p>1. Mở ứng dụng ${walletName} trên điện thoại</p>
                    <p>2. Quét mã QR</p>
                    <p>3. Xác nhận thanh toán</p>
                `;
            }
        });
    });

    // Copy to clipboard functionality
    const copyButtons = document.querySelectorAll('.copy-btn');

    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const textToCopy = this.getAttribute('data-clipboard-text');

            // Create a temporary input element
            const tempInput = document.createElement('input');
            tempInput.value = textToCopy;
            document.body.appendChild(tempInput);

            // Select and copy the text
            tempInput.select();
            document.execCommand('copy');

            // Remove the temporary element
            document.body.removeChild(tempInput);

            // Show notification
            showNotification('Đã sao chép vào clipboard', 'success');

            // Change button icon temporarily
            const originalIcon = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check"></i>';

            setTimeout(() => {
                this.innerHTML = originalIcon;
            }, 2000);
        });
    });

    // Complete payment button
    const completePaymentBtn = document.querySelector('.btn-complete-payment');

    if (completePaymentBtn) {
        completePaymentBtn.addEventListener('click', function() {
            // Get active payment method
            const activeMethod = document.querySelector('.payment-option.active').getAttribute('data-method');

            // Validate form based on payment method
            let isValid = true;
            let message = '';

            if (activeMethod === 'card') {
                // Validate credit card form
                if (!cardNumberInput.value || cardNumberInput.value.replace(/\s/g, '').length < 16) {
                    isValid = false;
                    message = 'Vui lòng nhập số thẻ hợp lệ';
                } else if (!cardHolderInput.value) {
                    isValid = false;
                    message = 'Vui lòng nhập tên chủ thẻ';
                } else if (!expiryDateInput.value || expiryDateInput.value.length < 5) {
                    isValid = false;
                    message = 'Vui lòng nhập ngày hết hạn hợp lệ';
                } else if (!cvvInput.value || cvvInput.value.length < 3) {
                    isValid = false;
                    message = 'Vui lòng nhập mã CVV hợp lệ';
                }
            }

            if (isValid) {
                // Show loading state
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';

                // Simulate payment processing
                setTimeout(() => {
                    showNotification('Thanh toán thành công!', 'success');

                    // Save order to localStorage
                    saveOrder();

                    // Clear cart
                    localStorage.setItem('cart', JSON.stringify([]));

                    // Redirect to success page
                    setTimeout(() => {
                        window.location.href = './success.html';
                    }, 1500);
                }, 2000);
            } else {
                showNotification(message, 'error');
            }
        });
    }

    // Save order to localStorage
    function saveOrder() {
        // Get cart items
        const cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Lấy giá trị từ localStorage để đảm bảo tính nhất quán
        const cartTotal = parseInt(localStorage.getItem('cart_total') || 0);
        const cartDiscount = parseInt(localStorage.getItem('cart_discount') || 0);
        const cartFinal = parseInt(localStorage.getItem('cart_final') || cartTotal);

        // Phí vận chuyển mặc định
        const shipping = 0; // Miễn phí vận chuyển

        // Create order object
        const order = {
            id: 'ORD' + Date.now(),
            date: new Date().toISOString(),
            items: cart,
            subtotal: cartTotal,
            shipping: shipping,
            discount: cartDiscount,
            total: cartFinal,
            status: 'Đang xử lý',
            paymentMethod: document.querySelector('.payment-option.active').getAttribute('data-method'),
            shippingAddress: JSON.parse(localStorage.getItem('shippingAddress')) || {}
        };

        // Get existing orders
        const orders = JSON.parse(localStorage.getItem('orders')) || [];

        // Add new order
        orders.push(order);

        // Save orders
        localStorage.setItem('orders', JSON.stringify(orders));
    }

    // Load order summary from localStorage
    function loadOrderSummary() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Log raw values from localStorage for debugging
        console.log('Raw values from localStorage:');
        console.log('- cart_total:', localStorage.getItem('cart_total'));
        console.log('- cart_discount:', localStorage.getItem('cart_discount'));
        console.log('- cart_final:', localStorage.getItem('cart_final'));

        // Tính tổng tiền từ giỏ hàng nếu không có trong localStorage
        let calculatedTotal = 0;
        if (cart.length > 0) {
            cart.forEach(item => {
                let itemPrice = 0;
                if (typeof item.price === 'string') {
                    // Loại bỏ tất cả các ký tự không phải số
                    itemPrice = parseInt(item.price.replace(/[^\d]/g, '')) || 0;
                } else if (typeof item.price === 'number') {
                    itemPrice = item.price;
                }
                const quantity = item.quantity || 1;
                calculatedTotal += itemPrice * quantity;
            });
            console.log('Calculated total from cart items:', calculatedTotal);
        }

        if (cart.length > 0) {
            // Lấy giá trị từ localStorage để đảm bảo tính nhất quán
            // Nếu không có trong localStorage, sử dụng giá trị tính toán từ giỏ hàng
            const cartTotal = parseInt(localStorage.getItem('cart_total')) || calculatedTotal;
            const cartDiscount = parseInt(localStorage.getItem('cart_discount')) || 0;
            const cartFinal = parseInt(localStorage.getItem('cart_final')) || cartTotal;

            // Phí vận chuyển mặc định
            const shipping = 0; // Miễn phí vận chuyển

            // Update summary display
            const subtotalElement = document.getElementById('subtotal');
            const shippingElement = document.getElementById('shipping');
            const discountElement = document.getElementById('discount');
            const totalElement = document.getElementById('total');

            if (subtotalElement) subtotalElement.textContent = cartTotal.toLocaleString() + 'đ';
            if (shippingElement) shippingElement.textContent = shipping > 0 ? shipping.toLocaleString() + 'đ' : 'Miễn phí';
            if (discountElement) discountElement.textContent = cartDiscount > 0 ? '-' + cartDiscount.toLocaleString() + 'đ' : '0đ';
            if (totalElement) totalElement.textContent = cartFinal.toLocaleString() + 'đ';

            console.log('Loaded order summary from localStorage:');
            console.log('- Subtotal:', cartTotal.toLocaleString() + 'đ');
            console.log('- Discount:', cartDiscount.toLocaleString() + 'đ');
            console.log('- Final Total:', cartFinal.toLocaleString() + 'đ');

            // Lưu lại giá trị để đảm bảo tính nhất quán
            localStorage.setItem('cart_total', cartTotal.toString());
            localStorage.setItem('cart_discount', cartDiscount.toString());
            localStorage.setItem('cart_final', cartFinal.toString());
        } else {
            console.warn('Cart is empty, redirecting to cart page');
            window.location.href = './AddCart.html';
        }
    }

    // Initialize
    // Thêm debug để xác định vấn đề
    console.log('payment.js: Khởi tạo trang thanh toán');
    console.log('payment.js: Giỏ hàng:', JSON.parse(localStorage.getItem('cart')));
    console.log('payment.js: Giá trị raw từ localStorage:');
    console.log('- cart_total:', localStorage.getItem('cart_total'));
    console.log('- cart_discount:', localStorage.getItem('cart_discount'));
    console.log('- cart_final:', localStorage.getItem('cart_final'));

    // Tính toán tổng tiền từ giỏ hàng để kiểm tra
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    let calculatedTotal = 0;

    if (cart.length > 0) {
        cart.forEach(item => {
            let itemPrice = 0;
            if (typeof item.price === 'string') {
                itemPrice = parseInt(item.price.replace(/[^\d]/g, '')) || 0;
            } else if (typeof item.price === 'number') {
                itemPrice = item.price;
            }
            const quantity = item.quantity || 1;
            calculatedTotal += itemPrice * quantity;
        });
    }

    console.log('payment.js: Tổng tiền tính toán từ giỏ hàng:', calculatedTotal);

    // Lưu lại giá trị tính toán nếu không có trong localStorage
    if (!localStorage.getItem('cart_total')) {
        localStorage.setItem('cart_total', calculatedTotal.toString());
        localStorage.setItem('cart_final', calculatedTotal.toString());
        console.log('payment.js: Đã lưu giá trị tính toán vào localStorage');
    }

    // Tải thông tin đơn hàng
    loadOrderSummary();
});
