const express = require('express');
const router = express.Router();
const products = require('../data/products');

// Get all products
router.get('/', (req, res) => {
  const { category, subcategory, search, sort, limit } = req.query;
  
  let filteredProducts = [...products];
  
  // Filter by category
  if (category) {
    filteredProducts = filteredProducts.filter(product => 
      product.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  // Filter by subcategory
  if (subcategory) {
    filteredProducts = filteredProducts.filter(product => 
      product.subcategory.toLowerCase() === subcategory.toLowerCase()
    );
  }
  
  // Search by name
  if (search) {
    filteredProducts = filteredProducts.filter(product => 
      product.name.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  // Sort products
  if (sort) {
    switch (sort) {
      case 'price-asc':
        filteredProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filteredProducts.sort((a, b) => b.price - a.price);
        break;
      case 'newest':
        filteredProducts.sort((a, b) => b.isNew - a.isNew);
        break;
      case 'bestseller':
        filteredProducts.sort((a, b) => b.isBestSeller - a.isBestSeller);
        break;
      case 'rating':
        filteredProducts.sort((a, b) => b.rating - a.rating);
        break;
      default:
        break;
    }
  }
  
  // Limit results
  if (limit) {
    filteredProducts = filteredProducts.slice(0, parseInt(limit));
  }
  
  res.json({
    status: 'success',
    results: filteredProducts.length,
    data: filteredProducts
  });
});

// Get product by ID
router.get('/:id', (req, res) => {
  const product = products.find(p => p.id === req.params.id);
  
  if (!product) {
    return res.status(404).json({
      status: 'fail',
      message: 'Product not found'
    });
  }
  
  res.json({
    status: 'success',
    data: product
  });
});

// Get related products
router.get('/:id/related', (req, res) => {
  const product = products.find(p => p.id === req.params.id);
  
  if (!product) {
    return res.status(404).json({
      status: 'fail',
      message: 'Product not found'
    });
  }
  
  // Find products in the same category
  const relatedProducts = products
    .filter(p => p.category === product.category && p.id !== product.id)
    .slice(0, 4);
  
  res.json({
    status: 'success',
    results: relatedProducts.length,
    data: relatedProducts
  });
});

module.exports = router;
