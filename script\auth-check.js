/**
 * Authentication Check Script
 * This script checks if the user is logged in before accessing protected pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Pages that require authentication
    const protectedPages = [
        'AddCart.html',
        'checkout.html',
        'confirm.html',
        'success.html',
        'account.html',
        'orders.html',
        'wishlist.html'
    ];

    // Get current page filename
    const currentPage = window.location.pathname.split('/').pop();

    // Check if current page is protected
    if (protectedPages.includes(currentPage)) {
        checkAuthentication();
    }

    // Add event listeners to cart and checkout links
    const cartLinks = document.querySelectorAll('.cart-link, .checkout-link, a[href="AddCart.html"], a[href="checkout.html"]');
    cartLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Check if user is logged in
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user || !user.isLoggedIn) {
                e.preventDefault();
                showLoginPrompt();
                return false;
            }
        });
    });

    // Add event listener to cart icon in header
    const cartIcon = document.querySelector('.nav-icon[title="Giỏ hàng"]');
    if (cartIcon) {
        cartIcon.addEventListener('click', function(e) {
            // Check if user is logged in
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user || !user.isLoggedIn) {
                e.preventDefault();
                showLoginPrompt();
                return false;
            }
        });
    }

    // Event listener for user icon is now handled by setupUsernameClickEvent()

    // Display username if logged in
    displayUsername();

    // Update cart count
    updateCartCount();

    // Update wishlist count
    updateWishlistCount();

    // Add click event to username in navbar
    setupUsernameClickEvent();
});

/**
 * Check if user is authenticated
 * If not, redirect to login page
 */
function checkAuthentication() {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user || !user.isLoggedIn) {
        // Save current URL to redirect back after login
        localStorage.setItem('redirectAfterLogin', window.location.href);

        // Show notification
        showNotification('Vui lòng đăng nhập để tiếp tục', 'warning');

        // Redirect to login page after a short delay
        setTimeout(() => {
            window.location.href = './pages/login.html';
        }, 2000);
    }
}

/**
 * Show login prompt popup
 */
function showLoginPrompt() {
    // Create login prompt container
    const loginPrompt = document.createElement('div');
    loginPrompt.className = 'login-prompt-overlay';

    loginPrompt.innerHTML = `
        <div class="login-prompt">
            <div class="login-prompt-header">
                <h3>Đăng nhập để tiếp tục</h3>
                <button class="login-prompt-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="login-prompt-body">
                <p>Bạn cần đăng nhập để xem giỏ hàng và thanh toán.</p>
                <div class="login-prompt-buttons">
                    <a href="./pages/login.html" class="btn-login">Đăng nhập</a>
                    <a href="./pages/register.html" class="btn-register">Đăng ký</a>
                </div>
            </div>
        </div>
    `;

    // Add to body
    document.body.appendChild(loginPrompt);

    // Show with animation
    setTimeout(() => {
        loginPrompt.classList.add('show');
    }, 10);

    // Close button event
    const closeButton = loginPrompt.querySelector('.login-prompt-close');
    closeButton.addEventListener('click', function() {
        loginPrompt.classList.remove('show');
        setTimeout(() => {
            loginPrompt.remove();
        }, 300);
    });

    // Save current URL to redirect back after login
    localStorage.setItem('redirectAfterLogin', window.location.href);
}

/**
 * Display username if logged in
 */
function displayUsername() {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const isLoggedIn = user && user.isLoggedIn;

    const userIcon = document.querySelector('.nav-icon[title="Tài khoản"]');
    if (!userIcon) return;

    if (isLoggedIn) {
        // Lấy tên người dùng
        let userName = user.name || (user.email ? user.email.split('@')[0] : 'Người dùng');

        // Giới hạn độ dài tên hiển thị
        if (userName.length > 8) {
            userName = userName.substring(0, 8) + '...';
        }

        // Tạo avatar với chữ cái đầu
        const firstLetter = (user.name || user.email || 'U').charAt(0).toUpperCase();

        // Thay đổi icon thành avatar và tên người dùng
        userIcon.innerHTML = `
            <div class="user-avatar">${firstLetter}</div>
            <span class="user-name">${userName}</span>
        `;
        userIcon.title = `Xin chào, ${user.name || (user.email ? user.email.split('@')[0] : 'Người dùng')}`;
        userIcon.classList.add('logged-in');

        // Thêm data attribute để xác định đã đăng nhập
        userIcon.setAttribute('data-logged-in', 'true');
    } else {
        // Người dùng chưa đăng nhập, đảm bảo hiển thị icon mặc định
        userIcon.innerHTML = `<i class="fas fa-user"></i>`;
        userIcon.title = "Tài khoản";
        userIcon.classList.remove('logged-in');
        userIcon.removeAttribute('data-logged-in');
    }
}

/**
 * Setup click event for username in navbar
 */
function setupUsernameClickEvent() {
    // Tìm phần tử tên người dùng trong navbar
    const userIcon = document.querySelector('.nav-icon[title="Tài khoản"]');

    if (userIcon) {
        userIcon.addEventListener('click', function(e) {
            e.preventDefault();

            // Kiểm tra xem người dùng đã đăng nhập chưa
            const user = JSON.parse(localStorage.getItem('user')) || {};
            const isLoggedIn = user && user.isLoggedIn;

            if (isLoggedIn) {
                // Người dùng đã đăng nhập, hiển thị menu tài khoản
                showAccountMenu(this);
            } else {
                // Người dùng chưa đăng nhập, chuyển hướng đến trang đăng nhập
                const isInPagesFolder = window.location.pathname.includes('/pages/');
                const loginUrl = isInPagesFolder ? 'login.html' : './pages/login.html';
                window.location.href = loginUrl;
            }

            return false;
        });
    }
}

/**
 * Update cart count
 */
function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        cartCount.textContent = cart.length;
    }
}

/**
 * Update wishlist count
 */
function updateWishlistCount() {
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    const wishlistCount = document.querySelector('.wishlist-count');
    if (wishlistCount) {
        wishlistCount.textContent = wishlist.length;
    }
}

/**
 * Show account menu
 */
function showAccountMenu(element) {
    // Remove existing menu if any
    const existingMenu = document.querySelector('.account-menu');
    if (existingMenu) {
        existingMenu.remove();
        return; // Nếu menu đã hiển thị, đóng menu và không hiển thị lại
    }

    // Create account menu
    const accountMenu = document.createElement('div');
    accountMenu.className = 'account-menu';

    // Get user data
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const userName = user.name || (user.email ? user.email.split('@')[0] : 'Người dùng');

    // Determine correct paths based on current location
    const isInPagesFolder = window.location.pathname.includes('/pages/');
    const accountPath = isInPagesFolder ? 'account.html' : './pages/account.html';
    const ordersPath = isInPagesFolder ? 'orders.html' : './pages/orders.html';
    const wishlistPath = isInPagesFolder ? 'wishlist.html' : './pages/wishlist.html';
    const cartPath = isInPagesFolder ? '../AddCart.html' : './AddCart.html';

    // Set menu content
    accountMenu.innerHTML = `
        <div class="account-menu-header">
            <div class="account-menu-user">
                <div class="account-menu-avatar">
                    ${user.avatar ? `<img src="${user.avatar}" alt="${userName}">` : `<i class="fas fa-user-circle"></i>`}
                </div>
                <div class="account-menu-info">
                    <div class="account-menu-name">${userName}</div>
                    <div class="account-menu-email">${user.email || 'Chưa cập nhật email'}</div>
                </div>
            </div>
        </div>
        <div class="account-menu-body">
            <a href="${accountPath}" class="account-menu-item">
                <i class="fas fa-user"></i> Tài khoản của tôi
            </a>
            <a href="${ordersPath}" class="account-menu-item">
                <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
            </a>
            <a href="${wishlistPath}" class="account-menu-item">
                <i class="fas fa-heart"></i> Danh sách yêu thích
            </a>
            <a href="${cartPath}" class="account-menu-item">
                <i class="fas fa-shopping-cart"></i> Giỏ hàng của tôi
            </a>
            <a href="${accountPath}#profile-section" class="account-menu-item">
                <i class="fas fa-edit"></i> Chỉnh sửa thông tin
            </a>
            <a href="${accountPath}#security-section" class="account-menu-item">
                <i class="fas fa-lock"></i> Bảo mật tài khoản
            </a>
            <div class="account-menu-divider"></div>
            <a href="#" class="account-menu-item logout-btn">
                <i class="fas fa-sign-out-alt"></i> Đăng xuất
            </a>
        </div>
    `;

    // Position menu
    const rect = element.getBoundingClientRect();
    accountMenu.style.top = `${rect.bottom + window.scrollY}px`;
    accountMenu.style.right = `${window.innerWidth - rect.right}px`;

    // Add to body
    document.body.appendChild(accountMenu);

    // Show with animation
    setTimeout(() => {
        accountMenu.classList.add('show');
    }, 10);

    // Add event listener to logout button
    const logoutBtn = accountMenu.querySelector('.logout-btn');
    logoutBtn.addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });

    // Add event listeners to section links
    const sectionLinks = accountMenu.querySelectorAll('.account-menu-item[href*="#"]');
    sectionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Store the section to show in localStorage
            const sectionId = this.getAttribute('href').split('#')[1];
            localStorage.setItem('activeAccountSection', sectionId);
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function closeMenu(e) {
        if (!accountMenu.contains(e.target) && e.target !== element) {
            accountMenu.classList.remove('show');
            setTimeout(() => {
                accountMenu.remove();
            }, 300);
            document.removeEventListener('click', closeMenu);
        }
    });
}

/**
 * Logout function
 */
function logout() {
    // Get user data
    const user = JSON.parse(localStorage.getItem('user')) || {};

    // Update user data
    user.isLoggedIn = false;

    // Save to localStorage
    localStorage.setItem('user', JSON.stringify(user));

    // Show notification
    showNotification('Đăng xuất thành công', 'success');

    // Reset user icon
    const userIcon = document.querySelector('.nav-icon.logged-in');
    if (userIcon) {
        userIcon.innerHTML = '<i class="fas fa-user"></i>';
        userIcon.title = 'Tài khoản';
        userIcon.classList.remove('logged-in');
    }

    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
