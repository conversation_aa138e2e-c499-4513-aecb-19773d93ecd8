<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="./styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <title>Về chúng tôi | Fashion Store</title>

    <style>
        /* Navigation Menu Styles */
        .nav-menu {
            background-color: #fff;
            border-bottom: 1px solid #eee;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu li {
            position: relative;
        }

        .menu li a {
            display: block;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .menu li:hover > a, .menu li.active > a {
            color: #ff6b6b;
        }

        .menu li.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20px;
            right: 20px;
            height: 3px;
            background-color: #ff6b6b;
        }

        /* Footer Styles */
        .footer {
            background-color: #222;
            color: #fff;
            padding: 60px 0 30px;
        }

        .footer-top {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 40px;
        }

        .footer-column {
            flex: 1;
            min-width: 250px;
        }

        .footer-title {
            font-size: 18px;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background-color: #ff6b6b;
        }

        .footer-description {
            color: #aaa;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .social-icons {
            display: flex;
            gap: 10px;
        }

        .social-icon {
            width: 36px;
            height: 36px;
            background-color: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            transition: all 0.3s;
        }

        .social-icon:hover {
            background-color: #ff6b6b;
            transform: translateY(-3px);
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: #aaa;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: #ff6b6b;
        }

        .contact-info {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .contact-info li {
            color: #aaa;
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .contact-info i {
            color: #ff6b6b;
            margin-right: 10px;
            min-width: 20px;
        }

        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 30px;
            border-top: 1px solid #333;
        }

        .copyright {
            color: #aaa;
            font-size: 14px;
        }

        .payment-methods img {
            height: 30px;
        }

        /* Hero Banner */
        .about-hero {
            position: relative;
            height: 500px;
            background-image: url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 60px;
        }

        .about-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.7));
            z-index: 1;
        }

        .about-hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
        }

        .about-hero h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            animation: fadeInDown 1s ease;
        }

        .about-hero p {
            font-size: 20px;
            line-height: 1.6;
            margin-bottom: 30px;
            animation: fadeInUp 1s ease 0.3s;
            animation-fill-mode: both;
        }

        .about-hero .btn {
            display: inline-block;
            background-color: #ff6b6b;
            color: white;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
            animation: fadeInUp 1s ease 0.6s;
            animation-fill-mode: both;
        }

        .about-hero .btn:hover {
            background-color: #ff5252;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .about-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .about-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .about-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .about-header h1 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .about-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .about-section {
            margin-bottom: 60px;
        }

        .about-section h2 {
            font-size: 28px;
            color: #ff6b6b;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        .about-section h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .about-section p {
            font-size: 16px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .about-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 30px;
        }

        .about-card {
            flex: 1;
            min-width: 250px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 25px;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .about-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .about-card i {
            font-size: 40px;
            color: #ff6b6b;
            margin-bottom: 20px;
        }

        .about-card h3 {
            font-size: 20px;
            color: #333;
            margin-bottom: 15px;
        }

        .about-card p {
            font-size: 15px;
            color: #666;
            line-height: 1.6;
        }

        .team-section {
            margin-top: 60px;
        }

        .team-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 30px;
        }

        .team-member {
            flex: 1;
            min-width: 250px;
            text-align: center;
        }

        .team-photo {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 20px;
            border: 5px solid #f8f8f8;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .team-name {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .team-role {
            font-size: 16px;
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .team-bio {
            font-size: 15px;
            color: #666;
            line-height: 1.6;
            max-width: 300px;
            margin: 0 auto;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .social-links a {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.3s;
        }

        .social-links a:hover {
            background-color: #ff6b6b;
            color: white;
        }

        /* Timeline Styles */
        .story-timeline {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 0;
        }

        .story-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 50%;
            width: 4px;
            background-color: #ff6b6b;
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 60px;
            display: flex;
            justify-content: flex-start;
            width: 100%;
        }

        .timeline-item:nth-child(even) {
            justify-content: flex-end;
        }

        .timeline-year {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            background-color: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            z-index: 2;
        }

        .timeline-content {
            width: 45%;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            position: relative;
            margin-top: 30px;
        }

        .timeline-content::before {
            content: '';
            position: absolute;
            top: 20px;
            width: 20px;
            height: 20px;
            background-color: white;
            transform: rotate(45deg);
        }

        .timeline-item:nth-child(odd) .timeline-content::before {
            right: -10px;
        }

        .timeline-item:nth-child(even) .timeline-content::before {
            left: -10px;
        }

        .timeline-content h3 {
            color: #ff6b6b;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 22px;
        }

        .timeline-content p {
            margin-bottom: 20px;
        }

        .timeline-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 6px;
        }

        .stats-section {
            background-color: #f9f9f9;
            padding: 60px 0;
            margin: 60px 0;
            text-align: center;
        }

        .stats-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 50px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .stat-item {
            flex: 1;
            min-width: 200px;
        }

        .stat-number {
            font-size: 48px;
            font-weight: 700;
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 18px;
            color: #666;
        }

        @media (max-width: 768px) {
            .about-header h1 {
                font-size: 28px;
            }

            .about-header p {
                font-size: 16px;
            }

            .about-section h2 {
                font-size: 24px;
            }

            .team-photo {
                width: 150px;
                height: 150px;
            }

            .stat-number {
                font-size: 36px;
            }

            .stat-label {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="about-hero">
        <div class="about-hero-content">
            <h1>Về Fashion Store</h1>
            <p>Chúng tôi là thương hiệu thời trang hàng đầu Việt Nam, mang đến những sản phẩm chất lượng cao với giá cả hợp lý. Sứ mệnh của chúng tôi là giúp mọi người tự tin thể hiện phong cách cá nhân thông qua thời trang.</p>
            <a href="#our-story" class="btn">Khám phá câu chuyện của chúng tôi</a>
        </div>
    </div>

    <!-- About Content -->
    <div class="about-container">
        <div class="about-header">
            <h1>Câu chuyện thương hiệu</h1>
            <p>Hành trình phát triển và những giá trị cốt lõi đã tạo nên Fashion Store như ngày hôm nay</p>
        </div>

        <div id="our-story" class="about-section">
            <h2>Câu chuyện của chúng tôi</h2>
            <div class="story-timeline">
                <div class="timeline-item">
                    <div class="timeline-year">2010</div>
                    <div class="timeline-content">
                        <h3>Khởi đầu</h3>
                        <p>Fashion Store được thành lập bởi một nhóm những người đam mê thời trang với mong muốn mang đến những sản phẩm thời trang chất lượng cao nhưng giá cả phải chăng cho người tiêu dùng Việt Nam.</p>
                        <img src="https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store 2010" class="timeline-image">
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2015</div>
                    <div class="timeline-content">
                        <h3>Mở rộng</h3>
                        <p>Từ một cửa hàng nhỏ tại Hà Nội, chúng tôi đã mở rộng với 10 cửa hàng tại các thành phố lớn và bắt đầu phát triển kênh bán hàng trực tuyến.</p>
                        <img src="https://images.unsplash.com/photo-1567401893414-76b7b1e5a7a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store 2015" class="timeline-image">
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2020</div>
                    <div class="timeline-content">
                        <h3>Phát triển mạnh mẽ</h3>
                        <p>Fashion Store trở thành một trong những thương hiệu thời trang được yêu thích nhất với hơn 50 cửa hàng trên toàn quốc và hệ thống bán hàng trực tuyến phục vụ hàng triệu khách hàng mỗi năm.</p>
                        <img src="https://images.unsplash.com/photo-1573855619003-97b4799dcd8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store 2020" class="timeline-image">
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Hiện tại</div>
                    <div class="timeline-content">
                        <h3>Hướng tới tương lai</h3>
                        <p>Chúng tôi tự hào về hành trình phát triển của mình và cam kết tiếp tục đổi mới, sáng tạo để mang đến những trải nghiệm mua sắm tốt nhất cho khách hàng.</p>
                        <img src="https://images.unsplash.com/photo-1607083206968-13611e3d76db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Future" class="timeline-image">
                    </div>
                </div>
            </div>
        </div>

        <div class="about-section">
            <h2>Giá trị cốt lõi</h2>
            <div class="about-grid">
                <div class="about-card">
                    <i class="fas fa-gem"></i>
                    <h3>Chất lượng</h3>
                    <p>Chúng tôi cam kết mang đến những sản phẩm chất lượng cao, được kiểm soát nghiêm ngặt từ khâu thiết kế đến sản xuất.</p>
                </div>
                <div class="about-card">
                    <i class="fas fa-hand-holding-heart"></i>
                    <h3>Trách nhiệm</h3>
                    <p>Chúng tôi hoạt động với tinh thần trách nhiệm cao đối với khách hàng, nhân viên, cộng đồng và môi trường.</p>
                </div>
                <div class="about-card">
                    <i class="fas fa-lightbulb"></i>
                    <h3>Sáng tạo</h3>
                    <p>Chúng tôi không ngừng đổi mới và sáng tạo để mang đến những xu hướng thời trang mới nhất cho khách hàng.</p>
                </div>
                <div class="about-card">
                    <i class="fas fa-users"></i>
                    <h3>Khách hàng là trọng tâm</h3>
                    <p>Mọi quyết định của chúng tôi đều hướng đến việc mang lại trải nghiệm tốt nhất cho khách hàng.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">50+</div>
                <div class="stat-label">Cửa hàng trên toàn quốc</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1M+</div>
                <div class="stat-label">Khách hàng hài lòng</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5000+</div>
                <div class="stat-label">Mẫu thiết kế</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">13</div>
                <div class="stat-label">Năm kinh nghiệm</div>
            </div>
        </div>
    </div>

    <div class="about-container">
        <div class="about-section team-section">
            <h2>Đội ngũ của chúng tôi</h2>
            <p>Đằng sau thành công của Fashion Store là một đội ngũ tài năng và đam mê, luôn nỗ lực không ngừng để mang đến những sản phẩm và dịch vụ tốt nhất cho khách hàng.</p>

            <div class="team-grid">
                <div class="team-member">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="CEO" class="team-photo">
                    <h3 class="team-name">Nguyễn Văn A</h3>
                    <div class="team-role">Giám đốc điều hành</div>
                    <p class="team-bio">Với hơn 15 năm kinh nghiệm trong ngành thời trang, anh A là người đặt nền móng và dẫn dắt Fashion Store phát triển vững mạnh.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="team-member">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Creative Director" class="team-photo">
                    <h3 class="team-name">Trần Thị B</h3>
                    <div class="team-role">Giám đốc sáng tạo</div>
                    <p class="team-bio">Chị B là người định hướng phong cách và xu hướng thời trang cho Fashion Store với con mắt thẩm mỹ tinh tế và sáng tạo không giới hạn.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div class="team-member">
                    <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Marketing Director" class="team-photo">
                    <h3 class="team-name">Lê Văn C</h3>
                    <div class="team-role">Giám đốc marketing</div>
                    <p class="team-bio">Anh C là chuyên gia trong lĩnh vực marketing với khả năng xây dựng chiến lược hiệu quả, giúp Fashion Store tiếp cận hàng triệu khách hàng.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="team-member">
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Customer Service Manager" class="team-photo">
                    <h3 class="team-name">Phạm Thị D</h3>
                    <div class="team-role">Quản lý dịch vụ khách hàng</div>
                    <p class="team-bio">Chị D và đội ngũ của mình luôn đảm bảo mọi khách hàng đều nhận được sự hỗ trợ tốt nhất và có trải nghiệm mua sắm tuyệt vời.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/lucky-wheel.js"></script>
    <script src="./script/chatbot.js"></script>
    <script src="./script/auth-check.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>

    <script>
        // Hiển thị tên người dùng đã đăng nhập
        document.addEventListener('DOMContentLoaded', function() {
            function displayUsername() {
                const user = JSON.parse(localStorage.getItem('user'));
                if (user && user.isLoggedIn) {
                    const userIcon = document.querySelector('.nav-icons .nav-icon[title="Tài khoản"]');
                    if (userIcon) {
                        // Thay đổi icon thành tên người dùng
                        const userName = user.name || user.email.split('@')[0];
                        userIcon.innerHTML = `<span class="user-name">${userName}</span>`;
                        userIcon.title = `Xin chào, ${userName}`;

                        // Thêm sự kiện click để hiển thị menu tài khoản
                        userIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showAccountMenu(this);
                        });
                    }
                }
            }

            // Hiển thị menu tài khoản
            function showAccountMenu(element) {
                // Xóa menu cũ nếu có
                const existingMenu = document.querySelector('.account-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }

                // Tạo menu tài khoản
                const accountMenu = document.createElement('div');
                accountMenu.className = 'account-menu';

                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user'));
                const userName = user.name || user.email.split('@')[0];

                // Thiết lập nội dung menu
                accountMenu.innerHTML = `
                    <div class="account-menu-header">
                        <div class="account-menu-user">
                            <div class="account-menu-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="account-menu-info">
                                <div class="account-menu-name">${userName}</div>
                                <div class="account-menu-email">${user.email}</div>
                            </div>
                        </div>
                    </div>
                    <div class="account-menu-body">
                        <a href="./pages/account.html" class="account-menu-item">
                            <i class="fas fa-user"></i> Tài khoản của tôi
                        </a>
                        <a href="./pages/orders.html" class="account-menu-item">
                            <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                        </a>
                        <a href="./pages/wishlist.html" class="account-menu-item">
                            <i class="fas fa-heart"></i> Danh sách yêu thích
                        </a>
                        <a href="./pages/settings.html" class="account-menu-item">
                            <i class="fas fa-cog"></i> Cài đặt
                        </a>
                        <div class="account-menu-divider"></div>
                        <a href="#" class="account-menu-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Đăng xuất
                        </a>
                    </div>
                `;

                // Định vị menu
                const rect = element.getBoundingClientRect();
                accountMenu.style.top = `${rect.bottom + window.scrollY}px`;
                accountMenu.style.right = `${window.innerWidth - rect.right}px`;

                // Thêm vào body
                document.body.appendChild(accountMenu);

                // Hiển thị với hiệu ứng
                setTimeout(() => {
                    accountMenu.classList.add('show');
                }, 10);

                // Thêm sự kiện cho nút đăng xuất
                const logoutBtn = accountMenu.querySelector('.logout-btn');
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });

                // Đóng menu khi click bên ngoài
                document.addEventListener('click', function closeMenu(e) {
                    if (!accountMenu.contains(e.target) && e.target !== element) {
                        accountMenu.classList.remove('show');
                        setTimeout(() => {
                            accountMenu.remove();
                        }, 300);
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }

            // Hàm đăng xuất
            function logout() {
                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user')) || {};

                // Cập nhật trạng thái đăng nhập
                user.isLoggedIn = false;

                // Lưu vào localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Hiển thị thông báo
                alert('Đăng xuất thành công');

                // Khôi phục icon người dùng
                const userIcon = document.querySelector('.nav-icons .nav-icon[title="Xin chào, ' + (user.name || user.email.split('@')[0]) + '"]');
                if (userIcon) {
                    userIcon.innerHTML = '<i class="fas fa-user"></i>';
                    userIcon.title = 'Tài khoản';
                }

                // Chuyển hướng về trang chủ sau một khoảng thời gian ngắn
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            }

            // Gọi hàm hiển thị tên người dùng
            displayUsername();

            // Cập nhật số lượng giỏ hàng
            function updateCartCount() {
                const cart = JSON.parse(localStorage.getItem('cart')) || [];
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = cart.length;
                }
            }

            // Cập nhật số lượng yêu thích
            function updateWishlistCount() {
                const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
                const wishlistCount = document.querySelector('.wishlist-count');
                if (wishlistCount) {
                    wishlistCount.textContent = wishlist.length;
                }
            }

            // Gọi các hàm cập nhật
            updateCartCount();
            updateWishlistCount();
        });
    </script>
</body>
</html>
