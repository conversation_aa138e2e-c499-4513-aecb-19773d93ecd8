// Popup Management
document.addEventListener('DOMContentLoaded', function() {
    // Get all popup elements
    const popupOverlays = document.querySelectorAll('.popup-overlay');
    const closeButtons = document.querySelectorAll('.popup-close');

    // Newsletter popup
    const newsletterPopup = document.getElementById('newsletter-popup');
    const dontShowAgainCheckbox = document.getElementById('dont-show-again');

    // Login popup
    const loginPopup = document.getElementById('login-popup');
    const loginLinks = document.querySelectorAll('.login-link');

    // Quick view popup
    const quickViewPopup = document.getElementById('quickview-popup');
    const quickViewButtons = document.querySelectorAll('.btn-quick-view');

    // Close popup when clicking the close button
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const popup = this.closest('.popup-overlay');
            closePopup(popup);
        });
    });

    // Close popup when clicking outside the popup content
    popupOverlays.forEach(overlay => {
        overlay.addEventListener('click', function(e) {
            if (e.target === this) {
                closePopup(this);
            }
        });
    });

    // Show newsletter popup after 5 seconds if not previously dismissed
    if (!localStorage.getItem('newsletter_popup_dismissed')) {
        setTimeout(() => {
            openPopup(newsletterPopup);
        }, 5000);
    }

    // Handle "Don't show again" checkbox
    if (dontShowAgainCheckbox) {
        dontShowAgainCheckbox.addEventListener('change', function() {
            if (this.checked) {
                localStorage.setItem('newsletter_popup_dismissed', 'true');
            } else {
                localStorage.removeItem('newsletter_popup_dismissed');
            }
        });
    }

    // Open login popup when clicking login links
    loginLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            openPopup(loginPopup);
        });
    });

    // Add login link functionality to user icon in header
    const userIcon = document.querySelector('.nav-icon[title="Tài khoản"]');
    if (userIcon) {
        userIcon.addEventListener('click', function(e) {
            e.preventDefault();
            openPopup(loginPopup);
        });
    }

    // Add newsletter popup functionality to footer newsletter form
    const footerNewsletterForm = document.querySelector('.footer-newsletter-form');
    if (footerNewsletterForm) {
        footerNewsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            openPopup(newsletterPopup);
        });
    }

    // Quick View functionality
    quickViewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Get product information from the parent product card
            const productCard = this.closest('.product-card');
            if (productCard) {
                const productImage = productCard.querySelector('img').src;
                const productTitle = productCard.querySelector('.product-name').textContent;
                const productPrice = productCard.querySelector('.product-price').textContent;

                // Update quick view popup with product information
                document.getElementById('quickview-image').src = productImage;
                document.getElementById('quickview-title').textContent = productTitle;
                document.getElementById('quickview-price').textContent = productPrice;

                // Open the quick view popup
                openPopup(quickViewPopup);
            }
        });
    });

    // Handle color options in quick view
    const colorOptions = document.querySelectorAll('.quickview-popup .color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all color options
            colorOptions.forEach(opt => opt.classList.remove('selected'));
            // Add selected class to clicked option
            this.classList.add('selected');
        });
    });

    // Handle size options in quick view
    const sizeOptions = document.querySelectorAll('.quickview-popup .size-option');
    sizeOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all size options
            sizeOptions.forEach(opt => opt.classList.remove('selected'));
            // Add selected class to clicked option
            this.classList.add('selected');
        });
    });

    // Handle quantity buttons in quick view
    const minusBtn = document.querySelector('.quickview-popup .minus');
    const plusBtn = document.querySelector('.quickview-popup .plus');
    const quantityInput = document.querySelector('.quickview-popup .quantity-input');

    if (minusBtn && plusBtn && quantityInput) {
        minusBtn.addEventListener('click', function() {
            let quantity = parseInt(quantityInput.value);
            if (quantity > 1) {
                quantityInput.value = quantity - 1;
            }
        });

        plusBtn.addEventListener('click', function() {
            let quantity = parseInt(quantityInput.value);
            quantityInput.value = quantity + 1;
        });
    }

    // Add to cart button in quick view
    const addToCartBtn = document.querySelector('.quickview-popup .add-to-cart');
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            // Check if color and size are selected
            const selectedColor = document.querySelector('.quickview-popup .color-option.selected');
            const selectedSize = document.querySelector('.quickview-popup .size-option.selected');

            if (!selectedColor) {
                showNotification('Vui lòng chọn màu sắc', 'warning');
                return;
            }

            if (!selectedSize) {
                showNotification('Vui lòng chọn kích thước', 'warning');
                return;
            }

            // Get product information
            const productTitle = document.getElementById('quickview-title').textContent;
            const productPrice = document.getElementById('quickview-price').textContent;
            const productImage = document.getElementById('quickview-image').src;
            const quantity = parseInt(document.querySelector('.quickview-popup .quantity-input').value);
            const color = selectedColor.getAttribute('data-color');
            const size = selectedSize.getAttribute('data-size');

            // Add to cart logic (using localStorage for demo)
            addToCart({
                title: productTitle,
                price: productPrice,
                image: productImage,
                quantity: quantity,
                color: color,
                size: size
            });

            // Close popup and show success notification
            closePopup(quickViewPopup);
            showNotification(`Đã thêm ${productTitle} vào giỏ hàng`, 'success');
        });
    }

    // Function to open popup
    function openPopup(popup) {
        if (popup) {
            popup.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling
        }
    }

    // Function to close popup
    function closePopup(popup) {
        if (popup) {
            popup.classList.remove('active');
            document.body.style.overflow = ''; // Restore scrolling
        }
    }

    // Function to add product to cart
    function addToCart(product) {
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Check if product already exists in cart with same color and size
        const existingProductIndex = cart.findIndex(item =>
            item.title === product.title &&
            item.color === product.color &&
            item.size === product.size
        );

        if (existingProductIndex !== -1) {
            // Update quantity if product already exists
            cart[existingProductIndex].quantity += product.quantity;
        } else {
            // Add new product to cart
            cart.push(product);
        }

        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));

        // Update cart count in header
        updateCartCount();
    }

    // Function to update cart count
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = cart.length;
        }
    }

    // Initialize cart count
    updateCartCount();
});

// Function to show notification
function showNotification(message, type = 'info') {
    // Get or create notification container
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Add icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

window.addEventListener('load', function () {
    // Hiển thị popup sau 10 giây
    setTimeout(() => {
        document.querySelector('.spin-popup').classList.remove('hide-spin');
    }, 10000);

    const prizes = ["10%", "15%", "Không trúng", "20%", "5%", "30%"];
    const wheel = document.getElementById('wheel');
    const ctx = wheel.getContext('2d');
    const spinBtn = document.getElementById('spin-btn');
    const result = document.getElementById('spin-result');
    const close = document.querySelector('.close-spin');

    let angle = 0;
    const segmentAngle = 360 / prizes.length;

    function drawWheel() {
        for (let i = 0; i < prizes.length; i++) {
            ctx.beginPath();
            ctx.fillStyle = i % 2 === 0 ? '#f1c40f' : '#e67e22';
            ctx.moveTo(150, 150);
            ctx.arc(150, 150, 140, angle * Math.PI/180, (angle + segmentAngle) * Math.PI/180);
            ctx.fill();
            ctx.save();
            ctx.translate(150, 150);
            ctx.rotate((angle + segmentAngle/2) * Math.PI/180);
            ctx.fillStyle = "#fff";
            ctx.font = "16px Arial";
            ctx.fillText(prizes[i], 60, 0);
            ctx.restore();
            angle += segmentAngle;
        }
    }

    drawWheel();

    spinBtn.addEventListener('click', () => {
        const rand = Math.floor(Math.random() * prizes.length);
        const rotation = 360 * 3 + rand * segmentAngle + segmentAngle / 2;
        wheel.style.transition = 'transform 4s ease-out';
        wheel.style.transform = `rotate(${rotation}deg)`;
        setTimeout(() => {
            result.textContent = `🎁 Bạn nhận được: ${prizes[rand]} giảm giá!`;
        }, 4200);
    });

    close.addEventListener('click', () => {
        document.querySelector('.spin-popup').classList.add('hide-spin');
    });
});