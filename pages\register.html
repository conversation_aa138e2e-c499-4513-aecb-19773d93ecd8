<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/sections.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/account.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="../styles/neon-effects.css">

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />

    <title>Đăng ký | Fashion Store</title>

    <style>
        /* Đảm bảo màu sắc giống với index.html */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #f5f5f5;
            --accent-color: #e74c3c;
            --accent-hover: #c0392b;
            --text-color: #333;
            --light-text: #fff;
            --dark-text: #222;
            --border-color: #ddd;
            --hover-color: #f9f9f9;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Navbar styles */
        .announcement-bar {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .main-nav {
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-item > a {
            color: var(--primary-color);
        }

        .nav-item > a:hover {
            color: var(--accent-color);
        }

        .nav-icon {
            color: var(--primary-color);
        }

        .nav-icon:hover {
            color: var(--accent-color);
        }

        /* Footer styles */
        .footer {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .footer::before {
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
        }

        .footer-column h3 {
            color: var(--light-text);
        }

        .footer-column h3::after {
            background-color: var(--accent-color);
        }

        .footer-column ul li a {
            color: rgba(255, 255, 255, 0.7);
        }

        .footer-column ul li a::before {
            color: var(--accent-color);
        }

        .footer-column ul li a:hover {
            color: var(--light-text);
        }

        .social-icon {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--light-text);
        }

        .social-icon:hover {
            background-color: var(--accent-color);
            color: var(--light-text);
        }

        .footer-newsletter-form button {
            background-color: var(--accent-color);
        }

        .footer-newsletter-form button:hover {
            background-color: var(--accent-hover);
        }

        .contact-icon {
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Register Page Content -->
    <div class="auth-container">
        <div class="container">
            <div class="auth-wrapper">
                <div class="auth-form-container neon-form-container electric-border" data-aos="fade-right" data-aos-duration="800">
                    <div class="led-strip"></div>
                    <div class="led-strip led-bottom"></div>
                    <div class="wave-container"></div>
                    <div class="glowing-bg"></div>

                    <div class="auth-header">
                        <h1 class="neon-title">Đăng ký tài khoản</h1>
                        <p>Tạo tài khoản để mua sắm dễ dàng hơn và nhận nhiều ưu đãi đặc biệt</p>
                    </div>

                    <div class="registration-steps">
                        <div class="step active" data-step="1">
                            <span class="step-number">1</span>
                            <span class="step-text">Thông tin cá nhân</span>
                        </div>
                        <div class="step" data-step="2">
                            <span class="step-number">2</span>
                            <span class="step-text">Tài khoản</span>
                        </div>
                    </div>

                    <form id="register-form" class="auth-form">
                        <!-- Step 1: Personal Information -->
                        <div class="form-step active" data-step="1">
                            <div class="form-row">
                                <div class="form-group neon-input-container lightning-effect">
                                    <label for="first-name">Họ <span class="required">*</span></label>
                                    <input type="text" id="first-name" name="first-name" class="neon-input" required>
                                </div>

                                <div class="form-group neon-input-container lightning-effect">
                                    <label for="last-name">Tên <span class="required">*</span></label>
                                    <input type="text" id="last-name" name="last-name" class="neon-input" required>
                                </div>
                            </div>

                            <div class="form-group neon-input-container lightning-effect">
                                <label for="email">Email <span class="required">*</span></label>
                                <input type="email" id="email" name="email" class="neon-input" required>
                                <div class="email-verification">
                                    <button type="button" id="verify-email" class="verify-btn neon-button">Xác minh email</button>
                                </div>
                            </div>

                            <div class="form-group neon-input-container lightning-effect">
                                <label for="phone">Số điện thoại <span class="required">*</span></label>
                                <input type="tel" id="phone" name="phone" class="neon-input" required>
                            </div>

                            <div class="form-group neon-input-container lightning-effect">
                                <label for="birthdate">Ngày sinh</label>
                                <input type="date" id="birthdate" name="birthdate" class="neon-input">
                            </div>

                            <div class="form-group">
                                <label>Giới tính</label>
                                <div class="gender-options">
                                    <div class="gender-option">
                                        <input type="radio" id="gender-male" name="gender" value="male">
                                        <label for="gender-male">Nam</label>
                                    </div>
                                    <div class="gender-option">
                                        <input type="radio" id="gender-female" name="gender" value="female">
                                        <label for="gender-female">Nữ</label>
                                    </div>
                                    <div class="gender-option">
                                        <input type="radio" id="gender-other" name="gender" value="other">
                                        <label for="gender-other">Khác</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="avatar">Ảnh đại diện</label>
                                <div class="avatar-upload">
                                    <div class="avatar-preview">
                                        <div id="avatar-preview" style="background-image: url('../img/default-avatar.jpg');"></div>
                                    </div>
                                    <div class="avatar-edit">
                                        <input type="file" id="avatar" name="avatar" accept="image/*">
                                        <label for="avatar">Chọn ảnh</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-navigation">
                                <button type="button" class="btn btn-next">Tiếp theo <i class="fas fa-arrow-right"></i></button>
                            </div>
                        </div>

                        <!-- Step 2: Account Information -->
                        <div class="form-step" data-step="2">
                            <div class="form-group">
                                <label for="username">Tên người dùng <span class="required">*</span></label>
                                <input type="text" id="username" name="username" required>
                                <div class="username-suggestion">
                                    <small class="suggestion-text">Gợi ý: <span id="username-suggestions"></span></small>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="password">Mật khẩu <span class="required">*</span></label>
                                <div class="password-input">
                                    <input type="password" id="password" name="password" required>
                                    <span class="toggle-password"><i class="far fa-eye"></i></span>
                                </div>
                                <div class="password-suggestion">
                                    <button type="button" id="suggest-password" class="suggest-btn">Gợi ý mật khẩu</button>
                                </div>
                                <div class="password-strength">
                                    <div class="strength-meter">
                                        <div class="strength-meter-fill" data-strength="0"></div>
                                    </div>
                                    <div class="strength-text">Mật khẩu yếu</div>
                                </div>
                                <div class="password-requirements">
                                    <ul>
                                        <li id="length"><i class="fas fa-times"></i> Ít nhất 8 ký tự</li>
                                        <li id="uppercase"><i class="fas fa-times"></i> Ít nhất 1 chữ hoa</li>
                                        <li id="lowercase"><i class="fas fa-times"></i> Ít nhất 1 chữ thường</li>
                                        <li id="number"><i class="fas fa-times"></i> Ít nhất 1 số</li>
                                        <li id="special"><i class="fas fa-times"></i> Ít nhất 1 ký tự đặc biệt</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="confirm-password">Xác nhận mật khẩu <span class="required">*</span></label>
                                <div class="password-input">
                                    <input type="password" id="confirm-password" name="confirm-password" required>
                                    <span class="toggle-password"><i class="far fa-eye"></i></span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>Bảo mật tài khoản</label>
                                <div class="security-options">
                                    <div class="security-option">
                                        <input type="checkbox" id="two-factor" name="two-factor">
                                        <label for="two-factor">Bật xác thực hai yếu tố</label>
                                    </div>
                                    <div class="security-option">
                                        <input type="checkbox" id="login-notification" name="login-notification" checked>
                                        <label for="login-notification">Thông báo khi có đăng nhập mới</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-navigation">
                                <button type="button" class="btn btn-next neon-button">Tiếp theo <i class="fas fa-arrow-right"></i></button>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-options">
                            <div class="remember-me">
                                <input type="checkbox" id="terms" name="terms" required>
                                <label for="terms">Tôi đồng ý với <a href="#">Điều khoản dịch vụ</a> và <a href="#">Chính sách bảo mật</a></label>
                            </div>
                        </div>

                        <div class="form-navigation text-center">
                            <button type="submit" class="btn btn-primary btn-block neon-button">Hoàn tất đăng ký</button>
                        </div>
                    </form>

                    <div class="social-login">
                        <p>Hoặc đăng ký nhanh với</p>
                        <div class="social-buttons">
                            <button class="social-btn facebook neon-button"><i class="fab fa-facebook-f"></i> Facebook</button>
                            <button class="social-btn google neon-button"><i class="fab fa-google"></i> Google</button>
                            <button class="social-btn apple neon-button"><i class="fab fa-apple"></i> Apple</button>
                        </div>
                    </div>

                    <div class="auth-footer">
                        <p>Bạn đã có tài khoản? <a href="login.html">Đăng nhập</a></p>
                    </div>
                </div>

                <div class="auth-image" data-aos="fade-left" data-aos-duration="800">
                    <img src="../img womens/women 15.webp" alt="Register Image">
                    <div class="auth-image-overlay">
                        <h2>Tham gia cùng chúng tôi</h2>
                        <p>Đăng ký để nhận nhiều ưu đãi đặc biệt và cập nhật xu hướng thời trang mới nhất</p>
                        <div class="benefits">
                            <div class="benefit-item">
                                <i class="fas fa-gift"></i>
                                <span>Quà tặng cho thành viên mới</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-percent"></i>
                                <span>Giảm giá độc quyền</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-truck"></i>
                                <span>Miễn phí vận chuyển</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-star"></i>
                                <span>Tích điểm đổi quà</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Verification Modal -->
    <div id="verification-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Xác minh email</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>Chúng tôi đã gửi mã xác minh đến email của bạn. Vui lòng nhập mã để xác minh.</p>
                <div class="verification-code">
                    <input type="text" maxlength="1" class="code-input">
                    <input type="text" maxlength="1" class="code-input">
                    <input type="text" maxlength="1" class="code-input">
                    <input type="text" maxlength="1" class="code-input">
                    <input type="text" maxlength="1" class="code-input">
                    <input type="text" maxlength="1" class="code-input">
                </div>
                <div class="verification-actions">
                    <button id="verify-code" class="btn btn-primary">Xác minh</button>
                    <button id="resend-code" class="btn btn-text">Gửi lại mã</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container"></div>





    <div id="footerbox"></div>

    <iframe
        id="youtube-player"
        width="0" height="0"
        src="https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=1"
        frameborder="0"
        allow="autoplay; encrypted-media">
    </iframe>




    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/auth-enhanced.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/voice-search.js"></script>
    <script src="../script/dark-mode.js"></script>
    <script src="../script/neon-effects.js"></script>
    <script src="../script/user-display.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/api-service.js"></script>
    <script src="../script/marquee.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Xử lý form đăng ký nhiều bước
        document.addEventListener('DOMContentLoaded', function() {
            // Xử lý chuyển bước
            const nextButtons = document.querySelectorAll('.btn-next');
            const prevButtons = document.querySelectorAll('.btn-prev');
            const steps = document.querySelectorAll('.step');
            const formSteps = document.querySelectorAll('.form-step');

            // Nút tiếp theo
            nextButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const currentStep = parseInt(this.closest('.form-step').dataset.step);
                    const nextStep = currentStep + 1;

                    // Kiểm tra dữ liệu nhập
                    if (validateStep(currentStep)) {
                        // Ẩn bước hiện tại
                        document.querySelector(`.form-step[data-step="${currentStep}"]`).classList.remove('active');
                        // Hiển thị bước tiếp theo
                        document.querySelector(`.form-step[data-step="${nextStep}"]`).classList.add('active');

                        // Cập nhật trạng thái các bước
                        steps.forEach(step => {
                            const stepNum = parseInt(step.dataset.step);
                            if (stepNum === nextStep) {
                                step.classList.add('active');
                            } else if (stepNum < nextStep) {
                                step.classList.add('completed');
                                step.classList.remove('active');
                            }
                        });

                        // Cuộn lên đầu form
                        document.querySelector('.auth-form-container').scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // Nút quay lại
            prevButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const currentStep = parseInt(this.closest('.form-step').dataset.step);
                    const prevStep = currentStep - 1;

                    // Ẩn bước hiện tại
                    document.querySelector(`.form-step[data-step="${currentStep}"]`).classList.remove('active');
                    // Hiển thị bước trước đó
                    document.querySelector(`.form-step[data-step="${prevStep}"]`).classList.add('active');

                    // Cập nhật trạng thái các bước
                    steps.forEach(step => {
                        const stepNum = parseInt(step.dataset.step);
                        if (stepNum === prevStep) {
                            step.classList.add('active');
                            step.classList.remove('completed');
                        } else if (stepNum > prevStep) {
                            step.classList.remove('active', 'completed');
                        }
                    });

                    // Cuộn lên đầu form
                    document.querySelector('.auth-form-container').scrollIntoView({ behavior: 'smooth' });
                });
            });

            // Xử lý xác minh email
            const verifyEmailBtn = document.getElementById('verify-email');
            const verificationModal = document.getElementById('verification-modal');
            const closeModal = document.querySelector('.close-modal');
            const verifyCodeBtn = document.getElementById('verify-code');
            const resendCodeBtn = document.getElementById('resend-code');
            const codeInputs = document.querySelectorAll('.code-input');

            // Mở modal xác minh
            verifyEmailBtn.addEventListener('click', function() {
                const email = document.getElementById('email').value;
                if (email && validateEmail(email)) {
                    verificationModal.style.display = 'block';
                    // Giả lập gửi mã xác minh
                    showNotification('Đã gửi mã xác minh đến ' + email, 'info');

                    // Focus vào ô nhập mã đầu tiên
                    codeInputs[0].focus();
                } else {
                    showNotification('Vui lòng nhập email hợp lệ', 'error');
                }
            });

            // Đóng modal
            closeModal.addEventListener('click', function() {
                verificationModal.style.display = 'none';
            });

            // Xử lý nhập mã xác minh
            codeInputs.forEach((input, index) => {
                input.addEventListener('keyup', function(e) {
                    if (e.key >= '0' && e.key <= '9') {
                        // Tự động chuyển đến ô tiếp theo
                        if (index < codeInputs.length - 1) {
                            codeInputs[index + 1].focus();
                        }
                    } else if (e.key === 'Backspace') {
                        // Xóa và quay lại ô trước đó
                        if (index > 0) {
                            codeInputs[index - 1].focus();
                        }
                    }
                });
            });

            // Xác minh mã
            verifyCodeBtn.addEventListener('click', function() {
                let code = '';
                codeInputs.forEach(input => {
                    code += input.value;
                });

                if (code.length === 6) {
                    // Giả lập xác minh thành công
                    showNotification('Xác minh email thành công!', 'success');
                    verificationModal.style.display = 'none';

                    // Đánh dấu email đã xác minh
                    verifyEmailBtn.textContent = 'Đã xác minh';
                    verifyEmailBtn.classList.add('verified');
                    verifyEmailBtn.disabled = true;
                } else {
                    showNotification('Vui lòng nhập đủ 6 chữ số', 'warning');
                }
            });

            // Gửi lại mã
            resendCodeBtn.addEventListener('click', function() {
                // Giả lập gửi lại mã
                showNotification('Đã gửi lại mã xác minh', 'info');

                // Reset các ô nhập mã
                codeInputs.forEach(input => {
                    input.value = '';
                });
                codeInputs[0].focus();
            });

            // Xử lý tải ảnh đại diện
            const avatarInput = document.getElementById('avatar');
            const avatarPreview = document.getElementById('avatar-preview');

            avatarInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        avatarPreview.style.backgroundImage = `url(${e.target.result})`;
                    }
                    reader.readAsDataURL(file);
                }
            });

            // Xử lý gợi ý tên người dùng
            const firstName = document.getElementById('first-name');
            const lastName = document.getElementById('last-name');
            const username = document.getElementById('username');
            const usernameSuggestions = document.getElementById('username-suggestions');

            function generateUsernameSuggestions() {
                const first = firstName.value.toLowerCase().trim();
                const last = lastName.value.toLowerCase().trim();

                if (first && last) {
                    const suggestions = [
                        `${first}.${last}`,
                        `${first}${last}`,
                        `${first}_${last}`,
                        `${first}${last}${Math.floor(Math.random() * 100)}`,
                        `${first.charAt(0)}${last}`
                    ];

                    usernameSuggestions.innerHTML = suggestions.join(', ');
                }
            }

            firstName.addEventListener('input', generateUsernameSuggestions);
            lastName.addEventListener('input', generateUsernameSuggestions);

            // Xử lý kiểm tra mật khẩu
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm-password');
            const strengthMeter = document.querySelector('.strength-meter-fill');
            const strengthText = document.querySelector('.strength-text');
            const lengthCheck = document.getElementById('length');
            const uppercaseCheck = document.getElementById('uppercase');
            const lowercaseCheck = document.getElementById('lowercase');
            const numberCheck = document.getElementById('number');
            const specialCheck = document.getElementById('special');

            password.addEventListener('input', function() {
                const value = this.value;
                let strength = 0;

                // Kiểm tra độ dài
                if (value.length >= 8) {
                    strength += 1;
                    lengthCheck.innerHTML = '<i class="fas fa-check"></i> Ít nhất 8 ký tự';
                    lengthCheck.classList.add('valid');
                } else {
                    lengthCheck.innerHTML = '<i class="fas fa-times"></i> Ít nhất 8 ký tự';
                    lengthCheck.classList.remove('valid');
                }

                // Kiểm tra chữ hoa
                if (/[A-Z]/.test(value)) {
                    strength += 1;
                    uppercaseCheck.innerHTML = '<i class="fas fa-check"></i> Ít nhất 1 chữ hoa';
                    uppercaseCheck.classList.add('valid');
                } else {
                    uppercaseCheck.innerHTML = '<i class="fas fa-times"></i> Ít nhất 1 chữ hoa';
                    uppercaseCheck.classList.remove('valid');
                }

                // Kiểm tra chữ thường
                if (/[a-z]/.test(value)) {
                    strength += 1;
                    lowercaseCheck.innerHTML = '<i class="fas fa-check"></i> Ít nhất 1 chữ thường';
                    lowercaseCheck.classList.add('valid');
                } else {
                    lowercaseCheck.innerHTML = '<i class="fas fa-times"></i> Ít nhất 1 chữ thường';
                    lowercaseCheck.classList.remove('valid');
                }

                // Kiểm tra số
                if (/[0-9]/.test(value)) {
                    strength += 1;
                    numberCheck.innerHTML = '<i class="fas fa-check"></i> Ít nhất 1 số';
                    numberCheck.classList.add('valid');
                } else {
                    numberCheck.innerHTML = '<i class="fas fa-times"></i> Ít nhất 1 số';
                    numberCheck.classList.remove('valid');
                }

                // Kiểm tra ký tự đặc biệt
                if (/[^A-Za-z0-9]/.test(value)) {
                    strength += 1;
                    specialCheck.innerHTML = '<i class="fas fa-check"></i> Ít nhất 1 ký tự đặc biệt';
                    specialCheck.classList.add('valid');
                } else {
                    specialCheck.innerHTML = '<i class="fas fa-times"></i> Ít nhất 1 ký tự đặc biệt';
                    specialCheck.classList.remove('valid');
                }

                // Cập nhật thanh độ mạnh
                strengthMeter.style.width = `${(strength / 5) * 100}%`;
                strengthMeter.dataset.strength = strength;

                // Cập nhật text
                if (strength === 0) {
                    strengthText.textContent = 'Mật khẩu yếu';
                    strengthText.className = 'strength-text weak';
                } else if (strength < 3) {
                    strengthText.textContent = 'Mật khẩu trung bình';
                    strengthText.className = 'strength-text medium';
                } else if (strength < 5) {
                    strengthText.textContent = 'Mật khẩu mạnh';
                    strengthText.className = 'strength-text strong';
                } else {
                    strengthText.textContent = 'Mật khẩu rất mạnh';
                    strengthText.className = 'strength-text very-strong';
                }
            });

            // Xử lý gợi ý mật khẩu
            const suggestPasswordBtn = document.getElementById('suggest-password');

            suggestPasswordBtn.addEventListener('click', function() {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+';
                let suggestedPassword = '';

                // Đảm bảo có ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt
                suggestedPassword += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
                suggestedPassword += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
                suggestedPassword += '0123456789'[Math.floor(Math.random() * 10)];
                suggestedPassword += '!@#$%^&*()_+'[Math.floor(Math.random() * 12)];

                // Thêm các ký tự ngẫu nhiên cho đủ 12 ký tự
                for (let i = 0; i < 8; i++) {
                    suggestedPassword += chars[Math.floor(Math.random() * chars.length)];
                }

                // Trộn ngẫu nhiên các ký tự
                suggestedPassword = suggestedPassword.split('').sort(() => 0.5 - Math.random()).join('');

                // Cập nhật giá trị mật khẩu
                password.value = suggestedPassword;
                confirmPassword.value = suggestedPassword;

                // Kích hoạt sự kiện input để cập nhật độ mạnh
                password.dispatchEvent(new Event('input'));

                // Hiển thị thông báo
                showNotification('Đã tạo mật khẩu mạnh', 'success');
            });

            // Xử lý hiển thị/ẩn mật khẩu
            const togglePasswordBtns = document.querySelectorAll('.toggle-password');

            togglePasswordBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const input = this.previousElementSibling;
                    const icon = this.querySelector('i');

                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });

            // Không còn sử dụng phần chọn màu sắc yêu thích

            // Xử lý submit form
            const registerForm = document.getElementById('register-form');

            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                // Kiểm tra dữ liệu
                if (validateStep(2)) {
                    // Lấy dữ liệu từ form
                    const firstName = document.getElementById('first-name').value;
                    const lastName = document.getElementById('last-name').value;
                    const email = document.getElementById('email').value;
                    const phone = document.getElementById('phone').value;
                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    // Hiển thị thông báo đang xử lý
                    showNotification('Đang đăng ký tài khoản...', 'info');

                    try {
                        // Gọi API đăng ký
                        const userData = {
                            username: username,
                            email: email,
                            password: password,
                            fullName: `${firstName} ${lastName}`,
                            phone: phone
                        };

                        // Gọi API đăng ký
                        const response = await ApiService.users.register(userData);

                        // Hiển thị thông báo thành công
                        showNotification('Đăng ký tài khoản thành công!', 'success');

                        // Chuyển hướng đến trang chủ hoặc trang trước đó
                        const redirectUrl = localStorage.getItem('redirectAfterLogin') || '../index.html';
                        localStorage.removeItem('redirectAfterLogin'); // Xóa URL chuyển hướng

                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 2000);
                    } catch (error) {
                        // Hiển thị thông báo lỗi
                        showNotification(error || 'Đăng ký thất bại. Vui lòng thử lại!', 'error');

                        // Giả lập đăng ký thành công (chỉ để demo)
                        setTimeout(() => {
                            // Lưu thông tin người dùng vào localStorage
                            localStorage.setItem('isLoggedIn', 'true');
                            localStorage.setItem('username', username);
                            localStorage.setItem('user', JSON.stringify({
                                email: email,
                                username: username,
                                fullName: `${firstName} ${lastName}`,
                                isLoggedIn: true,
                                loginTime: new Date().toISOString()
                            }));

                            // Hiển thị thông báo thành công
                            showNotification('Đăng ký tài khoản thành công (demo)!', 'success');

                            // Chuyển hướng đến trang chủ
                            setTimeout(() => {
                                window.location.href = '../index.html';
                            }, 1000);
                        }, 1500);
                    }
                }
            });
        });

        // Hàm kiểm tra dữ liệu từng bước
        function validateStep(step) {
            switch (step) {
                case 1:
                    // Kiểm tra thông tin cá nhân
                    const firstName = document.getElementById('first-name').value;
                    const lastName = document.getElementById('last-name').value;
                    const email = document.getElementById('email').value;
                    const phone = document.getElementById('phone').value;

                    if (!firstName) {
                        showNotification('Vui lòng nhập họ', 'warning');
                        return false;
                    }

                    if (!lastName) {
                        showNotification('Vui lòng nhập tên', 'warning');
                        return false;
                    }

                    if (!email || !validateEmail(email)) {
                        showNotification('Vui lòng nhập email hợp lệ', 'warning');
                        return false;
                    }

                    if (!phone) {
                        showNotification('Vui lòng nhập số điện thoại', 'warning');
                        return false;
                    }

                    return true;

                case 2:
                    // Kiểm tra thông tin tài khoản
                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirm-password').value;

                    if (!username) {
                        showNotification('Vui lòng nhập tên người dùng', 'warning');
                        return false;
                    }

                    if (!password) {
                        showNotification('Vui lòng nhập mật khẩu', 'warning');
                        return false;
                    }

                    if (password.length < 8) {
                        showNotification('Mật khẩu phải có ít nhất 8 ký tự', 'warning');
                        return false;
                    }

                    if (password !== confirmPassword) {
                        showNotification('Mật khẩu xác nhận không khớp', 'warning');
                        return false;
                    }

                    return true;

                case 2:
                    // Kiểm tra điều khoản
                    const terms = document.getElementById('terms').checked;

                    if (!terms) {
                        showNotification('Vui lòng đồng ý với điều khoản dịch vụ', 'warning');
                        return false;
                    }

                    return true;

                default:
                    return true;
            }
        }

        // Hàm kiểm tra email
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }




        document.addEventListener("click", function() {
        let youtubePlayer = document.getElementById("youtube-player");
        if (youtubePlayer.src.includes("mute=1")) {
            youtubePlayer.src = "https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=0";
            console.log("✅ Đã bật tiếng cho YouTube!");
        }
    }, { once: true }); // Chỉ chạy một lần


    </script>
</body>
</html>
