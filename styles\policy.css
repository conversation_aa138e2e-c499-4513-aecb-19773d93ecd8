/* Policy Pages Styles */

.policy-container {
    padding: 60px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 50px;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #343a40;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.page-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #ff6b6b;
}

.page-header p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

.policy-content {
    max-width: 900px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 40px;
}

.policy-section {
    margin-bottom: 40px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 30px;
}

.policy-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.policy-section h2 {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 20px;
    position: relative;
    padding-left: 20px;
}

.policy-section h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 25px;
    background-color: #ff6b6b;
    border-radius: 3px;
}

.policy-section p {
    font-size: 1rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 15px;
}

.policy-section ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.policy-section ul li {
    font-size: 1rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 10px;
    position: relative;
    padding-left: 15px;
}

.policy-section ul li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #ff6b6b;
}

.shipping-table {
    margin: 20px 0;
    overflow-x: auto;
}

.shipping-table table {
    width: 100%;
    border-collapse: collapse;
}

.shipping-table th, .shipping-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.shipping-table th {
    background-color: #f8f9fa;
    color: #343a40;
    font-weight: 600;
}

.shipping-table tr:last-child td {
    border-bottom: none;
}

.shipping-table tr:hover td {
    background-color: #f8f9fa;
}

.shipping-partners {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.shipping-partners li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-weight: 500;
}

.shipping-partners li img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.special-shipping {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.special-shipping-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.special-shipping-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.special-shipping-icon {
    width: 50px;
    height: 50px;
    background-color: #ff6b6b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.special-shipping-content h3 {
    font-size: 1.1rem;
    color: #343a40;
    margin-bottom: 10px;
}

.special-shipping-content p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.faq-container {
    margin-top: 20px;
}

.faq-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.faq-question {
    padding: 15px 20px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.faq-question h3 {
    font-size: 1rem;
    color: #343a40;
    margin: 0;
}

.faq-toggle {
    color: #6c757d;
    transition: all 0.3s ease;
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 15px 20px;
    max-height: 500px;
}

.faq-item.active .faq-toggle {
    transform: rotate(180deg);
}

.policy-contact {
    text-align: center;
    background-color: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin-top: 40px;
}

.policy-contact h2 {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 15px;
}

.policy-contact p {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 20px;
}

.contact-methods {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-method i {
    font-size: 1.2rem;
    color: #ff6b6b;
}

.contact-method p {
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
    .policy-content {
        padding: 30px 20px;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .special-shipping {
        grid-template-columns: 1fr;
    }
    
    .contact-methods {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
}
