/* Product Page Styles */
@import url('style.css');

/* Product Header */
.product-header {
    padding: 2rem 0;
    text-align: center;
    background-color: var(--secondary-color);
    margin-bottom: 2rem;
}

.product-header h1 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-header p {
    color: var(--text-color);
    font-size: 1rem;
}

.product-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.5rem 0;
    font-size: 0.9rem;
}

.product-breadcrumb a {
    color: var(--text-color);
}

.product-breadcrumb .current {
    color: var(--accent-color);
    font-weight: 500;
}

.product-breadcrumb .separator {
    margin: 0 0.8rem;
    color: #999;
}

/* Product Layout */
.product {
    display: flex;
    padding: 2rem 0;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Product Filter */
.product-filter {
    flex: 0 0 250px;
    padding: 1.5rem;
    background-color: #fff;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    align-self: flex-start;
    position: sticky;
    top: 100px;
}

.product-filter > p {
    font-family: var(--heading-font);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.short_line {
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
    margin-bottom: 1.5rem;
}

.filter-div {
    margin-bottom: 1.5rem;
}

.filter-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
}

.filter-head p {
    font-weight: 500;
    font-size: 1rem;
    color: var(--primary-color);
}

.filter-head i {
    font-size: 0.8rem;
    transition: transform var(--transition-fast);
}

.filter-div.active .filter-head i {
    transform: rotate(180deg);
}

.filter-option {
    padding: 1rem 0;
    display: none;
}

.filter-div.active .filter-option {
    display: block;
}

.filter-option ul {
    list-style: none;
}

.filter-option li {
    padding: 0.5rem 0;
    font-size: 0.9rem;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.filter-option li:hover {
    color: var(--accent-color);
}

/* Products Side */
.products-side {
    flex: 1;
}

.products-side h2 {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

/* Product Sorting */
.product-sorting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.product-sorting > div > ul {
    display: flex;
    gap: 1.5rem;
}

.product-sorting > div > ul > li {
    font-size: 0.9rem;
    cursor: pointer;
    position: relative;
}

.sorting_header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.sorting_header:hover {
    background-color: var(--secondary-color);
}

.sorting_div {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: #fff;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 10;
    display: none;
    overflow: hidden;
    padding: 1rem;
}

.sorting_div ul {
    list-style: none;
}

.sortPrice {
    padding: 0.8rem 1rem;
    font-size: 0.85rem;
    transition: background-color var(--transition-fast);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
}

.sortPrice:hover {
    background-color: var(--secondary-color);
}

/* Featured Products */
.product_big--img {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.product_big--img > div {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.product_big--img > div:hover {
    transform: translateY(-5px);
}

/* Product Grid */
.products-show {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.products-show > div {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.products-show > div:hover {
    transform: translateY(-5px);
}

/* Product Item */
.product-img {
    position: relative;
    overflow: hidden;
}

.product-img img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.product-img:hover img {
    transform: scale(1.05);
}

.product-actions {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity var(--transition-medium), transform var(--transition-medium);
    z-index: 2;
}

.product-img:hover .product-actions {
    opacity: 1;
    transform: translateX(-50%) translateY(-10px);
}

.btn-quick-view, .btn-add-to-cart {
    padding: 0.8rem 1rem;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-quick-view {
    background-color: #fff;
    color: var(--primary-color);
}

.btn-quick-view:hover {
    background-color: var(--secondary-color);
}

.btn-add-to-cart {
    background-color: var(--accent-color);
    color: #fff;
}

.btn-add-to-cart:hover {
    background-color: var(--accent-hover);
}

.heart-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: #fff;
    color: #999;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: color var(--transition-fast), transform var(--transition-fast);
    z-index: 2;
}

.heart-icon:hover {
    color: var(--accent-color);
    transform: scale(1.1);
}

/* Product Info */
.products-name {
    padding: 1.2rem;
}

.Colors-plate {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

.Colors-plate > span {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.Colors-plate > span:hover {
    transform: scale(1.2);
}

.active-color-border {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.products-name p:nth-child(2) {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
    height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.products-name p:nth-child(3) {
    font-weight: 600;
    color: var(--accent-color);
    font-size: 1.1rem;
}

/* Color Definitions */
.black { background-color: #1b1a1d; }
.lightslategrey { background-color: #8891b2; }
.florwhite { background-color: #faf8f2; }
.darkslategrey { background-color: #47473e; }
.tan { background-color: #d6be9d; }
.sliver { background-color: #bab7ba; }
.lavender { background-color: #ebebeb; }
.darkgrey { background-color: #96a7af; }
.dimigrey { background-color: #7c635e; }

/* Search Results */
#SearchResult.products-show {
    grid-template-columns: repeat(4, 1fr);
}

#SearchResult.no-result {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

#SearchResult.no-result h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    text-align: center;
}

/* Responsive */
@media (max-width: 1200px) {
    .products-show {
        grid-template-columns: repeat(2, 1fr);
    }

    #SearchResult.products-show {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .product {
        flex-direction: column;
    }

    .product-filter {
        flex: none;
        width: 100%;
        position: static;
    }

    #SearchResult.products-show {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .product_big--img {
        grid-template-columns: 1fr;
    }

    .products-show {
        grid-template-columns: 1fr;
    }

    #SearchResult.products-show {
        grid-template-columns: 1fr;
    }

    .product-sorting > div > ul {
        display: none;
    }
}
