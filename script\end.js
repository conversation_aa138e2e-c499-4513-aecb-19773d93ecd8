// Order Confirmation Page Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    }
    
    // Load order details
    loadOrderDetails();
    
    // Load recommended products
    loadRecommendedProducts();
});

// Load order details from localStorage
function loadOrderDetails() {
    // Get order from localStorage
    const order = JSON.parse(localStorage.getItem('current_order'));
    
    if (!order) {
        // If no order is found, redirect to home page
        // window.location.href = 'index.html';
        // For demo purposes, we'll use sample data instead
        useSampleOrderData();
        return;
    }
    
    // Update order information
    updateOrderInfo(order);
    
    // Update shipping information
    updateShippingInfo(order);
    
    // Load order items
    loadOrderItems(order.items);
    
    // Update order totals
    updateOrderTotals(order);
}

// Use sample order data for demo purposes
function useSampleOrderData() {
    const sampleOrder = {
        orderNumber: 'FS123456789',
        date: new Date().toISOString(),
        payment: {
            method: 'credit-card'
        },
        shipping: {
            method: 'standard',
            country: 'VN',
            address: '123 Đường Nguyễn Trãi, Quận 1',
            city: 'TP. Hồ Chí Minh',
            zip: '70000'
        },
        customer: {
            firstName: 'Nguyễn',
            lastName: 'Văn A',
            email: '<EMAIL>',
            phone: '+84 (0) 123 456 789'
        },
        items: [
            {
                id: 1,
                name: 'Áo sơ mi nữ dài tay',
                price: '350.000đ',
                image: './img womens/img women 1.webp',
                color: 'Trắng',
                size: 'M',
                quantity: 1
            },
            {
                id: 2,
                name: 'Quần jean nam ống suông',
                price: '450.000đ',
                image: './img mens/men 5.webp',
                color: 'Xanh',
                size: '32',
                quantity: 1
            }
        ],
        subtotal: 800000,
        shipping: 0,
        discount: 80000,
        total: 720000
    };
    
    // Update order information
    updateOrderInfo(sampleOrder);
    
    // Update shipping information
    updateShippingInfo(sampleOrder);
    
    // Load order items
    loadOrderItems(sampleOrder.items);
    
    // Update order totals
    updateOrderTotals(sampleOrder);
}

// Update order information
function updateOrderInfo(order) {
    // Order number
    const orderNumberElement = document.getElementById('order-number');
    if (orderNumberElement) {
        orderNumberElement.textContent = order.orderNumber;
    }
    
    // Order date
    const orderDateElement = document.getElementById('order-date');
    if (orderDateElement) {
        const date = new Date(order.date);
        orderDateElement.textContent = formatDate(date);
    }
    
    // Payment method
    const paymentMethodElement = document.getElementById('payment-method');
    if (paymentMethodElement) {
        let paymentMethod = 'Không xác định';
        
        switch (order.payment.method) {
            case 'credit-card':
                paymentMethod = 'Thẻ tín dụng/ghi nợ';
                break;
            case 'cod':
                paymentMethod = 'Thanh toán khi nhận hàng (COD)';
                break;
            case 'momo':
                paymentMethod = 'Ví điện tử';
                break;
        }
        
        paymentMethodElement.textContent = paymentMethod;
    }
    
    // Shipping method
    const shippingMethodElement = document.getElementById('shipping-method');
    if (shippingMethodElement) {
        let shippingMethod = 'Không xác định';
        
        switch (order.shipping.method) {
            case 'standard':
                shippingMethod = 'Giao hàng tiêu chuẩn';
                break;
            case 'express':
                shippingMethod = 'Giao hàng nhanh';
                break;
        }
        
        shippingMethodElement.textContent = shippingMethod;
    }
}

// Update shipping information
function updateShippingInfo(order) {
    // Customer name
    const customerNameElement = document.getElementById('customer-name');
    if (customerNameElement) {
        customerNameElement.textContent = `${order.customer.firstName} ${order.customer.lastName}`;
    }
    
    // Customer address
    const customerAddressElement = document.getElementById('customer-address');
    if (customerAddressElement) {
        customerAddressElement.textContent = order.shipping.address;
    }
    
    // Customer city
    const customerCityElement = document.getElementById('customer-city');
    if (customerCityElement) {
        let country = 'Việt Nam';
        
        switch (order.shipping.country) {
            case 'US':
                country = 'United States';
                break;
            case 'UK':
                country = 'United Kingdom';
                break;
            case 'CA':
                country = 'Canada';
                break;
            case 'AU':
                country = 'Australia';
                break;
        }
        
        customerCityElement.textContent = `${order.shipping.city}, ${country}`;
    }
    
    // Customer phone
    const customerPhoneElement = document.getElementById('customer-phone');
    if (customerPhoneElement) {
        customerPhoneElement.textContent = order.customer.phone;
    }
}

// Load order items
function loadOrderItems(items) {
    const orderItemsContainer = document.getElementById('order-items');
    if (!orderItemsContainer) return;
    
    // Clear container
    orderItemsContainer.innerHTML = '';
    
    // Add items to container
    items.forEach(item => {
        // Calculate item total
        const price = parseInt(item.price.replace(/\D/g, ''));
        const quantity = item.quantity || 1;
        const itemTotal = price * quantity;
        
        // Create item element
        const itemElement = document.createElement('div');
        itemElement.className = 'order-item';
        
        itemElement.innerHTML = `
            <div class="order-item-image">
                <img src="${item.image}" alt="${item.name}">
            </div>
            <div class="order-item-details">
                <div class="order-item-name">${item.name}</div>
                <div class="order-item-variant">
                    <span>Màu: ${item.color || 'N/A'}</span> | 
                    <span>Size: ${item.size || 'N/A'}</span> | 
                    <span>SL: ${quantity}</span>
                </div>
                <div class="order-item-price">
                    <span>${formatCurrency(price)}đ</span>
                    <span>${formatCurrency(itemTotal)}đ</span>
                </div>
            </div>
        `;
        
        orderItemsContainer.appendChild(itemElement);
    });
}

// Update order totals
function updateOrderTotals(order) {
    // Update DOM
    const subtotalElement = document.getElementById('subtotal');
    const shippingCostElement = document.getElementById('shipping-cost');
    const discountElement = document.getElementById('discount');
    const totalElement = document.getElementById('total');
    
    if (subtotalElement) subtotalElement.textContent = `${formatCurrency(order.subtotal)}đ`;
    if (shippingCostElement) shippingCostElement.textContent = order.shipping > 0 ? `${formatCurrency(order.shipping)}đ` : 'Miễn phí';
    if (discountElement) discountElement.textContent = order.discount > 0 ? `-${formatCurrency(order.discount)}đ` : '0đ';
    if (totalElement) totalElement.textContent = `${formatCurrency(order.total)}đ`;
}

// Load recommended products
function loadRecommendedProducts() {
    const productsContainer = document.getElementById('recommended-products');
    if (!productsContainer) return;
    
    // Sample recommended products
    const recommendedProducts = [
        {
            id: 1,
            name: 'Áo sơ mi nữ dài tay',
            price: '350.000đ',
            image: './img womens/img women 1.webp'
        },
        {
            id: 2,
            name: 'Quần jean nam ống suông',
            price: '450.000đ',
            image: './img mens/men 5.webp'
        },
        {
            id: 3,
            name: 'Váy đầm nữ dáng xòe',
            price: '550.000đ',
            image: './img womens/women 11.webp'
        },
        {
            id: 4,
            name: 'Áo thun nam cổ tròn',
            price: '250.000đ',
            image: './img mens/men 4.webp'
        }
    ];
    
    // Clear container
    productsContainer.innerHTML = '';
    
    // Add products to container
    recommendedProducts.forEach(product => {
        const productElement = document.createElement('div');
        productElement.className = 'product-card';
        
        productElement.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-price">${product.price}</p>
            </div>
        `;
        
        productsContainer.appendChild(productElement);
    });
}

// Format date (DD/MM/YYYY)
function formatDate(date) {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
}

// Format currency
function formatCurrency(value) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}
