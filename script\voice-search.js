// Voice Search Feature

document.addEventListener('DOMContentLoaded', function() {
    // Check if browser supports speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        initVoiceSearch();
    } else {
        console.log('Speech recognition not supported');
    }
});

// Initialize voice search
function initVoiceSearch() {
    // Add voice search button to all search boxes
    const searchBoxes = document.querySelectorAll('.search-box');
    
    searchBoxes.forEach(searchBox => {
        // Check if button already exists
        if (!searchBox.querySelector('.voice-search-btn')) {
            // Create voice search button
            const voiceSearchBtn = document.createElement('button');
            voiceSearchBtn.className = 'voice-search-btn';
            voiceSearchBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            voiceSearchBtn.title = 'T<PERSON><PERSON> kiếm bằng giọng nói';
            
            // Insert button before the search button
            const searchBtn = searchBox.querySelector('#search-btn');
            if (searchBtn) {
                searchBox.insertBefore(voiceSearchBtn, searchBtn);
            } else {
                searchBox.appendChild(voiceSearchBtn);
            }
            
            // Add click event
            voiceSearchBtn.addEventListener('click', startVoiceSearch);
        }
    });
}

// Start voice search
function startVoiceSearch(event) {
    event.preventDefault();
    
    // Get search input
    const searchBox = event.target.closest('.search-box');
    const searchInput = searchBox.querySelector('#search-input');
    
    if (!searchInput) return;
    
    // Create speech recognition instance
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    // Configure recognition
    recognition.lang = 'vi-VN'; // Vietnamese language
    recognition.continuous = false;
    recognition.interimResults = false;
    
    // Create and show listening indicator
    const listeningIndicator = createListeningIndicator();
    document.body.appendChild(listeningIndicator);
    
    // Change microphone icon to indicate listening
    const voiceSearchBtn = event.target.closest('.voice-search-btn');
    voiceSearchBtn.classList.add('listening');
    
    // Start recognition
    recognition.start();
    
    // Recognition result event
    recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        searchInput.value = transcript;
        
        // Show what was recognized
        showRecognitionResult(transcript);
        
        // Submit search after a short delay
        setTimeout(() => {
            performSearch();
        }, 1500);
    };
    
    // Recognition end event
    recognition.onend = function() {
        // Remove listening indicator
        if (listeningIndicator) {
            listeningIndicator.classList.add('fade-out');
            setTimeout(() => {
                listeningIndicator.remove();
            }, 300);
        }
        
        // Reset microphone icon
        voiceSearchBtn.classList.remove('listening');
    };
    
    // Recognition error event
    recognition.onerror = function(event) {
        console.error('Speech recognition error', event.error);
        
        // Show error message
        if (event.error === 'no-speech') {
            showNotification('Không nghe thấy giọng nói. Vui lòng thử lại.', 'warning');
        } else if (event.error === 'network') {
            showNotification('Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn.', 'error');
        } else {
            showNotification('Đã xảy ra lỗi. Vui lòng thử lại.', 'error');
        }
        
        // Remove listening indicator
        if (listeningIndicator) {
            listeningIndicator.classList.add('fade-out');
            setTimeout(() => {
                listeningIndicator.remove();
            }, 300);
        }
        
        // Reset microphone icon
        voiceSearchBtn.classList.remove('listening');
    };
}

// Create listening indicator
function createListeningIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'voice-search-indicator';
    
    indicator.innerHTML = `
        <div class="voice-search-indicator-content">
            <div class="voice-waves">
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
            </div>
            <div class="voice-search-text">Đang nghe...</div>
        </div>
    `;
    
    return indicator;
}

// Show recognition result
function showRecognitionResult(transcript) {
    // Create result element
    const resultElement = document.createElement('div');
    resultElement.className = 'voice-search-result';
    
    resultElement.innerHTML = `
        <div class="voice-search-result-content">
            <div class="voice-search-result-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="voice-search-result-text">
                <div class="voice-search-result-title">Đã nhận diện:</div>
                <div class="voice-search-result-transcript">"${transcript}"</div>
            </div>
        </div>
    `;
    
    // Add to body
    document.body.appendChild(resultElement);
    
    // Show with animation
    setTimeout(() => {
        resultElement.classList.add('show');
    }, 10);
    
    // Remove after delay
    setTimeout(() => {
        resultElement.classList.remove('show');
        setTimeout(() => {
            resultElement.remove();
        }, 300);
    }, 3000);
}

// Add styles for voice search
function addVoiceSearchStyles() {
    // Check if styles already exist
    if (document.getElementById('voice-search-styles')) return;
    
    // Create style element
    const style = document.createElement('style');
    style.id = 'voice-search-styles';
    
    // Add CSS
    style.innerHTML = `
        /* Voice Search Button */
        .voice-search-btn {
            background: none;
            border: none;
            color: #868e96;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 0 10px;
        }
        
        .voice-search-btn:hover {
            color: #ff6b6b;
        }
        
        .voice-search-btn.listening {
            color: #ff6b6b;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
        
        /* Voice Search Indicator */
        .voice-search-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease;
        }
        
        .voice-search-indicator.fade-out {
            animation: fadeOut 0.3s ease;
        }
        
        .voice-search-indicator-content {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .voice-waves {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            margin-bottom: 20px;
        }
        
        .voice-wave {
            width: 5px;
            height: 40px;
            margin: 0 3px;
            border-radius: 5px;
            background-color: #ff6b6b;
            animation: wave 1s infinite ease-in-out;
        }
        
        .voice-wave:nth-child(2) {
            animation-delay: 0.1s;
        }
        
        .voice-wave:nth-child(3) {
            animation-delay: 0.2s;
        }
        
        .voice-wave:nth-child(4) {
            animation-delay: 0.3s;
        }
        
        .voice-wave:nth-child(5) {
            animation-delay: 0.4s;
        }
        
        @keyframes wave {
            0%, 100% {
                height: 20px;
            }
            50% {
                height: 60px;
            }
        }
        
        .voice-search-text {
            font-size: 1.2rem;
            color: #343a40;
            font-weight: 500;
        }
        
        /* Voice Search Result */
        .voice-search-result {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            background-color: white;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            max-width: 80%;
        }
        
        .voice-search-result.show {
            transform: translateX(-50%) translateY(0);
        }
        
        .voice-search-result-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .voice-search-result-icon {
            color: #40c057;
            font-size: 1.5rem;
        }
        
        .voice-search-result-title {
            font-size: 0.9rem;
            color: #868e96;
            margin-bottom: 5px;
        }
        
        .voice-search-result-transcript {
            font-size: 1.1rem;
            color: #343a40;
            font-weight: 500;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }
    `;
    
    // Add to head
    document.head.appendChild(style);
}

// Call to add styles
addVoiceSearchStyles();
