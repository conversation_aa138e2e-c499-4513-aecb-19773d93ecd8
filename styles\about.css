/* About Page Styles */
@import url('style.css');

/* Hero Section */
.about-hero {
    height: 500px;
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../img banner/banner 3.avif');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    margin-bottom: 4rem;
}

.about-hero-content {
    max-width: 800px;
    padding: 0 2rem;
}

.about-hero h1 {
    font-family: var(--heading-font);
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

.about-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* About Section */
.about-section {
    padding: 5rem 0;
}

.bg-light {
    background-color: var(--secondary-color);
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-grid.reverse {
    grid-template-columns: 1fr 1fr;
    direction: rtl;
}

.about-grid.reverse .about-content,
.about-grid.reverse .about-image {
    direction: ltr;
}

.about-content h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.about-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--text-color);
}

.about-content ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.about-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
    color: var(--text-color);
}

.about-image {
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.about-image:hover img {
    transform: scale(1.05);
}

/* Values Section */
.values-section {
    padding: 5rem 0;
}

.section-title {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 3rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.value-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.value-card:hover {
    transform: translateY(-10px);
}

.value-icon {
    width: 80px;
    height: 80px;
    background-color: var(--accent-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
}

.value-card h3 {
    font-family: var(--heading-font);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.value-card p {
    color: var(--text-color);
    line-height: 1.6;
}

/* Team Section */
.team-section {
    padding: 5rem 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.team-member {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    height: 300px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: 1.5rem;
}

.member-info h3 {
    font-family: var(--heading-font);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.member-position {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-bio {
    color: var(--text-color);
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
    padding: 5rem 0;
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
}

.testimonial {
    padding: 2rem;
}

.testimonial-content {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-content:after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50px;
    border-width: 20px 20px 0;
    border-style: solid;
    border-color: #fff transparent;
}

.testimonial-content p {
    color: var(--text-color);
    line-height: 1.6;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    padding-left: 2rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    object-fit: cover;
}

.testimonial-author h4 {
    font-family: var(--heading-font);
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 0.3rem;
}

.testimonial-author p {
    color: var(--text-color);
    font-size: 0.9rem;
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-info h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.contact-info > p {
    margin-bottom: 2rem;
    line-height: 1.6;
    color: var(--text-color);
}

.contact-details {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.contact-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-right: 1rem;
    margin-top: 0.2rem;
}

.contact-item p {
    color: var(--text-color);
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
}

.social-link:hover {
    background-color: var(--accent-color);
}

.contact-form {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--accent-color);
    outline: none;
}

/* Responsive */
@media (max-width: 1200px) {
    .values-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .about-grid,
    .about-grid.reverse,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-grid.reverse {
        direction: ltr;
    }
    
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .about-hero h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
    }
    
    .about-hero {
        height: 400px;
    }
    
    .about-hero h1 {
        font-size: 2rem;
    }
}
