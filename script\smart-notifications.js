// Smart Notifications System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize notification system
    initNotificationSystem();
    
    // Check for notifications to display
    setTimeout(() => {
        checkAndDisplayNotifications();
    }, 3000); // Wait 3 seconds before showing first notification
});

// Notification types
const NOTIFICATION_TYPES = {
    WELCOME: 'welcome',
    PRODUCT_RECOMMENDATION: 'product_recommendation',
    CART_REMINDER: 'cart_reminder',
    SALE_ALERT: 'sale_alert',
    WISHLIST_UPDATE: 'wishlist_update',
    BACK_IN_STOCK: 'back_in_stock',
    PRICE_DROP: 'price_drop',
    SEASONAL: 'seasonal',
    LIMITED_OFFER: 'limited_offer',
    PERSONALIZED_OFFER: 'personalized_offer'
};

// Notification priorities
const NOTIFICATION_PRIORITIES = {
    LOW: 1,
    MEDIUM: 2,
    HIGH: 3,
    URGENT: 4
};

// Notification queue
let notificationQueue = [];

// Notification history
let notificationHistory = [];

// Notification settings
let notificationSettings = {
    enabled: true,
    frequency: 'medium', // low, medium, high
    types: {
        welcome: true,
        product_recommendation: true,
        cart_reminder: true,
        sale_alert: true,
        wishlist_update: true,
        back_in_stock: true,
        price_drop: true,
        seasonal: true,
        limited_offer: true,
        personalized_offer: true
    },
    doNotDisturb: false,
    doNotDisturbStart: '22:00',
    doNotDisturbEnd: '08:00'
};

// Initialize notification system
function initNotificationSystem() {
    // Load notification settings from localStorage
    loadNotificationSettings();
    
    // Load notification history from localStorage
    loadNotificationHistory();
    
    // Create notification container if it doesn't exist
    createNotificationContainer();
    
    // Generate initial notifications
    generateInitialNotifications();
}

// Load notification settings from localStorage
function loadNotificationSettings() {
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
        notificationSettings = JSON.parse(savedSettings);
    }
}

// Save notification settings to localStorage
function saveNotificationSettings() {
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
}

// Load notification history from localStorage
function loadNotificationHistory() {
    const savedHistory = localStorage.getItem('notificationHistory');
    if (savedHistory) {
        notificationHistory = JSON.parse(savedHistory);
    }
}

// Save notification history to localStorage
function saveNotificationHistory() {
    localStorage.setItem('notificationHistory', JSON.stringify(notificationHistory));
}

// Create notification container
function createNotificationContainer() {
    let container = document.querySelector('.smart-notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'smart-notification-container';
        document.body.appendChild(container);
    }
}

// Generate initial notifications
function generateInitialNotifications() {
    // Check if user is new or returning
    const user = localStorage.getItem('user');
    const isNewUser = !user;
    
    // Welcome notification for new users
    if (isNewUser) {
        addNotification({
            type: NOTIFICATION_TYPES.WELCOME,
            title: 'Chào mừng đến với Fashion Store!',
            message: 'Khám phá xu hướng thời trang mới nhất và nhận ưu đãi đặc biệt dành cho thành viên mới.',
            icon: 'fas fa-smile',
            priority: NOTIFICATION_PRIORITIES.HIGH,
            action: {
                text: 'Đăng ký ngay',
                url: './pages/register.html'
            },
            duration: 10000
        });
    } else {
        // Welcome back notification for returning users
        const userData = JSON.parse(user);
        addNotification({
            type: NOTIFICATION_TYPES.WELCOME,
            title: `Chào mừng trở lại, ${userData.name || 'quý khách'}!`,
            message: 'Chúng tôi có một số gợi ý mới dành cho bạn dựa trên lịch sử mua sắm của bạn.',
            icon: 'fas fa-hand-wave',
            priority: NOTIFICATION_PRIORITIES.MEDIUM,
            action: {
                text: 'Xem ngay',
                url: '#personalized-recommendations'
            },
            duration: 8000
        });
    }
    
    // Check for abandoned cart
    const cart = localStorage.getItem('cart');
    if (cart) {
        const cartItems = JSON.parse(cart);
        if (cartItems.length > 0) {
            addNotification({
                type: NOTIFICATION_TYPES.CART_REMINDER,
                title: 'Giỏ hàng của bạn đang chờ',
                message: `Bạn có ${cartItems.length} sản phẩm trong giỏ hàng. Hoàn tất đơn hàng ngay?`,
                icon: 'fas fa-shopping-cart',
                priority: NOTIFICATION_PRIORITIES.MEDIUM,
                action: {
                    text: 'Thanh toán',
                    url: './AddCart.html'
                },
                duration: 8000
            });
        }
    }
    
    // Seasonal notification
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    
    // Summer collection (May-July)
    if (currentMonth >= 4 && currentMonth <= 6) {
        addNotification({
            type: NOTIFICATION_TYPES.SEASONAL,
            title: 'Bộ sưu tập mùa hè đã ra mắt!',
            message: 'Khám phá những xu hướng mới nhất cho mùa hè năm nay.',
            icon: 'fas fa-sun',
            priority: NOTIFICATION_PRIORITIES.MEDIUM,
            action: {
                text: 'Xem bộ sưu tập',
                url: './womenproducts.html'
            },
            duration: 7000
        });
    }
    // Fall collection (August-October)
    else if (currentMonth >= 7 && currentMonth <= 9) {
        addNotification({
            type: NOTIFICATION_TYPES.SEASONAL,
            title: 'Bộ sưu tập thu đông đã sẵn sàng!',
            message: 'Chuẩn bị cho mùa thu với những thiết kế mới nhất của chúng tôi.',
            icon: 'fas fa-leaf',
            priority: NOTIFICATION_PRIORITIES.MEDIUM,
            action: {
                text: 'Xem bộ sưu tập',
                url: './womenproducts.html'
            },
            duration: 7000
        });
    }
    // Winter collection (November-January)
    else if (currentMonth >= 10 || currentMonth <= 0) {
        addNotification({
            type: NOTIFICATION_TYPES.SEASONAL,
            title: 'Bộ sưu tập mùa đông ấm áp',
            message: 'Giữ ấm phong cách với bộ sưu tập mùa đông mới nhất.',
            icon: 'fas fa-snowflake',
            priority: NOTIFICATION_PRIORITIES.MEDIUM,
            action: {
                text: 'Xem bộ sưu tập',
                url: './womenproducts.html'
            },
            duration: 7000
        });
    }
    // Spring collection (February-April)
    else {
        addNotification({
            type: NOTIFICATION_TYPES.SEASONAL,
            title: 'Bộ sưu tập xuân 2024 đã ra mắt!',
            message: 'Đón chào mùa xuân với những thiết kế tươi mới nhất.',
            icon: 'fas fa-seedling',
            priority: NOTIFICATION_PRIORITIES.MEDIUM,
            action: {
                text: 'Xem bộ sưu tập',
                url: './womenproducts.html'
            },
            duration: 7000
        });
    }
    
    // Limited time offer
    addNotification({
        type: NOTIFICATION_TYPES.LIMITED_OFFER,
        title: 'Ưu đãi giới hạn!',
        message: 'Giảm 30% cho tất cả sản phẩm mới. Chỉ còn 2 ngày!',
        icon: 'fas fa-clock',
        priority: NOTIFICATION_PRIORITIES.HIGH,
        action: {
            text: 'Mua ngay',
            url: './pages/sale.html'
        },
        duration: 10000
    });
}

// Add notification to queue
function addNotification(notification) {
    // Check if notifications are enabled
    if (!notificationSettings.enabled) {
        return;
    }
    
    // Check if this notification type is enabled
    if (!notificationSettings.types[notification.type]) {
        return;
    }
    
    // Check if in do not disturb mode
    if (notificationSettings.doNotDisturb && isInDoNotDisturbPeriod()) {
        return;
    }
    
    // Check if similar notification was shown recently
    if (wasShownRecently(notification)) {
        return;
    }
    
    // Add to queue
    notificationQueue.push(notification);
    
    // Sort queue by priority
    notificationQueue.sort((a, b) => b.priority - a.priority);
}

// Check if in do not disturb period
function isInDoNotDisturbPeriod() {
    if (!notificationSettings.doNotDisturb) {
        return false;
    }
    
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = currentHour * 60 + currentMinute;
    
    const startParts = notificationSettings.doNotDisturbStart.split(':');
    const startHour = parseInt(startParts[0]);
    const startMinute = parseInt(startParts[1]);
    const startTime = startHour * 60 + startMinute;
    
    const endParts = notificationSettings.doNotDisturbEnd.split(':');
    const endHour = parseInt(endParts[0]);
    const endMinute = parseInt(endParts[1]);
    const endTime = endHour * 60 + endMinute;
    
    // Handle overnight periods
    if (startTime > endTime) {
        return currentTime >= startTime || currentTime <= endTime;
    } else {
        return currentTime >= startTime && currentTime <= endTime;
    }
}

// Check if similar notification was shown recently
function wasShownRecently(notification) {
    // Check last 5 notifications in history
    const recentNotifications = notificationHistory.slice(-5);
    
    return recentNotifications.some(recent => {
        // If same type and shown in the last hour
        if (recent.type === notification.type) {
            const timeDiff = Date.now() - recent.timestamp;
            const oneHour = 60 * 60 * 1000;
            return timeDiff < oneHour;
        }
        return false;
    });
}

// Check and display notifications
function checkAndDisplayNotifications() {
    // If queue is empty or a notification is currently showing, return
    if (notificationQueue.length === 0 || document.querySelector('.smart-notification.show')) {
        // Schedule next check
        scheduleNextCheck();
        return;
    }
    
    // Get next notification from queue
    const notification = notificationQueue.shift();
    
    // Display notification
    displayNotification(notification);
    
    // Add to history
    addToHistory(notification);
    
    // Schedule next check
    scheduleNextCheck();
}

// Schedule next notification check
function scheduleNextCheck() {
    // Determine delay based on frequency setting
    let delay;
    switch (notificationSettings.frequency) {
        case 'low':
            delay = 120000; // 2 minutes
            break;
        case 'high':
            delay = 30000; // 30 seconds
            break;
        case 'medium':
        default:
            delay = 60000; // 1 minute
            break;
    }
    
    // If there are high priority notifications in the queue, reduce delay
    const hasHighPriority = notificationQueue.some(notification => 
        notification.priority >= NOTIFICATION_PRIORITIES.HIGH
    );
    
    if (hasHighPriority) {
        delay = Math.floor(delay / 2);
    }
    
    // Schedule next check
    setTimeout(() => {
        checkAndDisplayNotifications();
    }, delay);
}

// Add notification to history
function addToHistory(notification) {
    // Add timestamp
    notification.timestamp = Date.now();
    
    // Add to history
    notificationHistory.push(notification);
    
    // Keep history limited to last 50 notifications
    if (notificationHistory.length > 50) {
        notificationHistory.shift();
    }
    
    // Save history
    saveNotificationHistory();
}

// Display notification
function displayNotification(notification) {
    // Get container
    const container = document.querySelector('.smart-notification-container');
    
    // Create notification element
    const notificationElement = document.createElement('div');
    notificationElement.className = 'smart-notification';
    
    // Add priority class
    switch (notification.priority) {
        case NOTIFICATION_PRIORITIES.URGENT:
            notificationElement.classList.add('urgent');
            break;
        case NOTIFICATION_PRIORITIES.HIGH:
            notificationElement.classList.add('high');
            break;
        case NOTIFICATION_PRIORITIES.MEDIUM:
            notificationElement.classList.add('medium');
            break;
        case NOTIFICATION_PRIORITIES.LOW:
        default:
            notificationElement.classList.add('low');
            break;
    }
    
    // Set notification content
    notificationElement.innerHTML = `
        <div class="notification-icon">
            <i class="${notification.icon}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-message">${notification.message}</div>
            ${notification.action ? `<a href="${notification.action.url}" class="notification-action">${notification.action.text}</a>` : ''}
        </div>
        <div class="notification-close">&times;</div>
    `;
    
    // Add to container
    container.appendChild(notificationElement);
    
    // Show notification with animation
    setTimeout(() => {
        notificationElement.classList.add('show');
    }, 10);
    
    // Add close event
    const closeBtn = notificationElement.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notificationElement);
    });
    
    // Auto hide after duration
    setTimeout(() => {
        hideNotification(notificationElement);
    }, notification.duration || 5000);
}

// Hide notification
function hideNotification(notificationElement) {
    // Remove show class
    notificationElement.classList.remove('show');
    
    // Remove from DOM after animation completes
    setTimeout(() => {
        notificationElement.remove();
    }, 300);
}

// Public API
window.smartNotifications = {
    // Add a custom notification
    add: addNotification,
    
    // Update notification settings
    updateSettings: function(settings) {
        notificationSettings = {...notificationSettings, ...settings};
        saveNotificationSettings();
    },
    
    // Get current notification settings
    getSettings: function() {
        return {...notificationSettings};
    },
    
    // Enable notifications
    enable: function() {
        notificationSettings.enabled = true;
        saveNotificationSettings();
    },
    
    // Disable notifications
    disable: function() {
        notificationSettings.enabled = false;
        saveNotificationSettings();
    },
    
    // Set notification frequency
    setFrequency: function(frequency) {
        if (['low', 'medium', 'high'].includes(frequency)) {
            notificationSettings.frequency = frequency;
            saveNotificationSettings();
        }
    },
    
    // Enable/disable specific notification type
    toggleType: function(type, enabled) {
        if (notificationSettings.types.hasOwnProperty(type)) {
            notificationSettings.types[type] = enabled;
            saveNotificationSettings();
        }
    },
    
    // Set do not disturb mode
    setDoNotDisturb: function(enabled, startTime, endTime) {
        notificationSettings.doNotDisturb = enabled;
        if (startTime) notificationSettings.doNotDisturbStart = startTime;
        if (endTime) notificationSettings.doNotDisturbEnd = endTime;
        saveNotificationSettings();
    }
};
