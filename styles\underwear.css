/* Underwear Pages Styles */

/* Hero Section */
.product-hero {
    height: 400px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    margin-bottom: 2rem;
    position: relative;
}

.product-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.6) 100%);
    z-index: 1;
}

.product-hero-content {
    max-width: 800px;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.product-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInDown 1s ease;
}

.product-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease;
}

/* Product Filters */
.product-filters {
    padding: 1.5rem 0;
    background-color: #f8f9fa;
    margin-bottom: 2rem;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

.filter-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.6rem 1.2rem;
    background: none;
    border: 1px solid #dee2e6;
    border-radius: 30px;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.2), transparent);
    transition: all 0.5s ease;
    z-index: -1;
}

.filter-btn:hover {
    background-color: #f8f9fa;
    border-color: #ff6b6b;
    color: #ff6b6b;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active {
    background-color: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
    box-shadow: 0 3px 10px rgba(255, 107, 107, 0.3);
}

.sort-by {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-by label {
    font-size: 0.9rem;
    color: #6c757d;
}

.sort-by select {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #495057;
    background-color: white;
    cursor: pointer;
}

/* Products Section */
.products-section {
    padding: 2rem 0 4rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 107, 107, 0.1), transparent);
    opacity: 0;
    z-index: 1;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.product-card:hover::before {
    opacity: 1;
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: 20px;
    z-index: 2;
}

.new-tag {
    background-color: #51cf66;
    color: white;
}

.bestseller-tag {
    background-color: #ff922b;
    color: white;
}

.sale-tag {
    background-color: #ff6b6b;
    color: white;
    right: 10px;
    left: auto;
}

.product-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    display: flex;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 2;
}

.product-card:hover .product-overlay {
    opacity: 1;
    transform: translateY(0);
}

.product-overlay-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    color: #343a40;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 5px;
    position: relative;
    overflow: hidden;
}

.product-overlay-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #ff6b6b 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.3s ease;
}

.product-overlay-btn:hover {
    background-color: #ff6b6b;
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.product-overlay-btn:hover::before {
    opacity: 0.2;
    transform: scale(2);
}

.product-overlay-btn:active {
    transform: translateY(-2px);
}

.product-info {
    padding: 20px;
}

.product-name {
    font-size: 1rem;
    color: #343a40;
    margin-bottom: 10px;
    font-weight: 500;
    height: 2.4em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.original-price {
    font-size: 0.9rem;
    color: #adb5bd;
    text-decoration: line-through;
}

.current-price {
    font-size: 1.1rem;
    color: #ff6b6b;
    font-weight: 600;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.product-rating i {
    color: #fcc419;
    font-size: 0.9rem;
}

.product-rating span {
    font-size: 0.8rem;
    color: #6c757d;
}

.product-colors {
    display: flex;
    gap: 5px;
}

.color-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
}

.product-sizes {
    display: flex;
    gap: 5px;
}

.product-size {
    font-size: 0.7rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 2px 5px;
    border-radius: 3px;
}

.load-more {
    text-align: center;
    margin-top: 20px;
}

.btn-outline {
    padding: 12px 30px;
    background: none;
    border: 2px solid #ff6b6b;
    color: #ff6b6b;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
}

.btn-outline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: #ff6b6b;
    transition: all 0.3s ease;
    z-index: -1;
    border-radius: 30px;
}

.btn-outline:hover {
    color: white;
}

.btn-outline:hover::before {
    width: 100%;
}

.btn-outline:active {
    transform: scale(0.95);
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background-color: #f8f9fa;
    margin-top: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 10px;
}

.feature-content p {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.6;
}

/* Parent Guide Section */
.parent-guide-section {
    padding: 4rem 0;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.parent-guide-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/pattern-bg.png');
    background-size: 200px;
    opacity: 0.05;
    z-index: 0;
}

.section-title {
    font-size: 2rem;
    color: #343a40;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #ff6b6b;
}

.guide-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.guide-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background-color: #fff;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #ff6b6b;
}

.guide-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.guide-icon {
    width: 60px;
    height: 60px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
}

.guide-item:hover .guide-icon {
    transform: rotate(360deg);
}

.guide-text h3 {
    font-size: 1.3rem;
    color: #343a40;
    margin-bottom: 12px;
    position: relative;
    padding-bottom: 10px;
}

.guide-text h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #ff6b6b;
}

.guide-text p {
    font-size: 1rem;
    color: #6c757d;
    line-height: 1.7;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 992px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .product-hero h1 {
        font-size: 2.5rem;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-group {
        width: 100%;
        overflow-x: auto;
        padding-bottom: 10px;
        justify-content: flex-start;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .product-hero h1 {
        font-size: 2rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .product-card {
        max-width: 300px;
        margin: 0 auto;
    }

    .feature-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .guide-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}
