/* AI Stylist Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --accent-light: rgba(231, 76, 60, 0.1);
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

.ai-stylist-section {
    padding: 80px 0;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.ai-stylist-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/pattern-light.png');
    opacity: 0.05;
    z-index: 0;
}

.ai-container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.ai-header {
    text-align: center;
    margin-bottom: 50px;
}

.ai-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    position: relative;
    display: inline-block;
}

.ai-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
}

.ai-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

.ai-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
}

.ai-preview {
    flex: 1;
    position: relative;
    height: 500px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.ai-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ai-preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-light);
    color: var(--text-light);
}

.ai-preview-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.ai-preview-placeholder p {
    font-size: 1.1rem;
    text-align: center;
    max-width: 80%;
}

.ai-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    display: none;
}

.ai-loading.active {
    display: flex;
}

.ai-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--bg-light);
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.ai-loading-text {
    font-size: 1rem;
    color: var(--text-color);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.ai-controls {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.ai-control-group {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: var(--box-shadow);
}

.ai-control-group h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.ai-control-group h3 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.ai-form-group {
    margin-bottom: 20px;
}

.ai-form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-weight: 500;
}

.ai-form-group select,
.ai-form-group input[type="text"],
.ai-form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    color: var(--text-color);
    background-color: #fff;
    transition: border-color 0.3s ease;
}

.ai-form-group select:focus,
.ai-form-group input[type="text"]:focus,
.ai-form-group textarea:focus {
    border-color: var(--accent-color);
    outline: none;
}

.ai-form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.ai-form-group .hint {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-top: 5px;
}

.ai-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.ai-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.ai-btn i {
    margin-right: 8px;
}

.ai-btn-primary {
    background-color: var(--accent-color);
    color: white;
    position: relative;
    overflow: hidden;
}

.ai-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.ai-btn-primary:hover::before {
    left: 100%;
}

.ai-btn-primary:hover {
    background-color: var(--accent-hover);
}

.ai-btn-secondary {
    background-color: var(--primary-color);
    color: white;
}

.ai-btn-secondary:hover {
    background-color: var(--primary-dark);
}

.ai-btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.ai-btn-outline:hover {
    background-color: var(--hover-color);
}

.ai-results {
    margin-top: 30px;
    display: none;
}

.ai-results.active {
    display: block;
}

.ai-results-title {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-weight: 600;
}

.ai-outfit {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

.ai-outfit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.ai-outfit-title {
    font-size: 1.1rem;
    color: var(--text-color);
    font-weight: 500;
}

.ai-outfit-actions {
    display: flex;
    gap: 10px;
}

.ai-outfit-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.ai-outfit-btn:hover {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.ai-outfit-description {
    font-size: 0.95rem;
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.6;
}

.ai-outfit-items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.ai-outfit-item {
    background-color: var(--bg-light);
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.ai-outfit-item:hover {
    transform: translateY(-5px);
}

.ai-outfit-item-image {
    height: 150px;
    width: 100%;
    object-fit: cover;
}

.ai-outfit-item-info {
    padding: 10px;
}

.ai-outfit-item-name {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 5px;
    font-weight: 500;
}

.ai-outfit-item-price {
    font-size: 0.85rem;
    color: var(--accent-color);
    font-weight: 600;
}

.ai-outfit-item-link {
    display: block;
    text-align: center;
    padding: 8px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    font-size: 0.85rem;
    transition: background-color 0.3s ease;
}

.ai-outfit-item-link:hover {
    background-color: var(--accent-color);
}

/* Responsive */
@media (max-width: 992px) {
    .ai-content {
        flex-direction: column;
    }
    
    .ai-preview {
        width: 100%;
        height: 400px;
    }
    
    .ai-controls {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .ai-header h2 {
        font-size: 2rem;
    }
    
    .ai-preview {
        height: 350px;
    }
    
    .ai-actions {
        flex-direction: column;
    }
    
    .ai-outfit-items {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .ai-outfit-items {
        grid-template-columns: 1fr;
    }
}
