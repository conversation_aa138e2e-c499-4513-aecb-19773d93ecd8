.firstSection{
    /* border: 1px solid red; */
    width: 80%;
    margin: auto;
    margin-bottom: 50px;
   
}

.firstBox{
    font-size:24px;
    font-family: Trade Gothic LH Extended;
    font-weight: bolder;
    margin-bottom: 20px;
    /* margin-top: 10px; */
    position: relative;
}
.firstBox1{
    /* border: 1px solid green; */
    display: grid;
    grid-template-columns: repeat(6,16%);
    justify-content: space-between;
    height: 300px;
    color: black;
    font-family: Theory-web-ul;
}
a{
        text-decoration: none; 
}

.firstBox1>div>img{
    height: 100%;
    width: 100%;
}
.firstBox1>div{
    /* border: 1px solid rgb(255, 7, 234); */
    height: 260px;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
}
/* seconde section */
.secondBox1{
    /* border: 1px solid green; */
    display: grid;
    grid-template-columns: repeat(4,24%);
    justify-content: space-between;
    height: 570px;
    color: black;
    font-family: Theory-web-ul;
}
.secondBox1>div>img{
    height: 100%;
    width: 100%;
}
.secondBox1>div{
    /* border: 1px solid rgb(255, 7, 234); */
    height: 370px;
    text-align: center;
    font-weight: 400;
    cursor: pointer;

}
.secondBox2{
height:200px;
width:100% ;
/* border: 3px solid rgb(51, 153, 56); */
}
.bullets1{
   
/* margin-top: 10px; */
height:30px;
width:60% ;
display: flex;
/* border: 1px solid rebeccapurple; */
gap:10px;

}
.bullets1>div{
 height: 15px; 
 width: 15px; 
 border:transparent ; 

 border-radius: 50%;

}
/* .bullets1>:nth-child(1){
background-color: black;
}
.bullets1>:nth-child(2){
background-color:rgb(103, 163, 187);
}
.bullets1>:nth-child(3){
background-color:rgb(126, 86, 86);
}
.bullets1>:nth-child(4){
background-color:gray;
}
.bullets1>:nth-child(5){
background-color:pink;
} */

.number{
  height:30px; 
 width: 10%; 
 /* border: 1px solid rebeccapurple; */
 /* margin-top: 10px; */
}
.bulletBox1{
margin-top: 10px;
height:50px;
width:100% ;
/* border: 2px solid rgb(153, 88, 51); */
display: flex;
}
.productDetails{
margin-top: 10px;
height:70px;
width:90% ;
/* border: 1px solid rebeccapurple; */
text-align: left;
font-size: 18px;
}
.price{
height:70px;
width:90% ;
/* border: 1px solid rebeccapurple; */
text-align: left;
font-size: 18px;
}
/* third box */
.thirdBox1{
    /* border: 1px solid green; */
    display: grid;
    grid-template-columns: repeat(2,49%);
    justify-content: space-between;
    height: 850px;
    color: black;
    font-family: Theory-web-ul;
}
.thirdBox1>div>img{
    height: 100%;
    width: 100%;
}
.thirdBox1>div{
    /* border: 1px solid rgb(255, 7, 234); */
    height: 700px;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
}

.bulletBox2{
height:30px;
width:100% ;
/* border: 2px solid rgb(153, 88, 51); */
display: flex;
margin-top: 10px;
}
.thirdBox2{
height:140px;
width:60% ;
/* border: 3px solid rgb(51, 153, 56); */
}
.bullets2{
   

height:30px;
width:30% ;
display: flex;
/* border: 1px solid rebeccapurple; */
gap:10px;
margin-left: 5px;

}
.bullets2>div{
 height:15px; 
 width: 15px; 
 border:transparent ; 
 border-radius: 50%;

}
.bullets2>:nth-child(1){
background-color: black;
}
.bullets2>:nth-child(2){
background-color:rgb(103, 163, 187);
}
.bullets2>:nth-child(3){
background-color:rgb(126, 86, 86);
}
.productDetails2{
height:45px;
width:90% ;
/* border: 1px solid rebeccapurple; */
text-align: left;
font-size: 18px;
}
.price2{
height:45px;
width:90% ;
/* border: 1px solid rebeccapurple; */
text-align: left;
font-size: 18px;
}
/* forthbox */
.forthBox1{
    /* border: 1px solid green; */
    display: grid;
    grid-template-columns: repeat(3,33%);
    justify-content: space-between;
    height: 650px;
    font-family: Theory-web-ul;
}
.forthBox1>div>img{
    height: 100%;
    width: 100%;
}
.forthBox1>div{
    /* border: 1px solid rgb(255, 7, 234); */
    height: 480px;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
}

.bulletBox3{
height:30px;
width:100% ;
/* border: 2px solid rgb(153, 88, 51); */
display: flex;
margin-top: 10px;
}
.forthBox2{
height:140px;
width:60% ;
/* border: 3px solid rgb(51, 153, 56); */
}
.bullets3{
height:30px;
width:30% ;
display: flex;
/* border: 1px solid rebeccapurple; */
gap:10px;
margin-left:5px;

}
.bullets3>div{
 height:15px; 
 width: 15px; 
 border:transparent ; 
 border-radius: 50%;

}
.bullets3>:nth-child(1){
background-color: black;
}
.bullets3>:nth-child(2){
background-color:rgb(103, 163, 187);
}
.bullets3>:nth-child(3){
background-color:rgb(126, 86, 86);
}
.bullets3>:nth-child(4){
background-color:rgb(100, 41, 41);
}
.bullets3>:nth-child(5){
background-color:rgb(150, 149, 148);
}

.viewMore{
width: 300px;
height: 30px;
line-height: 30px;
border: 1px solid black;
margin: auto;
padding: 10px 55px;
text-align:center;
font-size:16px ;
text-decoration: none;

}
.viewMore>a{
color: black;
font-family: Theory-web-ul;
text-transform: uppercase;
}
.bullet1>div:nth-child(1){
width: 15px;
height: 15px;
border-radius: 50px;
outline-offset: 2px;
outline: 1px solid black;
}


.firstSectionSlider{
  height: 700px;
  margin: auto;
  width: 100%;
  margin-right: 20%;

}

/* slide css*/
#slider{
width: 80%;
height: 600px;
position:absolute;
left: 10%;
top: 50%;
/* transform: translate(-50%,-50%); */
background-image: url(https://media.routine.vn/prod/media/bat-mi-cac-phong-cach-thoi-trang-2024-nu-2-jzcq.webp);
background-size: 100% 100%;
animation: slider 9s infinite;
}
@keyframes slider {
0%{ background-image: url(https://media.routine.vn/prod/media/bat-mi-cac-phong-cach-thoi-trang-2024-nu-5-tdcc.webp);}
60%{background-image: url( https://storage.googleapis.com/ops-shopee-files-live/live/shopee-blog/2020/06/xu-huong-thoi-trang-he-2020-1-2.jpg );}
100%{background-image: url(https://dony.vn/wp-content/uploads/2024/05/mau-chan-vay-thoi-trang-2.jpg);}

}

.accoriesSection#slider{
    animation: none;
    background-image: url(https://ak-media.theory.com/i/theory/Boots_NEW?$defaultMedia$);
}

.page--header{
    padding: 20px 15px;
    margin-left: 9%;
    padding-top:33px;
    font-size: 32px;
}

.contentWrapper{
    position: absolute;
    top: 50%;
    left: 30%;
    text-align: center;
    font-weight: 100;
    line-height: 25px;
    font-size: 16px;
}
.contentWrapper p{
    font-family: Theory-web-ul;
}
.contentWrapper h3{
    font-size: 27px;
    margin-bottom: 10px;
}
.contentWrapper a{
    font-size: 12px;
    color: black;
    text-transform: uppercase;
    border-bottom: 1px solid black;
    font-family: Theory-web-ul;
    margin-top: 35px;
}