// Admin Charts JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueChartCtx = document.getElementById('revenue-chart');
    
    if (revenueChartCtx) {
        const revenueChart = new Chart(revenueChartCtx, {
            type: 'line',
            data: {
                labels: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
                datasets: [{
                    label: '<PERSON><PERSON>h thu (triệu đồng)',
                    data: [3.2, 4.5, 3.8, 5.2, 6.7, 4.9, 5.5],
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: '#3498db',
                    borderWidth: 2,
                    tension: 0.4,
                    pointBackgroundColor: '#3498db',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 10,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + ' triệu đồng';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6c757d'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            color: '#6c757d',
                            callback: function(value) {
                                return value + 'M';
                            }
                        }
                    }
                }
            }
        });
        
        // Chart period buttons
        const chartPeriodBtns = document.querySelectorAll('.btn-chart-option');
        
        chartPeriodBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Update active button
                chartPeriodBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Update chart data based on period
                const period = this.getAttribute('data-period');
                let labels, data;
                
                switch (period) {
                    case 'day':
                        labels = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
                        data = [3.2, 4.5, 3.8, 5.2, 6.7, 4.9, 5.5];
                        break;
                    case 'week':
                        labels = ['Tuần 1', 'Tuần 2', 'Tuần 3', 'Tuần 4'];
                        data = [18.5, 22.3, 25.8, 30.2];
                        break;
                    case 'month':
                        labels = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'];
                        data = [85, 92, 105, 110, 120, 135, 125, 130, 140, 150, 160, 175];
                        break;
                }
                
                revenueChart.data.labels = labels;
                revenueChart.data.datasets[0].data = data;
                revenueChart.update();
            });
        });
    }
    
    // Products Chart
    const productsChartCtx = document.getElementById('products-chart');
    
    if (productsChartCtx) {
        const productsChart = new Chart(productsChartCtx, {
            type: 'bar',
            data: {
                labels: ['Áo sơ mi nữ', 'Quần jean nam', 'Váy đầm', 'Áo thun nam', 'Áo khoác nữ'],
                datasets: [{
                    label: 'Số lượng bán ra',
                    data: [85, 72, 65, 60, 55],
                    backgroundColor: [
                        '#3498db',
                        '#2ecc71',
                        '#9b59b6',
                        '#f39c12',
                        '#e74c3c'
                    ],
                    borderWidth: 0,
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 10,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return 'Số lượng: ' + context.parsed.y;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6c757d'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            color: '#6c757d'
                        }
                    }
                }
            }
        });
        
        // Chart type select
        const chartSelect = document.querySelector('.chart-select');
        
        if (chartSelect) {
            chartSelect.addEventListener('change', function() {
                const value = this.value;
                
                if (value === 'quantity') {
                    productsChart.data.datasets[0].label = 'Số lượng bán ra';
                    productsChart.data.datasets[0].data = [85, 72, 65, 60, 55];
                    productsChart.options.plugins.tooltip.callbacks.label = function(context) {
                        return 'Số lượng: ' + context.parsed.y;
                    };
                } else if (value === 'revenue') {
                    productsChart.data.datasets[0].label = 'Doanh thu (triệu đồng)';
                    productsChart.data.datasets[0].data = [4.25, 5.04, 3.9, 3.0, 4.95];
                    productsChart.options.plugins.tooltip.callbacks.label = function(context) {
                        return 'Doanh thu: ' + context.parsed.y + ' triệu đồng';
                    };
                }
                
                productsChart.update();
            });
        }
    }
    
    // Date range filter
    const dateRangeSelect = document.getElementById('date-range');
    
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            // Update dashboard data based on selected date range
            const value = this.value;
            
            // This would typically call an API to get data for the selected period
            console.log('Selected date range:', value);
            
            // Show notification
            showNotification('Đã cập nhật dữ liệu cho ' + this.options[this.selectedIndex].text, 'info');
        });
    }
});
