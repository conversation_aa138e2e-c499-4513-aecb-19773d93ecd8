<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/products-new.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <title>Sản phẩm thời trang Nữ | Fashion Store</title>










</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="products-hero" style="background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('./img womens/women 25.webp');">
        <div class="products-hero-content">
            <h1 data-aos="fade-up">Thời Trang Nữ</h1>
            <p data-aos="fade-up" data-aos-delay="200">Khám phá bộ sưu tập thời trang nữ mới nhất của chúng tôi</p>
            <div class="breadcrumb" data-aos="fade-up" data-aos-delay="300">
                <a href="index.html">Trang chủ</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <span class="current">Thời trang nữ</span>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <div class="products-container">
        <!-- Sidebar Filters -->
        <div class="filters-sidebar">
            <div class="filter-section">
                <div class="filter-header active">
                    <h3>Danh mục</h3>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="filter-content active">
                    <ul class="filter-list">
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-all" checked>
                                <label for="category-all">Tất cả</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-dresses">
                                <label for="category-dresses">Váy & Đầm</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-tops">
                                <label for="category-tops">Áo sơ mi & Áo kiểu</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-tshirts">
                                <label for="category-tshirts">Áo thun</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-pants">
                                <label for="category-pants">Quần</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-skirts">
                                <label for="category-skirts">Chân váy</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-outerwear">
                                <label for="category-outerwear">Áo khoác & Blazer</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="category-sweaters">
                                <label for="category-sweaters">Áo len</label>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-header">
                    <h3>Giá</h3>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="filter-content">
                    <div class="price-range">
                        <div class="price-slider">
                            <div class="price-progress"></div>
                        </div>
                        <div class="range-input">
                            <input type="range" min="0" max="5000000" value="0" class="range-min">
                            <input type="range" min="0" max="5000000" value="5000000" class="range-max">
                        </div>
                        <div class="price-input">
                            <div class="field">
                                <span>Min</span>
                                <input type="number" class="input-min" value="0">
                            </div>
                            <div class="field">
                                <span>Max</span>
                                <input type="number" class="input-max" value="5000000">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-header">
                    <h3>Màu sắc</h3>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="filter-content">
                    <div class="color-options">
                        <div class="color-option" style="background-color: #000;" title="Đen"></div>
                        <div class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></div>
                        <div class="color-option" style="background-color: #ff0000;" title="Đỏ"></div>
                        <div class="color-option" style="background-color: #0000ff;" title="Xanh dương"></div>
                        <div class="color-option" style="background-color: #008000;" title="Xanh lá"></div>
                        <div class="color-option" style="background-color: #808080;" title="Xám"></div>
                        <div class="color-option" style="background-color: #f5f5dc;" title="Kem"></div>
                        <div class="color-option" style="background-color: #800080;" title="Tím"></div>
                        <div class="color-option" style="background-color: #ffc0cb;" title="Hồng"></div>
                        <div class="color-option" style="background-color: #d2b48c;" title="Be"></div>
                        <div class="color-option" style="background-color: #ffff00;" title="Vàng"></div>
                        <div class="color-option" style="background-color: #ffa500;" title="Cam"></div>
                    </div>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-header">
                    <h3>Kích thước</h3>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="filter-content">
                    <ul class="filter-list">
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="size-xs">
                                <label for="size-xs">XS</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="size-s">
                                <label for="size-s">S</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="size-m">
                                <label for="size-m">M</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="size-l">
                                <label for="size-l">L</label>
                            </div>
                        </li>
                        <li class="filter-item">
                            <div class="filter-checkbox">
                                <input type="checkbox" id="size-xl">
                                <label for="size-xl">XL</label>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="filter-actions">
                <button class="btn-filter btn-apply">Áp dụng</button>
                <button class="btn-filter btn-reset">Đặt lại</button>
            </div>
        </div>

        <!-- Products Content -->
        <div class="products-content">
            <div class="products-header">
                <div class="products-title">
                    <h2>Thời trang nữ</h2>
                    <p>Hiển thị 1-12 của 48 sản phẩm</p>












                </div>
                <div class="products-sorting">
                    <div class="view-options">
                        <div class="view-option active" data-view="grid"><i class="fas fa-th"></i></div>
                        <div class="view-option" data-view="list"><i class="fas fa-list"></i></div>
                    </div>
                    <div class="sort-dropdown">
                        <div class="sort-select">
                            <span>Sắp xếp theo</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="sort-options">
                            <div class="sort-option" data-sort="featured">Nổi bật</div>
                            <div class="sort-option" data-sort="price-low">Giá: Thấp đến cao</div>
                            <div class="sort-option" data-sort="price-high">Giá: Cao đến thấp</div>
                            <div class="sort-option" data-sort="newest">Mới nhất</div>
                            <div class="sort-option" data-sort="bestselling">Bán chạy nhất</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="products-grid" id="products-container">
                <!-- Products will be loaded dynamically -->
            </div>

            <div class="pagination">
                <ul class="pagination-list">
                    <li class="pagination-item disabled">
                        <a href="#"><i class="fas fa-chevron-left"></i></a>
                    </li>
                    <li class="pagination-item active">
                        <a href="#">1</a>
                    </li>
                    <li class="pagination-item">
                        <a href="#">2</a>
                    </li>
                    <li class="pagination-item">
                        <a href="#">3</a>
                    </li>
                    <li class="pagination-item">
                        <a href="#">4</a>
                    </li>
                    <li class="pagination-item">
                        <a href="#"><i class="fas fa-chevron-right"></i></a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>
    <script src="./script/marquee.js"></script>
    <script type="text/javascript" src="./script/productData-new.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Filter toggle
            const filterHeaders = document.querySelectorAll('.filter-header');
            filterHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const content = this.nextElementSibling;
                    content.classList.toggle('active');
                });
            });

            // Sort dropdown
            const sortSelect = document.querySelector('.sort-select');
            const sortOptions = document.querySelector('.sort-options');

            sortSelect.addEventListener('click', function() {
                this.classList.toggle('active');
                sortOptions.classList.toggle('active');
            });

            document.addEventListener('click', function(e) {
                if (!sortSelect.contains(e.target)) {
                    sortSelect.classList.remove('active');
                    sortOptions.classList.remove('active');
                }
            });

            // Sort options
            const sortOptionElements = document.querySelectorAll('.sort-option');
            sortOptionElements.forEach(option => {
                option.addEventListener('click', function() {
                    const sortType = this.getAttribute('data-sort');
                    const sortText = this.textContent;

                    // Update sort select text
                    sortSelect.querySelector('span').textContent = sortText;

                    // Close dropdown
                    sortSelect.classList.remove('active');
                    sortOptions.classList.remove('active');

                    // Sort products
                    sortProducts(sortType);
                });
            });

            // View options
            const viewOptions = document.querySelectorAll('.view-option');
            const productsGrid = document.querySelector('.products-grid');

            viewOptions.forEach(option => {
                option.addEventListener('click', function() {
                    viewOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');

                    const viewType = this.getAttribute('data-view');
                    if (viewType === 'list') {
                        productsGrid.classList.add('list-view');
                    } else {
                        productsGrid.classList.remove('list-view');
                    }
                });
            });

            // Load products
            loadProducts();

            // Pagination
            const paginationItems = document.querySelectorAll('.pagination-item:not(.disabled)');
            paginationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    paginationItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');

                    // Load products for this page
                    loadProducts();

                    // Scroll to top of products
                    window.scrollTo({
                        top: document.querySelector('.products-container').offsetTop - 100,
                        behavior: 'smooth'
                    });
                });
            });
        });

        // Load products function
        function loadProducts() {
            const productsContainer = document.getElementById('products-container');
            productsContainer.innerHTML = '';

            // Check if women array exists and use it
            if (typeof women !== 'undefined' && Array.isArray(women)) {
                women.forEach(product => {
                    const productCard = createProductCard(product);
                    productsContainer.appendChild(productCard);
                });
            } else {
                // Fallback data if women array is not available
                const fallbackProducts = [
                    {
                        id: 'w1',
                        title: 'Thời trang công sở trẻ trung nhất',
                        price: '350.000đ',
                        image: './img womens/img women 1.webp',
                        category: 'tops',
                        isNew: true,
                        colors: ['#000', '#fff', '#6c757d'],
                        sizes: ['S', 'M', 'L', 'XL'],
                        description: 'Áo sơ mi công sở trẻ trung với thiết kế hiện đại, phù hợp cho môi trường làm việc chuyên nghiệp.'
                    },
                    {
                        id: 'w2',
                        title: 'Bộ đồ thời trang mùa hè',
                        price: '450.000đ',
                        oldPrice: '560.000đ',
                        image: './img womens/img women 2.webp',
                        category: 'dresses',
                        discount: 20,
                        colors: ['#000', '#fff'],
                        sizes: ['S', 'M', 'L'],
                        description: 'Bộ đồ thời trang mùa hè thoáng mát với chất liệu cotton cao cấp, thiết kế hiện đại và trẻ trung.'
                    },
                    {
                        id: 'w3',
                        title: 'Áo sơ mi thời trang phong cách Hồng Kông',
                        price: '380.000đ',
                        image: './img womens/women 3.webp',
                        category: 'tops',
                        colors: ['#000', '#fff', '#f5f5dc'],
                        sizes: ['S', 'M', 'L', 'XL'],
                        description: 'Áo sơ mi thời trang phong cách Hồng Kông với thiết kế hiện đại, chất liệu thoáng mát phù hợp cho mùa hè.'
                    }
                ];

                fallbackProducts.forEach(product => {
                    const productCard = createProductCard(product);
                    productsContainer.appendChild(productCard);
                });
            }
        }

        // Create product card function
        function createProductCard(product) {
            const card = document.createElement('div');
            card.className = 'product-card';
            card.setAttribute('data-aos', 'fade-up');

            let badgeHTML = '';
            if (product.isNew) {
                badgeHTML = '<span class="product-badge new">Mới</span>';
            } else if (product.discount) {
                badgeHTML = `<span class="product-badge sale">-${product.discount}%</span>`;
            }

            let oldPriceHTML = '';
            if (product.oldPrice) {
                oldPriceHTML = `<span class="old-price">${product.oldPrice}</span>`;
            }

            card.innerHTML = `
                <div class="product-image">
                    ${badgeHTML}
                    <img src="${product.image}" alt="${product.title}">
                    <div class="product-actions">
                        <button class="product-action-btn btn-wishlist" title="Thêm vào danh sách yêu thích"><i class="far fa-heart"></i></button>
                        <button class="product-action-btn btn-quickview" title="Xem nhanh"><i class="far fa-eye"></i></button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.title}</h3>
                    <p class="product-description">${product.description || 'Sản phẩm thời trang cao cấp với thiết kế hiện đại và chất liệu thoải mái.'}</p>
                    <div class="product-price">
                        <span class="current-price">${product.price}</span>
                        ${oldPriceHTML}
                    </div>
                    <div class="product-colors">
                        ${product.colors.map(color => `<span class="product-color" style="background-color: ${color};" title="${color}"></span>`).join('')}
                    </div>
                    <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                </div>
            `;

            // Add event listeners
            const addToCartBtn = card.querySelector('.add-to-cart-btn');
            addToCartBtn.addEventListener('click', function() {
                // Check if user is logged in
                const user = JSON.parse(localStorage.getItem('user'));
                if (!user || !user.isLoggedIn) {
                    // Show login prompt
                    showLoginPrompt();
                    return;
                }

                // Add to cart logic
                let cart = JSON.parse(localStorage.getItem('cart')) || [];
                cart.push({
                    name: product.title,
                    price: parseInt(product.price.replace(/\D/g, '')),
                    image: product.image,
                    quantity: 1
                });
                localStorage.setItem('cart', JSON.stringify(cart));

                // Update cart count
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = cart.length;
                }

                // Show notification
                showNotification('Sản phẩm đã được thêm vào giỏ hàng', 'success');
            });

            const wishlistBtn = card.querySelector('.btn-wishlist');
            wishlistBtn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    icon.style.color = '#e74c3c';
                    showNotification('Đã thêm vào danh sách yêu thích', 'success');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    icon.style.color = '';
                    showNotification('Đã xóa khỏi danh sách yêu thích', 'info');
                }
            });

            const quickviewBtn = card.querySelector('.btn-quickview');
            quickviewBtn.addEventListener('click', function() {
                showNotification('Chức năng xem nhanh đang được phát triển', 'info');
            });

            return card;
        }

        // Sort products function
        function sortProducts(sortType) {
            const productsContainer = document.getElementById('products-container');
            const products = Array.from(productsContainer.children);

            products.sort((a, b) => {
                const priceA = parseInt(a.querySelector('.current-price').textContent.replace(/\D/g, ''));
                const priceB = parseInt(b.querySelector('.current-price').textContent.replace(/\D/g, ''));

                if (sortType === 'price-low') {
                    return priceA - priceB;
                } else if (sortType === 'price-high') {
                    return priceB - priceA;
                }

                return 0;
            });

            // Clear container
            productsContainer.innerHTML = '';

            // Append sorted products
            products.forEach(product => {
                productsContainer.appendChild(product);
            });

            // Show notification
            showNotification(`Đã sắp xếp sản phẩm theo ${sortType === 'price-low' ? 'giá tăng dần' : 'giá giảm dần'}`, 'success');
        }

        /**
         * Show login prompt popup
         */
        function showLoginPrompt() {
            // Create login prompt container
            const loginPrompt = document.createElement('div');
            loginPrompt.className = 'login-prompt-overlay';
            loginPrompt.style.position = 'fixed';
            loginPrompt.style.top = '0';
            loginPrompt.style.left = '0';
            loginPrompt.style.width = '100%';
            loginPrompt.style.height = '100%';
            loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loginPrompt.style.display = 'flex';
            loginPrompt.style.alignItems = 'center';
            loginPrompt.style.justifyContent = 'center';
            loginPrompt.style.zIndex = '9999';
            loginPrompt.style.opacity = '0';
            loginPrompt.style.transition = 'opacity 0.3s ease';

            loginPrompt.innerHTML = `
                <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                    <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                        <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                        <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="login-prompt-body" style="padding: 20px;">
                        <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                        <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                            <a href="login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                            <a href="register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                        </div>
                    </div>
                </div>
            `;

            // Add to body
            document.body.appendChild(loginPrompt);

            // Show with animation
            setTimeout(() => {
                loginPrompt.style.opacity = '1';
            }, 10);

            // Close button event
            const closeButton = loginPrompt.querySelector('.login-prompt-close');
            closeButton.addEventListener('click', function() {
                loginPrompt.style.opacity = '0';
                setTimeout(() => {
                    loginPrompt.remove();
                }, 300);
            });

            // Save current URL to redirect back after login
            localStorage.setItem('redirectAfterLogin', window.location.href);
        }




















        document.addEventListener("DOMContentLoaded", function () {
  const searchInput = document.getElementById("search-input");
  const productsContainer = document.getElementById("products-container");

  if (!searchInput || !productsContainer) return;

  if (!window.products && typeof women !== "undefined") {
    window.products = women; // 👉 Thay 'women' bằng biến tương ứng của từng file
  }

  searchInput.addEventListener("input", function () {
    const keyword = this.value.toLowerCase().trim();
    const filtered = window.products.filter(product =>
      product.title.toLowerCase().includes(keyword)
    );
    renderProducts(filtered);
  });

  function renderProducts(list) {
    productsContainer.innerHTML = "";
    list.forEach(p => {
      const card = createProductCard(p);
      productsContainer.appendChild(card);
    });
  }
});





    </script>
</body>
</html>
