// Dark Mode Feature

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dark mode
    initDarkMode();
    
    // Add dark mode toggle button
    addDarkModeToggle();
});

// Initialize dark mode
function initDarkMode() {
    // Check if dark mode is enabled in localStorage
    const isDarkMode = localStorage.getItem('darkMode') === 'enabled';
    
    // Check if user prefers dark mode
    const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // Apply dark mode if enabled or preferred
    if (isDarkMode || (prefersDarkMode && localStorage.getItem('darkMode') === null)) {
        enableDarkMode();
    } else {
        disableDarkMode();
    }
    
    // Add dark mode styles
    addDarkModeStyles();
}

// Add dark mode toggle button
function addDarkModeToggle() {
    // Check if button already exists
    if (document.getElementById('dark-mode-toggle')) return;
    
    // Create toggle button
    const toggleButton = document.createElement('button');
    toggleButton.id = 'dark-mode-toggle';
    toggleButton.className = 'dark-mode-toggle';
    toggleButton.title = 'Chuyển đổi chế độ tối';
    
    // Set icon based on current mode
    const isDarkMode = document.body.classList.contains('dark-mode');
    toggleButton.innerHTML = isDarkMode ? 
        '<i class="fas fa-sun"></i>' : 
        '<i class="fas fa-moon"></i>';
    
    // Add click event
    toggleButton.addEventListener('click', toggleDarkMode);
    
    // Add to body
    document.body.appendChild(toggleButton);
}

// Toggle dark mode
function toggleDarkMode() {
    // Check current state
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    if (isDarkMode) {
        disableDarkMode();
    } else {
        enableDarkMode();
    }
}

// Enable dark mode
function enableDarkMode() {
    // Add class to body
    document.body.classList.add('dark-mode');
    
    // Update toggle button icon
    const toggleButton = document.getElementById('dark-mode-toggle');
    if (toggleButton) {
        toggleButton.innerHTML = '<i class="fas fa-sun"></i>';
    }
    
    // Save preference to localStorage
    localStorage.setItem('darkMode', 'enabled');
    
    // Show notification
    showNotification('Đã bật chế độ tối', 'info');
}

// Disable dark mode
function disableDarkMode() {
    // Remove class from body
    document.body.classList.remove('dark-mode');
    
    // Update toggle button icon
    const toggleButton = document.getElementById('dark-mode-toggle');
    if (toggleButton) {
        toggleButton.innerHTML = '<i class="fas fa-moon"></i>';
    }
    
    // Save preference to localStorage
    localStorage.setItem('darkMode', 'disabled');
    
    // Show notification
    showNotification('Đã tắt chế độ tối', 'info');
}

// Add dark mode styles
function addDarkModeStyles() {
    // Check if styles already exist
    if (document.getElementById('dark-mode-styles')) return;
    
    // Create style element
    const style = document.createElement('style');
    style.id = 'dark-mode-styles';
    
    // Add CSS
    style.innerHTML = `
        /* Dark Mode Toggle Button */
        .dark-mode-toggle {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #fff;
            color: #343a40;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            z-index: 999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .dark-mode-toggle:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        body.dark-mode .dark-mode-toggle {
            background-color: #343a40;
            color: #f8f9fa;
        }
        
        /* Dark Mode Styles */
        body.dark-mode {
            background-color: #121212;
            color: #f8f9fa;
        }
        
        /* Header */
        body.dark-mode .announcement-bar {
            background-color: #343a40;
            color: #f8f9fa;
        }
        
        body.dark-mode .main-nav {
            background-color: #1e1e1e;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        body.dark-mode .nav-menu > li > a {
            color: #f8f9fa;
        }
        
        body.dark-mode .dropdown-menu {
            background-color: #2d2d2d;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        body.dark-mode .dropdown-column h3 {
            color: #f8f9fa;
        }
        
        body.dark-mode .dropdown-column ul li a {
            color: #adb5bd;
        }
        
        body.dark-mode .dropdown-column ul li a:hover {
            color: #ff6b6b;
        }
        
        body.dark-mode .search-box input {
            background-color: #2d2d2d;
            color: #f8f9fa;
            border-color: #495057;
        }
        
        body.dark-mode .search-box button {
            background-color: #495057;
            color: #f8f9fa;
        }
        
        body.dark-mode .nav-icon {
            color: #f8f9fa;
        }
        
        /* Sections */
        body.dark-mode .section-title {
            color: #f8f9fa;
        }
        
        body.dark-mode .hero-content h1,
        body.dark-mode .hero-content p {
            color: #f8f9fa;
        }
        
        body.dark-mode .btn-shop {
            background-color: #ff6b6b;
            color: #f8f9fa;
        }
        
        body.dark-mode .category-card {
            background-color: #2d2d2d;
        }
        
        body.dark-mode .category-content h3 {
            color: #f8f9fa;
        }
        
        /* Products */
        body.dark-mode .product-card {
            background-color: #2d2d2d;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        body.dark-mode .product-info {
            background-color: #2d2d2d;
        }
        
        body.dark-mode .product-name {
            color: #f8f9fa;
        }
        
        body.dark-mode .product-price {
            color: #ff6b6b;
        }
        
        body.dark-mode .product-tag {
            background-color: #495057;
            color: #f8f9fa;
        }
        
        /* Footer */
        body.dark-mode .footer {
            background-color: #1e1e1e;
            color: #f8f9fa;
        }
        
        body.dark-mode .footer-column h3 {
            color: #f8f9fa;
        }
        
        body.dark-mode .footer-column ul li a {
            color: #adb5bd;
        }
        
        body.dark-mode .footer-column ul li a:hover {
            color: #ff6b6b;
        }
        
        body.dark-mode .contact-info p {
            color: #adb5bd;
        }
        
        body.dark-mode .social-icon {
            background-color: #495057;
            color: #f8f9fa;
        }
        
        body.dark-mode .footer-bottom {
            border-top-color: #495057;
        }
        
        /* Special Features */
        body.dark-mode .feature-card {
            background-color: #2d2d2d;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        body.dark-mode .feature-content h3 {
            color: #f8f9fa;
        }
        
        body.dark-mode .feature-content p {
            color: #adb5bd;
        }
        
        /* Personalized Recommendations */
        body.dark-mode .personalized-recommendations {
            background-color: #1e1e1e;
        }
        
        body.dark-mode .recommendation-info {
            color: #adb5bd;
        }
        
        body.dark-mode .recommendation-actions button {
            background-color: #343a40;
            border-color: #495057;
            color: #f8f9fa;
        }
        
        /* Trending Section */
        body.dark-mode .trending-item {
            background-color: #2d2d2d;
        }
        
        body.dark-mode .trending-content h3 {
            color: #f8f9fa;
        }
        
        body.dark-mode .trending-content p {
            color: #adb5bd;
        }
        
        /* Marquee */
        body.dark-mode .marquee-container {
            background-color: #343a40;
        }
        
        body.dark-mode .marquee-item {
            color: #f8f9fa;
        }
        
        /* Notifications */
        body.dark-mode .notification {
            background-color: #2d2d2d;
            color: #f8f9fa;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        body.dark-mode .notification-message {
            color: #f8f9fa;
        }
        
        /* Chatbot */
        body.dark-mode #chat-bot {
            background-color: #2d2d2d;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        body.dark-mode .chat-header {
            background-color: #343a40;
            color: #f8f9fa;
        }
        
        body.dark-mode #chat-input {
            background-color: #343a40;
            color: #f8f9fa;
            border-color: #495057;
        }
        
        body.dark-mode #chat-toggle {
            background-color: #ff6b6b;
            color: #f8f9fa;
        }
    `;
    
    // Add to head
    document.head.appendChild(style);
}

// Listen for system preference changes
function listenForPreferenceChanges() {
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        // Add change listener
        mediaQuery.addEventListener('change', (e) => {
            // Only apply if user hasn't set a preference
            if (localStorage.getItem('darkMode') === null) {
                if (e.matches) {
                    enableDarkMode();
                } else {
                    disableDarkMode();
                }
            }
        });
    }
}

// Call to listen for preference changes
listenForPreferenceChanges();
