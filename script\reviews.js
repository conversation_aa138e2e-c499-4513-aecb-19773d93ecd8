/**
 * Product Reviews System
 * This script manages product reviews, ratings, and comments
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const reviewsSection = document.querySelector('.reviews-section');
    const writeReviewBtn = document.querySelector('.write-review-btn');
    const reviewForm = document.querySelector('.review-form');
    const reviewsList = document.querySelector('.reviews-list');
    const ratingInputs = document.querySelectorAll('.rating-input i');
    const reviewFormElement = document.getElementById('review-form');
    const cancelReviewBtn = document.querySelector('.btn-cancel');
    
    // Check if elements exist
    if (!reviewsSection) return;
    
    // Sample reviews data (in a real app, this would come from a database)
    let reviews = JSON.parse(localStorage.getItem('productReviews')) || [
        {
            id: 1,
            productId: 1,
            userId: 101,
            userName: "Nguyễn <PERSON>",
            rating: 5,
            title: "<PERSON>ản phẩm tuyệt vời",
            content: "Chất lượng sản phẩm rất tốt, đúng như mô tả. Giao hàng nhanh, đóng gói cẩn thận. Tôi rất hài lòng và sẽ mua lại.",
            date: "2023-10-15",
            photos: ["./img/review1.jpg", "./img/review2.jpg"],
            likes: 12,
            dislikes: 2,
            replies: [
                {
                    userId: 999,
                    userName: "Shop Fashion",
                    content: "Cảm ơn bạn đã đánh giá tích cực về sản phẩm của chúng tôi!",
                    date: "2023-10-16"
                }
            ]
        },
        {
            id: 2,
            productId: 1,
            userId: 102,
            userName: "Trần Thị B",
            rating: 4,
            title: "Sản phẩm khá tốt",
            content: "Sản phẩm đẹp, đúng kích thước, chất liệu tốt. Chỉ tiếc là màu sắc hơi khác so với hình ảnh một chút.",
            date: "2023-10-10",
            photos: [],
            likes: 5,
            dislikes: 1,
            replies: []
        },
        {
            id: 3,
            productId: 1,
            userId: 103,
            userName: "Lê Văn C",
            rating: 3,
            title: "Sản phẩm bình thường",
            content: "Sản phẩm tạm được, không quá xuất sắc nhưng cũng không tệ. Giao hàng hơi chậm.",
            date: "2023-09-28",
            photos: ["./img/review3.jpg"],
            likes: 3,
            dislikes: 4,
            replies: []
        }
    ];
    
    // Initialize reviews
    function initReviews() {
        if (!reviewsList) return;
        
        // Calculate average rating
        updateReviewsSummary();
        
        // Render reviews
        renderReviews();
    }
    
    // Update reviews summary
    function updateReviewsSummary() {
        if (!reviewsSection) return;
        
        // Calculate average rating
        let totalRating = 0;
        reviews.forEach(review => {
            totalRating += review.rating;
        });
        const averageRating = reviews.length > 0 ? (totalRating / reviews.length).toFixed(1) : 0;
        
        // Calculate rating breakdown
        const ratingCounts = [0, 0, 0, 0, 0]; // 1-5 stars
        reviews.forEach(review => {
            ratingCounts[review.rating - 1]++;
        });
        
        // Update average rating display
        const averageRatingElement = document.querySelector('.average-rating');
        if (averageRatingElement) {
            averageRatingElement.textContent = averageRating;
        }
        
        // Update rating stars
        const ratingStarsElement = document.querySelector('.reviews-average .rating-stars');
        if (ratingStarsElement) {
            ratingStarsElement.innerHTML = generateStarRating(averageRating);
        }
        
        // Update rating count
        const ratingCountElement = document.querySelector('.rating-count');
        if (ratingCountElement) {
            ratingCountElement.textContent = `${reviews.length} đánh giá`;
        }
        
        // Update rating breakdown
        const ratingProgressElements = document.querySelectorAll('.rating-progress-fill');
        if (ratingProgressElements.length === 5) {
            for (let i = 0; i < 5; i++) {
                const percent = reviews.length > 0 ? (ratingCounts[4 - i] / reviews.length) * 100 : 0;
                ratingProgressElements[i].style.width = `${percent}%`;
                
                // Update percent text
                const percentElement = ratingProgressElements[i].parentElement.nextElementSibling;
                if (percentElement) {
                    percentElement.textContent = `${Math.round(percent)}%`;
                }
            }
        }
    }
    
    // Render reviews
    function renderReviews() {
        if (!reviewsList) return;
        
        // Sort reviews by date (newest first)
        reviews.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        // Generate HTML for reviews
        let reviewsHTML = '';
        reviews.forEach(review => {
            reviewsHTML += `
                <div class="review-item" data-review-id="${review.id}">
                    <div class="review-header">
                        <div class="reviewer-info">
                            <div class="reviewer-avatar">${review.userName.charAt(0)}</div>
                            <div>
                                <div class="reviewer-name">${review.userName}</div>
                                <div class="review-date">${formatDate(review.date)}</div>
                            </div>
                        </div>
                        <div class="review-rating">
                            ${generateStarRating(review.rating)}
                        </div>
                    </div>
                    <h4 class="review-title">${review.title}</h4>
                    <div class="review-content">${review.content}</div>
                    ${review.photos.length > 0 ? `
                        <div class="review-photos">
                            ${review.photos.map(photo => `
                                <img src="${photo}" alt="Review Photo" class="review-photo">
                            `).join('')}
                        </div>
                    ` : ''}
                    <div class="review-actions">
                        <div class="review-action review-like" data-review-id="${review.id}">
                            <i class="far fa-thumbs-up"></i>
                            <span>${review.likes}</span>
                        </div>
                        <div class="review-action review-dislike" data-review-id="${review.id}">
                            <i class="far fa-thumbs-down"></i>
                            <span>${review.dislikes}</span>
                        </div>
                        <div class="review-action review-reply" data-review-id="${review.id}">
                            <i class="far fa-comment"></i>
                            <span>Trả lời</span>
                        </div>
                    </div>
                    ${review.replies.length > 0 ? `
                        <div class="review-replies">
                            ${review.replies.map(reply => `
                                <div class="review-reply-item">
                                    <div class="reviewer-info">
                                        <div class="reviewer-avatar shop">${reply.userName.charAt(0)}</div>
                                        <div>
                                            <div class="reviewer-name">${reply.userName}</div>
                                            <div class="review-date">${formatDate(reply.date)}</div>
                                        </div>
                                    </div>
                                    <div class="reply-content">${reply.content}</div>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        });
        
        // Update reviews list
        reviewsList.innerHTML = reviewsHTML;
        
        // Add event listeners to review actions
        addReviewActionListeners();
    }
    
    // Generate star rating HTML
    function generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
        
        let starsHTML = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        // Half star
        if (halfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }
        
        return starsHTML;
    }
    
    // Format date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', { year: 'numeric', month: 'long', day: 'numeric' });
    }
    
    // Add event listeners to review actions
    function addReviewActionListeners() {
        // Like buttons
        document.querySelectorAll('.review-like').forEach(btn => {
            btn.addEventListener('click', function() {
                const reviewId = parseInt(this.getAttribute('data-review-id'));
                const review = reviews.find(r => r.id === reviewId);
                if (review) {
                    review.likes++;
                    this.querySelector('span').textContent = review.likes;
                    saveReviews();
                }
            });
        });
        
        // Dislike buttons
        document.querySelectorAll('.review-dislike').forEach(btn => {
            btn.addEventListener('click', function() {
                const reviewId = parseInt(this.getAttribute('data-review-id'));
                const review = reviews.find(r => r.id === reviewId);
                if (review) {
                    review.dislikes++;
                    this.querySelector('span').textContent = review.dislikes;
                    saveReviews();
                }
            });
        });
        
        // Reply buttons
        document.querySelectorAll('.review-reply').forEach(btn => {
            btn.addEventListener('click', function() {
                const reviewId = parseInt(this.getAttribute('data-review-id'));
                alert('Chức năng trả lời đánh giá sẽ sớm được cập nhật!');
            });
        });
        
        // Review photos
        document.querySelectorAll('.review-photo').forEach(photo => {
            photo.addEventListener('click', function() {
                // Open photo in lightbox or modal
                alert('Chức năng xem ảnh phóng to sẽ sớm được cập nhật!');
            });
        });
    }
    
    // Save reviews to localStorage
    function saveReviews() {
        localStorage.setItem('productReviews', JSON.stringify(reviews));
    }
    
    // Event listeners
    if (writeReviewBtn && reviewForm) {
        writeReviewBtn.addEventListener('click', function() {
            reviewForm.classList.add('active');
            writeReviewBtn.style.display = 'none';
            
            // Scroll to review form
            reviewForm.scrollIntoView({ behavior: 'smooth' });
        });
    }
    
    if (cancelReviewBtn && reviewForm && writeReviewBtn) {
        cancelReviewBtn.addEventListener('click', function() {
            reviewForm.classList.remove('active');
            writeReviewBtn.style.display = 'flex';
            
            // Reset form
            if (reviewFormElement) {
                reviewFormElement.reset();
                
                // Reset rating stars
                ratingInputs.forEach(star => {
                    star.classList.remove('active');
                });
            }
        });
    }
    
    // Rating input stars
    if (ratingInputs.length > 0) {
        ratingInputs.forEach((star, index) => {
            star.addEventListener('click', function() {
                // Update visual state
                ratingInputs.forEach((s, i) => {
                    if (i <= index) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
                
                // Update hidden input
                const ratingInput = document.getElementById('rating');
                if (ratingInput) {
                    ratingInput.value = index + 1;
                }
            });
            
            star.addEventListener('mouseover', function() {
                // Hover effect
                ratingInputs.forEach((s, i) => {
                    if (i <= index) {
                        s.classList.add('hover');
                    } else {
                        s.classList.remove('hover');
                    }
                });
            });
            
            star.addEventListener('mouseout', function() {
                // Remove hover effect
                ratingInputs.forEach(s => {
                    s.classList.remove('hover');
                });
            });
        });
    }
    
    // Review form submission
    if (reviewFormElement) {
        reviewFormElement.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const rating = parseInt(document.getElementById('rating').value);
            const title = document.getElementById('review-title').value;
            const content = document.getElementById('review-content').value;
            
            // Validate form
            if (!rating || !title || !content) {
                alert('Vui lòng điền đầy đủ thông tin đánh giá!');
                return;
            }
            
            // Create new review
            const newReview = {
                id: reviews.length > 0 ? Math.max(...reviews.map(r => r.id)) + 1 : 1,
                productId: 1, // Replace with actual product ID
                userId: 999, // Replace with actual user ID
                userName: "Khách hàng", // Replace with actual user name
                rating: rating,
                title: title,
                content: content,
                date: new Date().toISOString().split('T')[0],
                photos: [],
                likes: 0,
                dislikes: 0,
                replies: []
            };
            
            // Add review to array
            reviews.push(newReview);
            
            // Save reviews
            saveReviews();
            
            // Update UI
            updateReviewsSummary();
            renderReviews();
            
            // Reset form
            reviewFormElement.reset();
            ratingInputs.forEach(star => {
                star.classList.remove('active');
            });
            
            // Hide form
            reviewForm.classList.remove('active');
            writeReviewBtn.style.display = 'flex';
            
            // Show success message
            alert('Cảm ơn bạn đã đánh giá sản phẩm!');
        });
    }
    
    // Initialize reviews
    initReviews();
});
