// Simple Cart Handler

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart handler loaded');

    // Initialize cart
    initCart();

    // Add event listeners to all add to cart buttons
    setupAddToCartButtons();
});

// Initialize cart
function initCart() {
    // Make sure cart exists in localStorage
    if (!localStorage.getItem('cart')) {
        localStorage.setItem('cart', JSON.stringify([]));
    }

    // Update cart count
    updateCartCount();
}

// Setup add to cart buttons
function setupAddToCartButtons() {
    // Get all add to cart buttons
    const addToCartButtons = document.querySelectorAll('.btn-add-to-cart');
    console.log('Found add to cart buttons:', addToCartButtons.length);

    // Add click event listener to each button
    addToCartButtons.forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });
}

// Handle add to cart button click
function handleAddToCart(event) {
    event.preventDefault();
    console.log('Add to cart button clicked');

    // Get product information
    const button = event.target.closest('.btn-add-to-cart');
    const productCard = button.closest('.product-img').parentElement;

    if (!productCard) {
        console.error('Product card not found');
        return;
    }

    // Get product details
    const productImg = productCard.querySelector('.product-img img');
    const productInfo = productCard.querySelector('.products-name');

    if (!productImg || !productInfo) {
        console.error('Product image or info not found');
        return;
    }

    const productName = productInfo.querySelector('p:nth-child(2)');
    const productPrice = productInfo.querySelector('p:nth-child(3)');

    if (!productName || !productPrice) {
        console.error('Product name or price not found');
        return;
    }

    // Create product object
    const product = {
        id: generateProductId(),
        name: productName.textContent.trim(),
        title: productName.textContent.trim(),
        price: productPrice.textContent.trim(),
        image: productImg.src,
        image1: productImg.src,
        quantity: 1
    };

    console.log('Adding product to cart:', product);

    // Mở modal chọn size và màu sắc thay vì thêm trực tiếp vào giỏ hàng
    if (typeof openProductModal === 'function') {
        openProductModal(product);
    } else {
        // Nếu không có modal, thêm trực tiếp vào giỏ hàng với giá trị mặc định
        product.color = 'Đen'; // Default color
        product.size = 'M';    // Default size

        // Add product to cart
        addToCart(product);
    }
}

// Add product to cart
function addToCart(product) {
    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Kiểm tra xem sản phẩm đã tồn tại trong giỏ hàng chưa
    const existingProductIndex = cart.findIndex(item => item.name === product.name);

    if (existingProductIndex !== -1) {
        // Nếu sản phẩm đã tồn tại, hiển thị thông báo
        if (typeof showNotification === 'function') {
            showNotification("Sản phẩm đã có trong giỏ hàng", "info");
        } else {
            alert("Sản phẩm đã có trong giỏ hàng");
        }
        return;
    }

    // Tạo ID duy nhất cho sản phẩm mới
    const uniqueId = Date.now() + Math.random().toString(36).substring(2, 7);

    // Đảm bảo mỗi sản phẩm có ID duy nhất và thuộc tính image
    product.id = uniqueId;
    product.addedAt = new Date().toISOString();

    // Đảm bảo thuộc tính image được đặt đúng
    if (product.image1 && !product.image) {
        product.image = product.image1;
    }

    // Add product to cart
    cart.push(product);

    // Save cart to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart count
    updateCartCount();

    // Hiển thị thông báo thành công
    if (typeof showNotification === 'function') {
        showNotification("Đã thêm vào giỏ hàng", "success");
    } else {
        alert("Đã thêm vào giỏ hàng");
    }
}

// Update cart count
function updateCartCount() {
    const cartCountElement = document.querySelector('.cart-count');
    if (!cartCountElement) {
        console.error('Cart count element not found');
        return;
    }

    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Update cart count
    cartCountElement.textContent = cart.length;
}

// Generate random product ID
function generateProductId() {
    return 'product_' + Math.random().toString(36).substring(2, 11);
}

// Show notification
function showNotification(message, type = 'info') {
    console.log('Showing notification:', message, type);

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification ' + type;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                       type === 'error' ? '#F44336' :
                                       type === 'warning' ? '#FF9800' : '#2196F3';
    notification.style.color = '#fff';
    notification.style.padding = '15px 20px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    notification.style.zIndex = '9999';
    notification.style.opacity = '0';
    notification.style.transition = 'opacity 0.3s';

    // Add icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
    }

    // Set notification content
    notification.innerHTML = icon + message;

    // Add notification to body
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);

    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';

        // Remove notification after transition
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
