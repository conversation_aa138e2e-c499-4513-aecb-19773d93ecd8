// Underwear Women JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sample product data
    const products = [
        {
            id: 'w1',
            name: 'Silk Phoenix 👙 Hàng tồn kho Áo Lót Nâng Ngực Không Gọng plus size',
            price: 299000,
            originalPrice: 399000,
            image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lmrbqpv8e4tgb8.webp',
            category: 'bra',
            colors: ['Đen', 'Trắng', 'màu xám'],
            sizes: ['32A', '32B', '34A', '34B', '36A', '36B'],
            rating: 4.8,
            reviews: 124,
            isNew: true,
            isBestSeller: true
        },
        {
            id: 'w2',
            name: '<PERSON><PERSON> ngực nữ cotton không gọng nâng ngực, áo bra nữ mềm mại thoáng mát tôn vòngÁo ngực nữ cotton không gọng nâng ngực, áo bra nữ mềm mại thoáng mát tôn vòng',
            price: 349000,
            originalPrice: 449000,
            image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lwxpnt7t81mt4e.webp',
            category: 'bra',
            colors: ['Đen', 'Be', 'Xanh navy'],
            sizes: ['32B', '34B', '34C', '36B', '36C'],
            rating: 4.6,
            reviews: 98,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'w3',
            name: 'Quần Lót Tam Giác Cạp Thấp Nửa Trong Suốt Gợi Cảm Ren Lông Mi Satin Tinh Khiết Quần Lót Nữ Mỏng Thoáng Khí Kháng Khuẩn',
            price: 99000,
            originalPrice: 129000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rcdp-m6jq3m89xvy398.webp',
            category: 'panty',
            colors: ['Đen', 'Trắng', 'Xám', 'Hồng'],
            sizes: ['S', 'M', 'L', 'XL'],
            rating: 4.9,
            reviews: 215,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'w4',
            name: 'Hoa Tình Yêu Mãnh Liệt Như Lửa" Quần Lót Ren Nữ Thoáng Khí Tươi Trẻ Gợi Cảm Nữ Tính',
            price: 129000,
            originalPrice: 159000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rdye-lxi632aexlyefd.webp',
            category: 'panty',
            colors: ['Đen', 'Trắng', 'hồng'],
            sizes: ['S', 'M', 'L'],
            rating: 4.7,
            reviews: 87,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'w5',
            name: 'Bộ Đồ Lót Cô Gái Ngọt Ngào Mỏng Loli Dễ Thương Áo Ngực Sinh Viên Mềm Mại Không Vòng Thép Thỏ Tai Bộ Áo Ngực',
            price: 499000,
            originalPrice: 699000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134202-7rd61-lv1au9pcoeyx2f.webp',
            category: 'set',
            colors: ['hồng', 'trắng'],
            sizes: ['S', 'M', 'L'],
            rating: 4.9,
            reviews: 56,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'w6',
            name: 'Bộ Ngủ Sexy - Đồ Ngủ Sexy Voan Phối Ren Form 40-55kg( ảnh và video thật)',
            price: 899000,
            originalPrice: 1199000,
            image: 'https://down-vn.img.susercontent.com/file/bde0b7bde8116274581adb68525aa718@resize_w450_nl.webp',
            category: 'sleepwear',
            colors: ['Đen', 'Hồng phấn', 'đỏ'],
            sizes: ['S', 'M', 'L', 'XL'],
            rating: 4.8,
            reviews: 42,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'w7',
            name: 'Quần nịt bụng đúc su lụa nguyên khối co giãn định hình vùng bụng dưới ôm sát',
            price: 599000,
            originalPrice: 799000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-licr3x4ybljm77@resize_w450_nl.webp',
            category: 'shapewear',
            colors: ['Đen', 'Be'],
            sizes: ['S', 'M', 'L', 'XL', 'XXL'],
            rating: 4.5,
            reviews: 78,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'w8',
            name: 'Gợi Cảm Ngọt Ngào Phong Cách Thỏ Quần Lót Phối Cảnh Cám Dỗ Ren Ngực Nhìn Lớn Tập Hợp Áo Ngực Siêu Mỏng Mềm Mại Thoải Má',
            price: 399000,
            originalPrice: 499000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rblj-lqk0isocyvja53@resize_w450_nl.webp',
            category: 'bra',
            colors: ['Đen', 'Xám', 'Xanh dương'],
            sizes: ['S', 'M', 'L', 'XL'],
            rating: 4.7,
            reviews: 103,
            isNew: false,
            isBestSeller: true
        }
    ];

    // Function to render products
    function renderProducts(productsToRender) {
        const productsContainer = document.getElementById('products-container');
        if (!productsContainer) return;

        productsContainer.innerHTML = '';

        productsToRender.forEach(product => {
            const discount = Math.round((1 - product.price / product.originalPrice) * 100);

            const productCard = document.createElement('div');
            productCard.className = 'product-card';
            productCard.setAttribute('data-category', product.category);

            productCard.innerHTML = `
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}">
                    ${product.isNew ? '<span class="product-tag new-tag">Mới</span>' : ''}
                    ${product.isBestSeller ? '<span class="product-tag bestseller-tag">Bán chạy</span>' : ''}
                    ${discount > 0 ? `<span class="product-tag sale-tag">-${discount}%</span>` : ''}
                    <div class="product-overlay">
                        <button class="product-overlay-btn quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="product-overlay-btn add-to-wishlist-btn" data-product-id="${product.id}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="product-overlay-btn add-to-cart-btn" data-product-id="${product.id}">
                            <i class="fas fa-shopping-bag"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <div class="product-price">
                        ${discount > 0 ? `<span class="original-price">${product.originalPrice.toLocaleString()}đ</span>` : ''}
                        <span class="current-price">${product.price.toLocaleString()}đ</span>
                    </div>
                    <div class="product-meta">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <span>${product.rating} (${product.reviews})</span>
                        </div>
                        <div class="product-colors">
                            ${product.colors.slice(0, 3).map(color => `<span class="color-dot" style="background-color: ${getColorCode(color)}" title="${color}"></span>`).join('')}
                        </div>
                    </div>
                </div>
            `;

            productsContainer.appendChild(productCard);
        });

        // Add event listeners to buttons
        document.querySelectorAll('.quick-view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                quickViewProduct(productId);
            });
        });

        document.querySelectorAll('.add-to-wishlist-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToWishlist(productId);
            });
        });

        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToCart(productId);
            });
        });
    }

    // Helper function to get color code from color name
    function getColorCode(colorName) {
        const colorMap = {
            'Đen': '#000000',
            'Trắng': '#FFFFFF',
            'Hồng nhạt': '#FFB6C1',
            'Hồng phấn': '#FFC0CB',
            'Be': '#F5F5DC',
            'Xanh navy': '#000080',
            'Xám': '#808080',
            'Hồng': '#FF69B4',
            'Đỏ đô': '#8B0000',
            'Đỏ': '#FF0000',
            'Xanh ngọc': '#40E0D0',
            'Xanh biển': '#0000FF',
            'Xanh dương': '#1E90FF'
        };

        return colorMap[colorName] || '#CCCCCC';
    }

    // Function to filter products
    function filterProducts() {
        const activeFilter = document.querySelector('.filter-btn.active');
        if (!activeFilter) return products;

        const filterValue = activeFilter.getAttribute('data-filter');
        if (filterValue === 'all') return products;

        return products.filter(product => product.category === filterValue);
    }

    // Function to sort products
    function sortProducts(productsToSort) {
        const sortSelect = document.getElementById('sort-select');
        if (!sortSelect) return productsToSort;

        const sortValue = sortSelect.value;

        switch (sortValue) {
            case 'price-asc':
                return [...productsToSort].sort((a, b) => a.price - b.price);
            case 'price-desc':
                return [...productsToSort].sort((a, b) => b.price - a.price);
            case 'newest':
                return [...productsToSort].sort((a, b) => b.isNew - a.isNew);
            case 'discount':
                return [...productsToSort].sort((a, b) => {
                    const discountA = (a.originalPrice - a.price) / a.originalPrice;
                    const discountB = (b.originalPrice - b.price) / b.originalPrice;
                    return discountB - discountA;
                });
            case 'popular':
            default:
                return [...productsToSort].sort((a, b) => {
                    if (a.isBestSeller && !b.isBestSeller) return -1;
                    if (!a.isBestSeller && b.isBestSeller) return 1;
                    return b.reviews - a.reviews;
                });
        }
    }

    // Function to update products based on filters and sorting
    function updateProducts() {
        const filteredProducts = filterProducts();
        const sortedProducts = sortProducts(filteredProducts);
        renderProducts(sortedProducts);
    }

    // Initialize products
    updateProducts();

    // Add event listeners to filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            updateProducts();
        });
    });

    // Add event listener to sort select
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', updateProducts);
    }

    // Quick view product function
    function quickViewProduct(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        showNotification(`Xem nhanh: ${product.name}`, 'info');
        // In a real implementation, this would open a modal with product details
    }

    // Add to wishlist function
    function addToWishlist(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        showNotification(`Đã thêm ${product.name} vào danh sách yêu thích`, 'success');
        // In a real implementation, this would add the product to the wishlist
    }

    // Add to cart function
    function addToCart(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        // Check if user is logged in
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.isLoggedIn) {
            // Show login prompt
            showLoginPrompt();
            return;
        }

        showNotification(`Đã thêm ${product.name} vào giỏ hàng`, 'success');

        // Get current cart from localStorage
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Add product to cart
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price.toLocaleString() + 'đ', // Chuyển đổi giá thành chuỗi có định dạng
            image: product.image,
            image1: product.image, // Thêm image1 để tương thích với AddCart.html
            quantity: 1
        });

        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));

        // Update cart count
        updateCartCount();
    }

    // Show login prompt function
    function showLoginPrompt() {
        // Create login prompt container
        const loginPrompt = document.createElement('div');
        loginPrompt.className = 'login-prompt-overlay';
        loginPrompt.style.position = 'fixed';
        loginPrompt.style.top = '0';
        loginPrompt.style.left = '0';
        loginPrompt.style.width = '100%';
        loginPrompt.style.height = '100%';
        loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        loginPrompt.style.display = 'flex';
        loginPrompt.style.alignItems = 'center';
        loginPrompt.style.justifyContent = 'center';
        loginPrompt.style.zIndex = '9999';
        loginPrompt.style.opacity = '0';
        loginPrompt.style.transition = 'opacity 0.3s ease';

        loginPrompt.innerHTML = `
            <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                    <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                    <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                </div>
                <div class="login-prompt-body" style="padding: 20px;">
                    <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                    <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                        <a href="login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                        <a href="register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                    </div>
                </div>
            </div>
        `;

        // Add to body
        document.body.appendChild(loginPrompt);

        // Show with animation
        setTimeout(() => {
            loginPrompt.style.opacity = '1';
        }, 10);

        // Close button event
        const closeButton = loginPrompt.querySelector('.login-prompt-close');
        closeButton.addEventListener('click', function() {
            loginPrompt.style.opacity = '0';
            setTimeout(() => {
                loginPrompt.remove();
            }, 300);
        });

        // Save current URL to redirect back after login
        localStorage.setItem('redirectAfterLogin', window.location.href);
    }

    // Update cart count function
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = cart.length;
        }
    }

    // Initialize cart count
    updateCartCount();

    // Load more button functionality
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            showNotification('Đang tải thêm sản phẩm...', 'info');
            // In a real implementation, this would load more products
            setTimeout(() => {
                showNotification('Đã tải tất cả sản phẩm', 'success');
                this.disabled = true;
                this.textContent = 'Đã tải tất cả';
            }, 1000);
        });
    }
});
