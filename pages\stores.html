<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="../styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <title>Hệ thống cửa hàng | Fashion Store</title>

    <style>
        /* Stores Page Styles */
        .stores-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .stores-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .stores-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .stores-header h1 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .stores-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .store-finder {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 40px;
        }

        .store-finder h2 {
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .store-search {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .store-search input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .store-search button {
            background-color: #ff6b6b;
            color: white;
            border: none;
            padding: 0 20px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .store-search button:hover {
            background-color: #ff5252;
        }

        .store-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-btn {
            background-color: #f0f0f0;
            color: #666;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .filter-btn:hover, .filter-btn.active {
            background-color: #ff6b6b;
            color: white;
        }

        .store-map {
            height: 400px;
            background-color: #f9f9f9;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 40px;
        }

        .store-map iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .stores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
        }

        .store-card {
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .store-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .store-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .store-content {
            padding: 20px;
        }

        .store-name {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
        }

        .store-address {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .store-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .store-info-item {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 14px;
        }

        .store-info-item i {
            width: 20px;
            margin-right: 10px;
            color: #ff6b6b;
        }

        .store-actions {
            display: flex;
            gap: 10px;
        }

        .store-btn {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-directions {
            background-color: #ff6b6b;
            color: white;
            border: none;
        }

        .btn-directions:hover {
            background-color: #ff5252;
        }

        .btn-details {
            background-color: #f0f0f0;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-details:hover {
            background-color: #e0e0e0;
        }

        .store-badge {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .store-badge.new {
            background-color: #2196F3;
        }

        .store-badge.flagship {
            background-color: #FFC107;
        }

        @media (max-width: 768px) {
            .store-search {
                flex-direction: column;
            }

            .stores-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
        </div>
    </div>

    <!-- Header Section -->
    <header id="header">
        <!-- Top Announcement Bar -->
        <div class="announcement-bar">
            <p><i class="fas fa-truck"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ</p>
        </div>

        <!-- Main Navigation -->
        <nav class="main-nav">
            <div class="container">
                <div class="nav-wrapper">
                    <!-- Mobile Menu Toggle -->
                    <div class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </div>

                    <!-- Logo -->
                    <div class="logo">
                        <a href="../index.html">
                            <img src="../img logo/logo 1.jpg" alt="Fashion Store Logo">
                        </a>
                    </div>

                  <!-- Main Navigation -->
        <nav class="main-nav">
            <div class="container">
                <div class="nav-wrapper">
                    <!-- Mobile Menu Toggle -->
                    <div class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </div>

                    <!-- Logo -->
                    <div class="logo">
                        <a href="../index.html">
                            <img src="../img logo/logo 1.jpg" alt="Fashion Store Logo">
                        </a>
                    </div>

                    <!-- Navigation Menu -->
                    <ul class="nav-menu">
                        <li class="nav-item has-dropdown">
                            <a href="../womenproducts-new.html">Nữ</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="../womenproducts-new.html">Áo sơ mi</a></li>
                                        <li><a href="../womenproducts-new.html">Áo thun</a></li>
                                        <li><a href="../womenproducts-new.html">Quần jean</a></li>
                                        <li><a href="../womenproducts-new.html">Váy & Đầm</a></li>
                                        <li><a href="../womenproducts-new.html">Áo khoác</a></li>
                                        <li><a href="../womenproducts-new.html">Đồ thể thao</a></li>
                                        <li><a href="../womenproducts-new.html">Đồ ngủ</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="../womenproducts-new.html">Mùa hè 2024</a></li>
                                        <li><a href="../womenproducts-new.html">Công sở thanh lịch</a></li>
                                        <li><a href="../womenproducts-new.html">Dạo phố năng động</a></li>
                                        <li><a href="../womenproducts-new.html">Dự tiệc sang trọng</a></li>
                                        <li><a href="../sexy-women.html">Đồ sexy</a></li>
                                        <li><a href="../underwear-women.html">Nội y</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img womens/women 15.webp" alt="Women's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="../womenproducts-new.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="../menproducts-new.html">Nam</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="../menproducts-new.html">Áo sơ mi</a></li>
                                        <li><a href="../menproducts-new.html">Áo thun</a></li>
                                        <li><a href="../menproducts-new.html">Quần jean</a></li>
                                        <li><a href="../menproducts-new.html">Quần kaki</a></li>
                                        <li><a href="../menproducts-new.html">Áo khoác</a></li>
                                        <li><a href="../menproducts-new.html">Đồ thể thao</a></li>
                                        <li><a href="../underwear-men.html">Nội y</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="../menproducts-new.html">Mùa hè 2024</a></li>
                                        <li><a href="../menproducts-new.html">Công sở lịch lãm</a></li>
                                        <li><a href="../menproducts-new.html">Thể thao năng động</a></li>
                                        <li><a href="../menproducts-new.html">Dạo phố cá tính</a></li>
                                        <li><a href="../menproducts-new.html">Hàng mới về</a></li>
                                        <li><a href="../menproducts-new.html">Bán chạy nhất</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img mens/men 10.jpg" alt="Men's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="../menproducts-new.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="./kids.html" class="active">Trẻ em</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Bé gái</h3>
                                    <ul>
                                        <li><a href="kids.html?category=girls">Áo</a></li>
                                        <li><a href="kids.html?category=girls">Quần</a></li>
                                        <li><a href="kids.html?category=girls">Váy đầm</a></li>
                                        <li><a href="kids.html?category=girls">Đồ ngủ</a></li>
                                        <li><a href="kids.html?category=girls">Đồ thể thao</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bé trai</h3>
                                    <ul>
                                        <li><a href="kids.html?category=boys">Áo</a></li>
                                        <li><a href="kids.html?category=boys">Quần</a></li>
                                        <li><a href="kids.html?category=boys">Đồ ngủ</a></li>
                                        <li><a href="kids.html?category=boys">Đồ thể thao</a></li>
                                        <li><a href="kids.html?category=boys">Bộ quần áo</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img womens/women 12.webp" alt="Kids Collection">
                                    <h4>Bộ sưu tập mới cho bé</h4>
                                    <a href="kids.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="sale.html">Khuyến mãi</a>
                        </li>
                        <li class="nav-item">
                            <a href="about.html">Giới thiệu</a>
                        </li>
                    </ul>

                    <!-- Right Navigation -->
                    <div class="nav-right">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="Tìm kiếm...">
                            <button id="search-btn"><i class="fas fa-search"></i></button>
                        </div>
                        <div class="nav-icons">
                            <a href="./account.html" class="nav-icon" title="Tài khoản">
                                <i class="fas fa-user"></i>
                            </a>
                            <a href="./wishlist.html" class="nav-icon" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </a>
                            <a href="../AddCart.html" class="nav-icon cart-icon" title="Giỏ hàng">
                                <i class="fas fa-shopping-bag"></i>
                                <span class="cart-count">0</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Stores Content -->
    <div class="stores-container">
        <div class="stores-header">
            <h1>Hệ thống cửa hàng</h1>
            <p>Tìm kiếm cửa hàng Fashion Store gần bạn nhất. Chúng tôi có hơn 20 cửa hàng trên toàn quốc với đầy đủ các sản phẩm thời trang mới nhất.</p>
        </div>

        <div class="store-finder">
            <h2>Tìm cửa hàng</h2>
            <div class="store-search">
                <input type="text" placeholder="Nhập tỉnh/thành phố hoặc quận/huyện..." id="store-search-input">
                <button id="store-search-btn">Tìm kiếm</button>
            </div>
            <div class="store-filters">
                <button class="filter-btn active" data-filter="all">Tất cả</button>
                <button class="filter-btn" data-filter="hcm">TP. Hồ Chí Minh</button>
                <button class="filter-btn" data-filter="hanoi">Hà Nội</button>
                <button class="filter-btn" data-filter="danang">Đà Nẵng</button>
                <button class="filter-btn" data-filter="flagship">Cửa hàng lớn</button>
                <button class="filter-btn" data-filter="new">Cửa hàng mới</button>
            </div>
        </div>

        <div class="store-map">
            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d501725.3382536451!2d106.**************!3d10.755341073400366!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x317529292e8d3dd1%3A0xf15f5aad773c112b!2zSOG7kyBDaMOtIE1pbmggQ2l0eSwgVmlldG5hbQ!5e0!3m2!1sen!2s!4v1624271613897!5m2!1sen!2s" allowfullscreen="" loading="lazy"></iframe>
        </div>

        <div class="stores-grid" id="stores-grid">
            <!-- Store 1 -->
            <div class="store-card" data-location="hcm">
                <img src="https://images.unsplash.com/photo-1567401893414-76b7b1e5a7a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Quận 1" class="store-image">
                <div class="store-content">
                    <span class="store-badge flagship">Cửa hàng lớn</span>
                    <h3 class="store-name">Fashion Store Quận 1</h3>
                    <p class="store-address">123 Đường Nguyễn Huệ, Quận 1, TP. Hồ Chí Minh</p>
                    <div class="store-info">
                        <div class="store-info-item">
                            <i class="fas fa-phone"></i>
                            <span>028 1234 5678</span>
                        </div>
                        <div class="store-info-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00 - 22:00 (Thứ 2 - Chủ nhật)</span>
                        </div>
                    </div>
                    <div class="store-actions">
                        <a href="https://goo.gl/maps/1JyKmNuXZdv5JGWP6" target="_blank" class="store-btn btn-directions">Chỉ đường</a>
                        <a href="#" class="store-btn btn-details">Chi tiết</a>
                    </div>
                </div>
            </div>

            <!-- Store 2 -->
            <div class="store-card" data-location="hcm">
                <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Quận 3" class="store-image">
                <div class="store-content">
                    <h3 class="store-name">Fashion Store Quận 3</h3>
                    <p class="store-address">456 Đường Nam Kỳ Khởi Nghĩa, Quận 3, TP. Hồ Chí Minh</p>
                    <div class="store-info">
                        <div class="store-info-item">
                            <i class="fas fa-phone"></i>
                            <span>028 2345 6789</span>
                        </div>
                        <div class="store-info-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00 - 21:30 (Thứ 2 - Chủ nhật)</span>
                        </div>
                    </div>
                    <div class="store-actions">
                        <a href="https://goo.gl/maps/1JyKmNuXZdv5JGWP6" target="_blank" class="store-btn btn-directions">Chỉ đường</a>
                        <a href="#" class="store-btn btn-details">Chi tiết</a>
                    </div>
                </div>
            </div>

            <!-- Store 3 -->
            <div class="store-card" data-location="hanoi">
                <img src="https://images.unsplash.com/photo-1555529669-e69e7aa0ba9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Hoàn Kiếm" class="store-image">
                <div class="store-content">
                    <span class="store-badge flagship">Cửa hàng lớn</span>
                    <h3 class="store-name">Fashion Store Hoàn Kiếm</h3>
                    <p class="store-address">789 Đường Hàng Bài, Quận Hoàn Kiếm, Hà Nội</p>
                    <div class="store-info">
                        <div class="store-info-item">
                            <i class="fas fa-phone"></i>
                            <span>024 3456 7890</span>
                        </div>
                        <div class="store-info-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00 - 22:00 (Thứ 2 - Chủ nhật)</span>
                        </div>
                    </div>
                    <div class="store-actions">
                        <a href="https://goo.gl/maps/1JyKmNuXZdv5JGWP6" target="_blank" class="store-btn btn-directions">Chỉ đường</a>
                        <a href="#" class="store-btn btn-details">Chi tiết</a>
                    </div>
                </div>
            </div>

            <!-- Store 4 -->
            <div class="store-card" data-location="hanoi">
                <img src="https://images.unsplash.com/photo-1567401893414-76b7b1e5a7a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Cầu Giấy" class="store-image">
                <div class="store-content">
                    <span class="store-badge new">Mới</span>
                    <h3 class="store-name">Fashion Store Cầu Giấy</h3>
                    <p class="store-address">101 Đường Xuân Thủy, Quận Cầu Giấy, Hà Nội</p>
                    <div class="store-info">
                        <div class="store-info-item">
                            <i class="fas fa-phone"></i>
                            <span>024 4567 8901</span>
                        </div>
                        <div class="store-info-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00 - 21:30 (Thứ 2 - Chủ nhật)</span>
                        </div>
                    </div>
                    <div class="store-actions">
                        <a href="https://goo.gl/maps/1JyKmNuXZdv5JGWP6" target="_blank" class="store-btn btn-directions">Chỉ đường</a>
                        <a href="#" class="store-btn btn-details">Chi tiết</a>
                    </div>
                </div>
            </div>

            <!-- Store 5 -->
            <div class="store-card" data-location="danang">
                <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Đà Nẵng" class="store-image">
                <div class="store-content">
                    <h3 class="store-name">Fashion Store Đà Nẵng</h3>
                    <p class="store-address">202 Đường Bạch Đằng, Quận Hải Châu, Đà Nẵng</p>
                    <div class="store-info">
                        <div class="store-info-item">
                            <i class="fas fa-phone"></i>
                            <span>0236 567 8901</span>
                        </div>
                        <div class="store-info-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00 - 21:00 (Thứ 2 - Chủ nhật)</span>
                        </div>
                    </div>
                    <div class="store-actions">
                        <a href="https://goo.gl/maps/1JyKmNuXZdv5JGWP6" target="_blank" class="store-btn btn-directions">Chỉ đường</a>
                        <a href="#" class="store-btn btn-details">Chi tiết</a>
                    </div>
                </div>
            </div>

            <!-- Store 6 -->
            <div class="store-card" data-location="hcm">
                <img src="https://images.unsplash.com/photo-1555529669-e69e7aa0ba9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Fashion Store Quận 7" class="store-image">
                <div class="store-content">
                    <span class="store-badge new">Mới</span>
                    <h3 class="store-name">Fashion Store Quận 7</h3>
                    <p class="store-address">303 Đường Nguyễn Thị Thập, Quận 7, TP. Hồ Chí Minh</p>
                    <div class="store-info">
                        <div class="store-info-item">
                            <i class="fas fa-phone"></i>
                            <span>028 3456 7890</span>
                        </div>
                        <div class="store-info-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00 - 22:00 (Thứ 2 - Chủ nhật)</span>
                        </div>
                    </div>
                    <div class="store-actions">
                        <a href="https://goo.gl/maps/1JyKmNuXZdv5JGWP6" target="_blank" class="store-btn btn-directions">Chỉ đường</a>
                        <a href="#" class="store-btn btn-details">Chi tiết</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Thông tin</h3>
                    <ul>
                        <li><a href="../about.html">Về chúng tôi</a></li>
                        <li><a href="../contact.html">Liên hệ</a></li>
                        <li><a href="../blog.html">Tin tức & Xu hướng</a></li>
                        <li><a href="../faq.html">Câu hỏi thường gặp</a></li>
                        <li><a href="../stores.html">Hệ thống cửa hàng</a></li>
                        <li><a href="../careers.html">Tuyển dụng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Dịch vụ khách hàng</h3>
                    <ul>
                        <li><a href="../account.html">Tài khoản của tôi</a></li>
                        <li><a href="../orders.html">Theo dõi đơn hàng</a></li>
                        <li><a href="../shipping.html">Chính sách vận chuyển</a></li>
                        <li><a href="../returns.html">Chính sách đổi trả</a></li>
                        <li><a href="../size-guide.html">Hướng dẫn chọn size</a></li>
                        <li><a href="../gift-cards.html">Thẻ quà tặng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Liên hệ với chúng tôi</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                123 Đường Nguyễn Trãi, Quận 1, TP. Hồ Chí Minh
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-text">
                                +84 (0) 123 456 789
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <EMAIL>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                Thứ Hai - Chủ Nhật: 9:00 - 21:00
                            </div>
                        </div>
                    </div>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <div class="footer-newsletter">
                        <h4>Đăng ký nhận tin</h4>
                        <p>Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
                        <form class="footer-newsletter-form">
                            <input type="email" placeholder="Email của bạn" required>
                            <button type="submit"><i class="fas fa-paper-plane"></i></button>
                        </form>
                    </div>
                    <div class="app-download">
                        <h4>Tải ứng dụng</h4>
                        <div class="app-buttons">
                            <a href="#"><img src="../img logo/images.png" alt="App Store"></a>
                            <a href="#"><img src="../img logo/img 2.png" alt="Google Play"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Fashion Store. Tất cả các quyền được bảo lưu. | Thiết kế bởi <a href="#">MindX JSA08</a></p>
            </div>
        </div>
    </footer>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/lucky-wheel.js"></script>
    <script src="../script/chatbot.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/search.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Store Filter Functionality
            const filterButtons = document.querySelectorAll('.filter-btn');
            const storeCards = document.querySelectorAll('.store-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get filter value
                    const filterValue = this.getAttribute('data-filter');

                    // Filter stores
                    storeCards.forEach(card => {
                        if (filterValue === 'all') {
                            card.style.display = 'block';
                        } else if (filterValue === 'flagship' && card.querySelector('.store-badge.flagship')) {
                            card.style.display = 'block';
                        } else if (filterValue === 'new' && card.querySelector('.store-badge.new')) {
                            card.style.display = 'block';
                        } else if (card.getAttribute('data-location') === filterValue) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Store Search Functionality
            const searchInput = document.getElementById('store-search-input');
            const searchButton = document.getElementById('store-search-btn');

            searchButton.addEventListener('click', function() {
                const searchTerm = searchInput.value.toLowerCase().trim();

                if (searchTerm === '') {
                    storeCards.forEach(card => {
                        card.style.display = 'block';
                    });
                    return;
                }

                storeCards.forEach(card => {
                    const storeName = card.querySelector('.store-name').textContent.toLowerCase();
                    const storeAddress = card.querySelector('.store-address').textContent.toLowerCase();

                    if (storeName.includes(searchTerm) || storeAddress.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });

            // Hiển thị tên người dùng đã đăng nhập
            function displayUsername() {
                const user = JSON.parse(localStorage.getItem('user'));
                if (user && user.isLoggedIn) {
                    const userIcon = document.querySelector('.nav-icons .nav-icon[title="Tài khoản"]');
                    if (userIcon) {
                        // Thay đổi icon thành tên người dùng
                        const userName = user.name || user.email.split('@')[0];
                        userIcon.innerHTML = `<span class="user-name">${userName}</span>`;
                        userIcon.title = `Xin chào, ${userName}`;

                        // Thêm sự kiện click để hiển thị menu tài khoản
                        userIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showAccountMenu(this);
                        });
                    }
                }
            }

            // Hiển thị menu tài khoản
            function showAccountMenu(element) {
                // Xóa menu cũ nếu có
                const existingMenu = document.querySelector('.account-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }

                // Tạo menu tài khoản
                const accountMenu = document.createElement('div');
                accountMenu.className = 'account-menu';

                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user'));
                const userName = user.name || user.email.split('@')[0];

                // Thiết lập nội dung menu
                accountMenu.innerHTML = `
                    <div class="account-menu-header">
                        <div class="account-menu-user">
                            <div class="account-menu-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="account-menu-info">
                                <div class="account-menu-name">${userName}</div>
                                <div class="account-menu-email">${user.email}</div>
                            </div>
                        </div>
                    </div>
                    <div class="account-menu-body">
                        <a href="./account.html" class="account-menu-item">
                            <i class="fas fa-user"></i> Tài khoản của tôi
                        </a>
                        <a href="./orders.html" class="account-menu-item">
                            <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                        </a>
                        <a href="./wishlist.html" class="account-menu-item">
                            <i class="fas fa-heart"></i> Danh sách yêu thích
                        </a>
                        <a href="./settings.html" class="account-menu-item">
                            <i class="fas fa-cog"></i> Cài đặt
                        </a>
                        <div class="account-menu-divider"></div>
                        <a href="#" class="account-menu-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Đăng xuất
                        </a>
                    </div>
                `;

                // Định vị menu
                const rect = element.getBoundingClientRect();
                accountMenu.style.top = `${rect.bottom + window.scrollY}px`;
                accountMenu.style.right = `${window.innerWidth - rect.right}px`;

                // Thêm vào body
                document.body.appendChild(accountMenu);

                // Hiển thị với hiệu ứng
                setTimeout(() => {
                    accountMenu.classList.add('show');
                }, 10);

                // Thêm sự kiện cho nút đăng xuất
                const logoutBtn = accountMenu.querySelector('.logout-btn');
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });

                // Đóng menu khi click bên ngoài
                document.addEventListener('click', function closeMenu(e) {
                    if (!accountMenu.contains(e.target) && e.target !== element) {
                        accountMenu.classList.remove('show');
                        setTimeout(() => {
                            accountMenu.remove();
                        }, 300);
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }

            // Hàm đăng xuất
            function logout() {
                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user')) || {};

                // Cập nhật trạng thái đăng nhập
                user.isLoggedIn = false;

                // Lưu vào localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Hiển thị thông báo
                alert('Đăng xuất thành công');

                // Khôi phục icon người dùng
                const userIcon = document.querySelector('.nav-icons .nav-icon[title="Xin chào, ' + (user.name || user.email.split('@')[0]) + '"]');
                if (userIcon) {
                    userIcon.innerHTML = '<i class="fas fa-user"></i>';
                    userIcon.title = 'Tài khoản';
                }

                // Chuyển hướng về trang chủ sau một khoảng thời gian ngắn
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            }

            // Gọi hàm hiển thị tên người dùng
            displayUsername();
        });
    </script>
</body>
</html>