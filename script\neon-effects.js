// Neon Effects JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Wave effect when clicking on the form
    const formContainer = document.querySelector('.neon-form-container');
    const waveContainer = document.querySelector('.wave-container');
    
    if (formContainer && waveContainer) {
        formContainer.addEventListener('click', function(e) {
            // Create a new wave element
            const wave = document.createElement('div');
            wave.classList.add('wave');
            
            // Position the wave at the click position
            const rect = formContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            wave.style.left = x + 'px';
            wave.style.top = y + 'px';
            
            // Add the wave to the container
            waveContainer.appendChild(wave);
            
            // Start the animation
            setTimeout(() => {
                wave.classList.add('wave-animation');
            }, 10);
            
            // Remove the wave after animation completes
            setTimeout(() => {
                wave.remove();
            }, 1000);
        });
    }
    
    // Flicker effect for neon title
    const neonTitle = document.querySelector('.neon-title');
    if (neonTitle) {
        setInterval(() => {
            if (Math.random() > 0.95) {
                neonTitle.style.opacity = 0.7;
                setTimeout(() => {
                    neonTitle.style.opacity = 1;
                }, 100);
            }
        }, 500);
    }
    
    // Button shake effect
    const neonButtons = document.querySelectorAll('.neon-button');
    neonButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Prevent default form submission for demo purposes
            // e.preventDefault();
            
            // Add shake class
            this.classList.add('shake');
            
            // Remove shake class after animation
            setTimeout(() => {
                this.classList.remove('shake');
            }, 300);
        });
    });
    
    // Dynamic color change for electric border
    const electricBorders = document.querySelectorAll('.electric-border');
    let hue = 0;
    
    setInterval(() => {
        hue = (hue + 1) % 360;
        const primaryColor = `hsl(${hue}, 100%, 50%)`;
        const secondaryColor = `hsl(${(hue + 120) % 360}, 100%, 50%)`;
        const tertiaryColor = `hsl(${(hue + 240) % 360}, 100%, 50%)`;
        
        document.documentElement.style.setProperty('--neon-primary', primaryColor);
        document.documentElement.style.setProperty('--neon-secondary', secondaryColor);
        document.documentElement.style.setProperty('--neon-tertiary', tertiaryColor);
    }, 100);
    
    // Input focus effects
    const neonInputs = document.querySelectorAll('.neon-input');
    neonInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focus');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focus');
        });
    });
    
    // Password visibility toggle
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);
            
            // Toggle icon
            this.innerHTML = type === 'password' ? 
                '<i class="fas fa-eye"></i>' : 
                '<i class="fas fa-eye-slash"></i>';
        });
    });
    
    // Create random lightning flashes in the background
    const createLightningFlash = () => {
        if (!formContainer) return;
        
        const flash = document.createElement('div');
        flash.classList.add('lightning-flash');
        
        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        
        flash.style.left = `${x}%`;
        flash.style.top = `${y}%`;
        
        formContainer.appendChild(flash);
        
        // Remove after animation
        setTimeout(() => {
            flash.remove();
        }, 200);
    };
    
    // Trigger lightning occasionally
    setInterval(() => {
        if (Math.random() > 0.9) {
            createLightningFlash();
        }
    }, 2000);
    
    // Form validation with neon effects
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email');
            const password = document.getElementById('password');
            
            let isValid = true;
            
            if (!email.value || !email.value.includes('@')) {
                highlightInvalidInput(email);
                isValid = false;
            } else {
                removeInvalidHighlight(email);
            }
            
            if (!password.value || password.value.length < 6) {
                highlightInvalidInput(password);
                isValid = false;
            } else {
                removeInvalidHighlight(password);
            }
            
            if (isValid) {
                // Show success animation
                showSuccessAnimation();
                
                // Simulate login (replace with actual login logic)
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            }
        });
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username');
            const email = document.getElementById('email');
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm-password');
            
            let isValid = true;
            
            if (!username.value || username.value.length < 3) {
                highlightInvalidInput(username);
                isValid = false;
            } else {
                removeInvalidHighlight(username);
            }
            
            if (!email.value || !email.value.includes('@')) {
                highlightInvalidInput(email);
                isValid = false;
            } else {
                removeInvalidHighlight(email);
            }
            
            if (!password.value || password.value.length < 6) {
                highlightInvalidInput(password);
                isValid = false;
            } else {
                removeInvalidHighlight(password);
            }
            
            if (!confirmPassword.value || confirmPassword.value !== password.value) {
                highlightInvalidInput(confirmPassword);
                isValid = false;
            } else {
                removeInvalidHighlight(confirmPassword);
            }
            
            if (isValid) {
                // Show success animation
                showSuccessAnimation();
                
                // Simulate registration (replace with actual registration logic)
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
            }
        });
    }
    
    function highlightInvalidInput(input) {
        input.style.borderColor = '#ff0000';
        input.style.boxShadow = '0 0 10px rgba(255, 0, 0, 0.7)';
        
        // Add shake animation
        input.classList.add('shake');
        setTimeout(() => {
            input.classList.remove('shake');
        }, 300);
    }
    
    function removeInvalidHighlight(input) {
        input.style.borderColor = '';
        input.style.boxShadow = '';
    }
    
    function showSuccessAnimation() {
        const successOverlay = document.createElement('div');
        successOverlay.classList.add('success-overlay');
        
        const successIcon = document.createElement('div');
        successIcon.classList.add('success-icon');
        successIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
        
        successOverlay.appendChild(successIcon);
        document.body.appendChild(successOverlay);
        
        setTimeout(() => {
            successOverlay.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            successOverlay.classList.remove('show');
            setTimeout(() => {
                successOverlay.remove();
            }, 500);
        }, 1500);
    }
});
