/* Authentication Check Styles */

/* User Name Display */
.user-name {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.nav-icon .user-name {
    display: inline-block;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 5px;
}

/* User Avatar */
.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498db, #2c3e50);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Logged in nav icon */
.nav-icon.logged-in {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 20px;
    padding: 3px 10px 3px 3px;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    position: relative;
}

.nav-icon.logged-in:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.nav-icon.logged-in:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Account Menu */
.account-menu {
    position: absolute;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    width: 280px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(10px);
    visibility: hidden;
    transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
    border: 1px solid #eee;
    overflow: hidden;
}

.account-menu.show {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

.account-menu-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.account-menu-user {
    display: flex;
    align-items: center;
}

.account-menu-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    overflow: hidden;
}

.account-menu-avatar i {
    font-size: 20px;
    color: #999;
}

.account-menu-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.account-menu-info {
    flex: 1;
}

.account-menu-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.account-menu-email {
    font-size: 12px;
    color: #666;
}

.account-menu-body {
    padding: 10px 0;
}

.account-menu-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.account-menu-item:hover {
    background-color: #f9f9f9;
    transform: translateX(5px);
}

.account-menu-item.active {
    background-color: #f0f7ff;
    color: #3498db;
    font-weight: 500;
}

.account-menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #3498db;
}

.account-menu-item i {
    width: 20px;
    margin-right: 10px;
    color: #666;
    transition: color 0.3s ease;
}

.account-menu-item:hover i,
.account-menu-item.active i {
    color: #3498db;
}

.account-menu-divider {
    height: 1px;
    background-color: #eee;
    margin: 5px 0;
}

.logout-btn {
    color: #ff6b6b !important;
}

.logout-btn i {
    color: #ff6b6b !important;
}

/* Login Prompt Styles */
.login-prompt-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.login-prompt-overlay.show {
    opacity: 1;
    visibility: visible;
}

.login-prompt {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s;
}

.login-prompt-overlay.show .login-prompt {
    transform: translateY(0);
}

.login-prompt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f8f8;
    border-bottom: 1px solid #eee;
}

.login-prompt-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.login-prompt-close {
    background: none;
    border: none;
    font-size: 16px;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.login-prompt-close:hover {
    color: #ff6b6b;
}

.login-prompt-body {
    padding: 20px;
}

.login-prompt-body p {
    margin-top: 0;
    margin-bottom: 20px;
    color: #666;
}

.login-prompt-buttons {
    display: flex;
    gap: 10px;
}

.login-prompt-buttons a {
    flex: 1;
    padding: 10px;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-login {
    background-color: #ff6b6b;
    color: white;
}

.btn-login:hover {
    background-color: #ff5252;
}

.btn-register {
    background-color: #f8f8f8;
    color: #333;
    border: 1px solid #ddd;
}

.btn-register:hover {
    background-color: #eee;
}

/* Responsive Styles */
@media (max-width: 576px) {
    .login-prompt {
        width: 95%;
    }

    .login-prompt-header {
        padding: 12px 15px;
    }

    .login-prompt-header h3 {
        font-size: 16px;
    }

    .login-prompt-body {
        padding: 15px;
    }

    .login-prompt-buttons {
        flex-direction: column;
    }
}
