<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/sections.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/account.css">
    <link rel="stylesheet" type="text/css" href="../styles/account-new.css">
    <link rel="stylesheet" type="text/css" href="../styles/account-additional.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css">

    <title>Tài khoản | Fashion Store</title>

    <style>
        /* Đảm bảo màu sắc giống với index.html */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #f5f5f5;
            --accent-color: #e74c3c;
            --accent-hover: #c0392b;
            --text-color: #333;
            --light-text: #fff;
            --dark-text: #222;
            --border-color: #ddd;
            --hover-color: #f9f9f9;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Navbar styles */
        .announcement-bar {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .main-nav {
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-item > a {
            color: var(--primary-color);
        }

        .nav-item > a:hover {
            color: var(--accent-color);
        }

        .nav-icon {
            color: var(--primary-color);
        }

        .nav-icon:hover {
            color: var(--accent-color);
        }

        /* Footer styles */
        .footer {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .footer::before {
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
        }

        .footer-column h3 {
            color: var(--light-text);
        }

        .footer-column h3::after {
            background-color: var(--accent-color);
        }

        .footer-column ul li a {
            color: rgba(255, 255, 255, 0.7);
        }

        .footer-column ul li a::before {
            color: var(--accent-color);
        }

        .footer-column ul li a:hover {
            color: var(--light-text);
        }

        .social-icon {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--light-text);
        }

        .social-icon:hover {
            background-color: var(--accent-color);
            color: var(--light-text);
        }

        .footer-newsletter-form button {
            background-color: var(--accent-color);
        }

        .footer-newsletter-form button:hover {
            background-color: var(--accent-hover);
        }

        .contact-icon {
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Account Page Content -->
    <div class="account-container">
        <div class="container">
            <div class="account-header">
                <h1>Tài khoản của tôi</h1>
                <p>Quản lý thông tin tài khoản và đơn hàng của bạn</p>
            </div>

            <div class="account-content">
                <!-- Account Sidebar -->
                <div class="account-sidebar">
                    <div class="account-user">
                        <div class="user-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="user-info">
                            <h3 id="user-name">Khách</h3>
                            <p id="user-status">Bạn chưa đăng nhập</p>
                        </div>
                    </div>
                    <ul class="account-menu">
                        <li class="account-menu-item active"><a href="#dashboard"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                        <li class="account-menu-item"><a href="#orders"><i class="fas fa-shopping-bag"></i> Đơn hàng của tôi</a></li>
                        <li class="account-menu-item"><a href="#wishlist"><i class="fas fa-heart"></i> Danh sách yêu thích</a></li>
                        <li class="account-menu-item"><a href="#address"><i class="fas fa-map-marker-alt"></i> Địa chỉ của tôi</a></li>
                        <li class="account-menu-item"><a href="#profile"><i class="fas fa-user-edit"></i> Thông tin cá nhân</a></li>
                        <li class="account-menu-item"><a href="../membership.html"><i class="fas fa-id-card"></i> Thẻ thành viên</a></li>
                        <li class="account-menu-item"><a href="#security"><i class="fas fa-shield-alt"></i> Bảo mật tài khoản</a></li>
                        <li class="account-menu-item"><a href="#login-history"><i class="fas fa-history"></i> Nhật ký đăng nhập</a></li>
                        <li class="account-menu-item" id="logout-btn"><a href="#"><i class="fas fa-sign-out-alt"></i> Đăng xuất</a></li>
                    </ul>
                </div>

                <!-- Account Main Content -->
                <div class="account-main">
                    <!-- Not Logged In Message -->
                    <div class="not-logged-in" id="not-logged-in">
                        <div class="login-message">
                            <h2>Vui lòng đăng nhập</h2>
                            <p>Bạn cần đăng nhập để xem thông tin tài khoản và quản lý đơn hàng</p>
                            <div class="login-buttons">
                                <a href="login.html" class="btn btn-primary">Đăng nhập</a>
                                <a href="register.html" class="btn btn-secondary">Đăng ký</a>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Section (visible when logged in) -->
                    <div class="account-section" id="dashboard-section" style="display: none;">
                        <h2>Xin chào, <span id="dashboard-name">Khách</span>!</h2>
                        <p>Từ trang tổng quan tài khoản của bạn, bạn có thể xem các đơn đặt hàng gần đây, quản lý địa chỉ giao hàng và thanh toán, cũng như chỉnh sửa mật khẩu và thông tin tài khoản của bạn.</p>

                        <div class="dashboard-stats">
                            <div class="stat-box" data-aos="fade-up">
                                <div class="stat-icon"><i class="fas fa-shopping-bag"></i></div>
                                <div class="stat-info">
                                    <h3 id="order-count">0</h3>
                                    <p>Đơn hàng</p>
                                </div>
                            </div>
                            <div class="stat-box" data-aos="fade-up" data-aos-delay="100">
                                <div class="stat-icon"><i class="fas fa-heart"></i></div>
                                <div class="stat-info">
                                    <h3 id="wishlist-count">0</h3>
                                    <p>Sản phẩm yêu thích</p>
                                </div>
                            </div>
                            <div class="stat-box" data-aos="fade-up" data-aos-delay="200">
                                <div class="stat-icon"><i class="fas fa-tag"></i></div>
                                <div class="stat-info">
                                    <h3 id="coupon-count">0</h3>
                                    <p>Mã giảm giá</p>
                                </div>
                            </div>
                            <div class="stat-box membership-box" data-aos="fade-up" data-aos-delay="300">
                                <a href="../membership.html" style="text-decoration: none; color: inherit;">
                                    <div class="stat-icon"><i class="fas fa-coins"></i></div>
                                    <div class="stat-info">
                                        <h3 id="points-count">0</h3>
                                        <p>Điểm tích lũy</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Membership Banner -->
                        <div class="membership-banner" data-aos="fade-up" style="background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d); color: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; display: flex; align-items: center; justify-content: space-between;">
                            <div style="flex: 1;">
                                <h3 style="margin-top: 0; font-size: 1.5rem;">Thẻ Thành Viên</h3>
                                <p style="margin-bottom: 10px;">Tận hưởng ưu đãi độc quyền với thẻ thành viên. Tích điểm với mỗi đơn hàng và nhận nhiều đặc quyền hấp dẫn!</p>
                                <a href="../membership.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 5px; text-decoration: none; transition: background 0.3s;">Xem thẻ thành viên</a>
                            </div>
                            <div style="margin-left: 20px;">
                                <i class="fas fa-id-card" style="font-size: 4rem; opacity: 0.8;"></i>
                            </div>
                        </div>

                        <div class="account-activity" data-aos="fade-up">
                            <h3>Hoạt động gần đây</h3>
                            <div class="activity-timeline">
                                <div class="timeline-item">
                                    <div class="timeline-icon"><i class="fas fa-user"></i></div>
                                    <div class="timeline-content">
                                        <h4>Đăng nhập thành công</h4>
                                        <p>Bạn đã đăng nhập vào tài khoản</p>
                                        <span class="timeline-date">Hôm nay</span>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-icon"><i class="fas fa-heart"></i></div>
                                    <div class="timeline-content">
                                        <h4>Thêm sản phẩm yêu thích</h4>
                                        <p>Bạn đã thêm sản phẩm vào danh sách yêu thích</p>
                                        <span class="timeline-date">Hôm qua</span>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-icon"><i class="fas fa-shopping-cart"></i></div>
                                    <div class="timeline-content">
                                        <h4>Thêm sản phẩm vào giỏ hàng</h4>
                                        <p>Bạn đã thêm sản phẩm vào giỏ hàng</p>
                                        <span class="timeline-date">3 ngày trước</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="recent-orders" data-aos="fade-up">
                            <h3>Đơn hàng gần đây</h3>
                            <div class="empty-orders">
                                <p>Bạn chưa có đơn hàng nào</p>
                                <a href="../index.html" class="btn btn-primary">Mua sắm ngay</a>
                            </div>
                        </div>

                        <div class="recommended-products" data-aos="fade-up">
                            <h3>Gợi ý cho bạn</h3>
                            <div class="product-slider">
                                <div class="product-slide">
                                    <div class="product-card">
                                        <div class="product-image">
                                            <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-lji68k92ghdue5@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7qukw-lji68k92ghdue5@resize_w450_nl.webp" alt="Sản phẩm 1">
                                            <div class="product-overlay">
                                                <button class="product-overlay-btn"><i class="fas fa-eye"></i></button>
                                                <button class="product-overlay-btn"><i class="fas fa-heart"></i></button>
                                                <button class="product-overlay-btn"><i class="fas fa-shopping-bag"></i></button>
                                            </div>
                                        </div>
                                        <div class="product-info">
                                            <h3 class="product-name">(Sẵn be S) Áo Croptop Hai Dây Viền Bèo Thun Gân Đính Nơ</h3>
                                            <div class="product-price">
                                                <span class="current-price">122.000đ</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="product-slide">
                                    <div class="product-card">
                                        <div class="product-image">
                                            <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lu4gizkeknq71e@resize_w450_nl.webp" alt="Sản phẩm 2">
                                            <div class="product-overlay">
                                                <button class="product-overlay-btn"><i class="fas fa-eye"></i></button>
                                                <button class="product-overlay-btn"><i class="fas fa-heart"></i></button>
                                                <button class="product-overlay-btn"><i class="fas fa-shopping-bag"></i></button>
                                            </div>
                                        </div>
                                        <div class="product-info">
                                            <h3 class="product-name">
                                                Áo thun nữ dáng ôm thun cotton nhung phối ren thêu trái tim MONÁ - HEARTY
                                        </h3>
                                            <div class="product-price">
                                                <span class="current-price">100.000đ</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="product-slide">
                                    <div class="product-card">
                                        <div class="product-image">
                                            <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lwqwt6y83m5530@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lwqwt6y83m5530@resize_w450_nl.webp" alt="Sản phẩm 3">
                                            <div class="product-overlay">
                                                <button class="product-overlay-btn"><i class="fas fa-eye"></i></button>
                                                <button class="product-overlay-btn"><i class="fas fa-heart"></i></button>
                                                <button class="product-overlay-btn"><i class="fas fa-shopping-bag"></i></button>
                                            </div>
                                        </div>
                                        <div class="product-info">
                                            <h3 class="product-name">Chân váy nhún bèo nhiều tầng vải xốp in họa tiết nơ MONÁ - CUNCUN</h3>
                                            <div class="product-price">
                                                <span class="current-price">299.000đ</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Section -->
                    <div class="account-section" id="profile-section" style="display: none;">
                        <h2>Thông tin cá nhân</h2>
                        <p>Cập nhật thông tin cá nhân của bạn để cải thiện trải nghiệm mua sắm.</p>

                        <div class="profile-content">
                            <div class="profile-avatar">
                                <div class="avatar-container">
                                    <img id="user-avatar" src="../img/default-avatar.png" alt="Avatar">
                                    <div class="avatar-overlay">
                                        <i class="fas fa-camera"></i>
                                        <span>Thay đổi</span>
                                    </div>
                                </div>
                                <input type="file" id="avatar-upload" accept="image/*" style="display: none;">
                                <button class="btn btn-outline" id="change-avatar-btn">Thay đổi ảnh đại diện</button>
                            </div>

                            <div class="profile-form">
                                <form id="profile-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="profile-name">Họ và tên</label>
                                            <input type="text" id="profile-name" name="name" placeholder="Nhập họ và tên">
                                        </div>
                                        <div class="form-group">
                                            <label for="profile-email">Email</label>
                                            <input type="email" id="profile-email" name="email" placeholder="Nhập email" disabled>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="profile-phone">Số điện thoại</label>
                                            <input type="tel" id="profile-phone" name="phone" placeholder="Nhập số điện thoại">
                                        </div>
                                        <div class="form-group">
                                            <label for="profile-gender">Giới tính</label>
                                            <select id="profile-gender" name="gender">
                                                <option value="">Chọn giới tính</option>
                                                <option value="male">Nam</option>
                                                <option value="female">Nữ</option>
                                                <option value="other">Khác</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="profile-dob">Ngày sinh</label>
                                            <input type="date" id="profile-dob" name="dob">
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                                        <button type="reset" class="btn btn-outline">Hủy</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Address Section -->
                    <div class="account-section" id="address-section" style="display: none;">
                        <h2>Địa chỉ của tôi</h2>
                        <p>Quản lý địa chỉ giao hàng của bạn.</p>

                        <div class="address-actions">
                            <button class="btn btn-primary" id="add-address-btn"><i class="fas fa-plus"></i> Thêm địa chỉ mới</button>
                        </div>

                        <div class="address-list" id="address-list">
                            <!-- Address items will be added here dynamically -->
                            <div class="empty-address">
                                <p>Bạn chưa có địa chỉ nào</p>
                            </div>
                        </div>

                        <!-- Add/Edit Address Modal -->
                        <div class="modal" id="address-modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3 id="address-modal-title">Thêm địa chỉ mới</h3>
                                    <span class="modal-close">&times;</span>
                                </div>
                                <div class="modal-body">
                                    <form id="address-form">
                                        <input type="hidden" id="address-id">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="address-name">Họ và tên</label>
                                                <input type="text" id="address-name" name="name" placeholder="Nhập họ và tên người nhận" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="address-phone">Số điện thoại</label>
                                                <input type="tel" id="address-phone" name="phone" placeholder="Nhập số điện thoại" required>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="address-province">Tỉnh/Thành phố</label>
                                            <select id="address-province" name="province" required>
                                                <option value="">Chọn Tỉnh/Thành phố</option>
                                                <!-- Options will be added dynamically -->
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="address-district">Quận/Huyện</label>
                                            <select id="address-district" name="district" required>
                                                <option value="">Chọn Quận/Huyện</option>
                                                <!-- Options will be added dynamically -->
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="address-ward">Phường/Xã</label>
                                            <select id="address-ward" name="ward" required>
                                                <option value="">Chọn Phường/Xã</option>
                                                <!-- Options will be added dynamically -->
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="address-detail">Địa chỉ chi tiết</label>
                                            <input type="text" id="address-detail" name="detail" placeholder="Số nhà, tên đường, tòa nhà, ..." required>
                                        </div>

                                        <div class="form-group">
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="address-default" name="default">
                                                <label for="address-default">Đặt làm địa chỉ mặc định</label>
                                            </div>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" class="btn btn-primary">Lưu địa chỉ</button>
                                            <button type="button" class="btn btn-outline modal-cancel">Hủy</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Section -->
                    <div class="account-section" id="security-section" style="display: none;">
                        <h2>Bảo mật tài khoản</h2>
                        <p>Quản lý các thiết lập bảo mật cho tài khoản của bạn.</p>

                        <div class="security-content">
                            <div class="security-card">
                                <div class="security-card-header">
                                    <h3>Đổi mật khẩu</h3>
                                    <p>Thay đổi mật khẩu định kỳ để bảo vệ tài khoản của bạn.</p>
                                </div>
                                <div class="security-card-body">
                                    <form id="password-form">
                                        <div class="form-group">
                                            <label for="current-password">Mật khẩu hiện tại</label>
                                            <div class="password-input">
                                                <input type="password" id="current-password" name="currentPassword" placeholder="Nhập mật khẩu hiện tại">
                                                <span class="toggle-password" data-target="current-password"><i class="fas fa-eye"></i></span>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="new-password">Mật khẩu mới</label>
                                            <div class="password-input">
                                                <input type="password" id="new-password" name="newPassword" placeholder="Nhập mật khẩu mới">
                                                <span class="toggle-password" data-target="new-password"><i class="fas fa-eye"></i></span>
                                            </div>
                                            <div class="password-strength">
                                                <div class="strength-meter">
                                                    <div class="strength-meter-fill" data-strength="0"></div>
                                                </div>
                                                <div class="strength-text">Độ mạnh: Yếu</div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="confirm-password">Xác nhận mật khẩu mới</label>
                                            <div class="password-input">
                                                <input type="password" id="confirm-password" name="confirmPassword" placeholder="Nhập lại mật khẩu mới">
                                                <span class="toggle-password" data-target="confirm-password"><i class="fas fa-eye"></i></span>
                                            </div>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" class="btn btn-primary">Đổi mật khẩu</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="security-card">
                                <div class="security-card-header">
                                    <h3>Bảo mật tài khoản</h3>
                                    <p>Các tùy chọn bảo mật bổ sung để bảo vệ tài khoản của bạn.</p>
                                </div>
                                <div class="security-card-body">
                                    <div class="security-option">
                                        <div class="security-option-info">
                                            <h4>Khóa tài khoản</h4>
                                            <p>Khóa tài khoản của bạn nếu bạn nghi ngờ có hoạt động đáng ngờ.</p>
                                        </div>
                                        <div class="security-option-action">
                                            <button class="btn btn-danger" id="lock-account-btn">Khóa tài khoản</button>
                                        </div>
                                    </div>

                                    <div class="security-option">
                                        <div class="security-option-info">
                                            <h4>Đăng xuất khỏi tất cả thiết bị</h4>
                                            <p>Đăng xuất khỏi tất cả các thiết bị đã đăng nhập vào tài khoản của bạn.</p>
                                        </div>
                                        <div class="security-option-action">
                                            <button class="btn btn-warning" id="logout-all-btn">Đăng xuất tất cả</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Login History Section -->
                    <div class="account-section" id="login-history-section" style="display: none;">
                        <h2>Nhật ký đăng nhập</h2>
                        <p>Xem lịch sử đăng nhập gần đây của bạn.</p>

                        <div class="login-history-content">
                            <div class="login-history-filters">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="history-from">Từ ngày</label>
                                        <input type="date" id="history-from" name="from">
                                    </div>
                                    <div class="form-group">
                                        <label for="history-to">Đến ngày</label>
                                        <input type="date" id="history-to" name="to">
                                    </div>
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button class="btn btn-primary" id="filter-history-btn">Lọc</button>
                                    </div>
                                </div>
                            </div>

                            <div class="login-history-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Thời gian</th>
                                            <th>Thiết bị</th>
                                            <th>Trình duyệt</th>
                                            <th>Địa chỉ IP</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                    </thead>
                                    <tbody id="login-history-body">
                                        <!-- Login history items will be added here dynamically -->
                                        <tr>
                                            <td colspan="5" class="empty-history">Không có dữ liệu đăng nhập</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!-- Notification Container -->
    <div class="notification-container"></div>

    <div id="footerbox"></div>

    <!-- Chatbot Liên hệ -->
    <div id="chat-bot">
        <div class="chat-header">💬 Liên hệ với chúng tôi <span id="chat-close">×</span></div>
        <div class="chat-body">
          <div class="chat-message bot">👋 Xin chào! Bạn cần hỗ trợ gì?</div>
          <input type="text" id="chat-input" placeholder="Nhập tin nhắn..." />
        </div>
    </div>

    <!-- Nút mở chat -->
    <div id="chat-toggle"><i class="fas fa-comments"></i></div>

    <!-- Wheel Spin Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
          <span class="close-spin">&times;</span>
          <h2>🎉 Quay vòng may mắn để nhận mã giảm giá!</h2>
          <canvas id="wheel" width="300" height="300"></canvas>
          <button id="spin-btn">Quay ngay</button>
          <div id="spin-result"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/account.js"></script>
    <script src="../script/wishlist-handler.js"></script>
    <script src="../script/popup.js"></script>
    <script src="../script/lucky-wheel.js"></script>
    <script src="../script/chatbot.js"></script>
    <script src="../script/voice-search.js"></script>
    <script src="../script/dark-mode.js"></script>
    <script src="../script/user-display.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/marquee.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Khởi tạo slider sản phẩm
        $(document).ready(function(){
            $('.product-slider').slick({
                slidesToShow: 3,
                slidesToScroll: 1,
                autoplay: true,
                autoplaySpeed: 3000,
                dots: true,
                arrows: true,
                responsive: [
                    {
                        breakpoint: 992,
                        settings: {
                            slidesToShow: 2
                        }
                    },
                    {
                        breakpoint: 576,
                        settings: {
                            slidesToShow: 1
                        }
                    }
                ]
            });

            // Xử lý chatbot
            const chatToggle = document.getElementById('chat-toggle');
            const chatBot = document.getElementById('chat-bot');
            const chatClose = document.getElementById('chat-close');
            const chatInput = document.getElementById('chat-input');
            const chatBody = document.querySelector('.chat-body');

            if (chatToggle && chatBot && chatClose) {
                chatToggle.addEventListener('click', () => {
                    chatBot.style.display = 'flex';
                    chatToggle.style.display = 'none';
                });

                chatClose.addEventListener('click', () => {
                    chatBot.style.display = 'none';
                    chatToggle.style.display = 'block';
                });

                if (chatInput && chatBody) {
                    chatInput.addEventListener('keypress', function (e) {
                        if (e.key === 'Enter' && chatInput.value.trim() !== '') {
                            const userMsg = document.createElement('div');
                            userMsg.className = 'chat-message';
                            userMsg.textContent = chatInput.value;
                            chatBody.insertBefore(userMsg, chatInput);

                            // Giả lập phản hồi
                            const botReply = document.createElement('div');
                            botReply.className = 'chat-message bot';
                            botReply.textContent = 'Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm!';
                            setTimeout(() => {
                                chatBody.insertBefore(botReply, chatInput);
                            }, 1000);

                            chatInput.value = '';
                        }
                    });
                }
            }

            // Hiển thị popup quay số may mắn sau 5 giây
            setTimeout(function() {
                document.querySelector('.spin-popup').classList.remove('hide-spin');
            }, 5000);

            // Đóng popup quay số
            document.querySelector('.close-spin').addEventListener('click', function() {
                document.querySelector('.spin-popup').classList.add('hide-spin');
            });
        });
    </script>

</body>
</html>
