/* Admin Panel Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --text-light: #6c757d;
    --border-color: #ddd;
    --bg-light: #f8f9fa;
    --bg-dark: #343a40;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 60px;
    --transition-speed: 0.3s;
}

/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: var(--text-color);
    line-height: 1.6;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
    width: var(--sidebar-width);
    background-color: var(--primary-color);
    color: white;
    position: fixed;
    height: 100vh;
    transition: width var(--transition-speed) ease;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}

.sidebar-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
}

.admin-sidebar.collapsed .sidebar-header h2 {
    display: none;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item a i {
    font-size: 1.1rem;
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.admin-sidebar.collapsed .nav-item a span {
    display: none;
}

.nav-item a:hover, .nav-item.active a {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: var(--accent-color);
}

.sidebar-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-back-to-site, .btn-logout {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    background-color: transparent;
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-back-to-site i, .btn-logout i {
    margin-right: 10px;
}

.btn-back-to-site:hover, .btn-logout:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.admin-sidebar.collapsed .btn-back-to-site span, 
.admin-sidebar.collapsed .btn-logout span {
    display: none;
}

/* Main Content Styles */
.admin-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-speed) ease;
}

.admin-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Bar Styles */
.admin-topbar {
    height: var(--topbar-height);
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 99;
}

.topbar-left {
    display: flex;
    align-items: center;
}

.btn-toggle-sidebar {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    margin-right: 15px;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: var(--bg-light);
    border-radius: 5px;
    padding: 5px 10px;
}

.search-box input {
    border: none;
    background: none;
    padding: 5px;
    width: 200px;
    outline: none;
}

.search-box button {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
}

.topbar-right {
    display: flex;
    align-items: center;
}

.notifications {
    position: relative;
    margin-right: 20px;
}

.btn-notification {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.1rem;
    cursor: pointer;
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 100;
}

.notifications:hover .notification-dropdown {
    display: block;
}

.notification-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h3 {
    font-size: 1rem;
    font-weight: 600;
}

.btn-mark-all-read {
    background: none;
    border: none;
    color: var(--accent-color);
    font-size: 0.8rem;
    cursor: pointer;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 15px;
    display: flex;
    align-items: flex-start;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: var(--bg-light);
}

.notification-item.unread {
    background-color: rgba(231, 76, 60, 0.05);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.notification-icon.order {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.notification-icon.user {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.notification-icon.alert {
    background-color: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.notification-content p {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.notification-time {
    font-size: 0.8rem;
    color: var(--text-light);
}

.notification-footer {
    padding: 15px;
    text-align: center;
}

.notification-footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.admin-profile {
    display: flex;
    align-items: center;
    position: relative;
}

.admin-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}

.admin-info {
    display: flex;
    flex-direction: column;
}

.admin-name {
    font-size: 0.9rem;
    font-weight: 600;
}

.admin-role {
    font-size: 0.8rem;
    color: var(--text-light);
}

.btn-profile-dropdown {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    margin-left: 5px;
}

.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 100;
}

.admin-profile:hover .profile-dropdown {
    display: block;
}

.profile-dropdown a {
    display: block;
    padding: 10px 15px;
    color: var(--text-color);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.profile-dropdown a:hover {
    background-color: var(--bg-light);
}

.profile-dropdown a i {
    margin-right: 10px;
    width: 16px;
}

/* Admin Sections Styles */
.admin-sections {
    padding: 20px;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.date-filter {
    display: flex;
    align-items: center;
}

.date-filter label {
    margin-right: 10px;
    font-size: 0.9rem;
}

.date-filter select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: white;
    font-size: 0.9rem;
}

/* Dashboard Stats Styles */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.sales {
    background-color: #3498db;
}

.stat-icon.orders {
    background-color: #2ecc71;
}

.stat-icon.users {
    background-color: #9b59b6;
}

.stat-icon.visitors {
    background-color: #f39c12;
}

.stat-content h3 {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
}

.stat-change.positive {
    color: #2ecc71;
}

.stat-change.negative {
    color: #e74c3c;
}

.stat-change span {
    color: var(--text-light);
    margin-left: 5px;
}

/* More styles will be added in separate files */
