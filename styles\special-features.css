/* Special Features Section Styles */

.special-features {
    padding: 60px 0;
    background-color: #f8f9fa;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(51, 154, 240, 0.1) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background-color: #ff6b6b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px rgba(255, 107, 107, 0.2);
}

.feature-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.feature-content h3 {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

.feature-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background-color: #ff6b6b;
    transition: width 0.3s ease;
}

.feature-card:hover .feature-content h3::after {
    width: 60px;
}

.feature-content p {
    color: #868e96;
    margin-bottom: 20px;
    line-height: 1.6;
    flex: 1;
}

.btn-feature {
    display: inline-block;
    padding: 10px 25px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid #ff6b6b;
}

.btn-feature:hover {
    background-color: transparent;
    color: #ff6b6b;
}

/* Feature Card Variations */
.feature-card:nth-child(2) .feature-icon {
    background-color: #339af0;
}

.feature-card:nth-child(2) .feature-content h3::after {
    background-color: #339af0;
}

.feature-card:nth-child(2) .btn-feature {
    background-color: #339af0;
    border-color: #339af0;
}

.feature-card:nth-child(2) .btn-feature:hover {
    background-color: transparent;
    color: #339af0;
}

.feature-card:nth-child(3) .feature-icon {
    background-color: #40c057;
}

.feature-card:nth-child(3) .feature-content h3::after {
    background-color: #40c057;
}

.feature-card:nth-child(3) .btn-feature {
    background-color: #40c057;
    border-color: #40c057;
}

.feature-card:nth-child(3) .btn-feature:hover {
    background-color: transparent;
    color: #40c057;
}

/* Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.feature-card:hover .feature-icon {
    animation: pulse 1.5s infinite;
}

/* Responsive */
@media (max-width: 992px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .feature-card {
        max-width: 400px;
        margin: 0 auto;
    }
}
