/* Notification Styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(120%);
    opacity: 0;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
}

.notification.success::before {
    background-color: #4CAF50;
}

.notification.error::before {
    background-color: #F44336;
}

.notification.warning::before {
    background-color: #FF9800;
}

.notification.info::before {
    background-color: #2196F3;
}

.notification-icon {
    font-size: 20px;
}

.notification.success .notification-icon {
    color: #4CAF50;
}

.notification.error .notification-icon {
    color: #F44336;
}

.notification.warning .notification-icon {
    color: #FF9800;
}

.notification.info .notification-icon {
    color: #2196F3;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    color: #333;
}

/* Responsive Styles */
@media (max-width: 576px) {
    .notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
