// AI Stylist JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const aiPreview = document.querySelector('.ai-preview');
    const aiPreviewImage = document.querySelector('.ai-preview-image');
    const aiPreviewPlaceholder = document.querySelector('.ai-preview-placeholder');
    const aiLoading = document.querySelector('.ai-loading');
    const generateOutfitBtn = document.getElementById('generate-outfit');
    const resetFormBtn = document.getElementById('reset-form');
    const aiResults = document.querySelector('.ai-results');

    // Form elements
    const occasionSelect = document.getElementById('occasion');
    const styleSelect = document.getElementById('style');
    const seasonSelect = document.getElementById('season');
    const colorPreferenceInput = document.getElementById('color-preference');
    const additionalInfoInput = document.getElementById('additional-info');

    // Sample outfit data
    const outfitData = [
        {
            title: "Casual Chic Ensemble",
            description: "A perfect blend of comfort and style for everyday wear. This outfit combines classic pieces with modern touches for a timeless look that's suitable for various casual occasions.",
            items: [
                {
                    name: "Relaxed Fit T-Shirt",
                    price: "250.000đ",
                    image: "./img womens/women 7.webp",
                    link: "./womenproducts.html"
                },
                {
                    name: "High-Waisted Jeans",
                    price: "420.000đ",
                    image: "./img womens/women 8.webp",
                    link: "./womenproducts.html"
                },
                {
                    name: "Canvas Sneakers",
                    price: "350.000đ",
                    image: "./img womens/women 11.webp",
                    link: "./womenproducts.html"
                }
            ]
        },
        {
            title: "Office Elegance",
            description: "A sophisticated and professional outfit perfect for the workplace. This combination strikes the right balance between formal and comfortable, ensuring you look polished throughout the day.",
            items: [
                {
                    name: "Tailored Blazer",
                    price: "650.000đ",
                    image: "./img womens/women 9.webp",
                    link: "./womenproducts.html"
                },
                {
                    name: "Slim Fit Trousers",
                    price: "480.000đ",
                    image: "./img womens/women 12.webp",
                    link: "./womenproducts.html"
                },
                {
                    name: "Leather Loafers",
                    price: "520.000đ",
                    image: "./img womens/women 13.webp",
                    link: "./womenproducts.html"
                }
            ]
        },
        {
            title: "Evening Glamour",
            description: "A stunning outfit for special evening events. This ensemble combines elegance and glamour, ensuring you make a memorable impression at any formal gathering or celebration.",
            items: [
                {
                    name: "Cocktail Dress",
                    price: "750.000đ",
                    image: "./img womens/women 14.webp",
                    link: "./womenproducts.html"
                },
                {
                    name: "Statement Earrings",
                    price: "280.000đ",
                    image: "./img womens/women 15.webp",
                    link: "./womenproducts.html"
                },
                {
                    name: "Strappy Heels",
                    price: "480.000đ",
                    image: "./img womens/women 16.webp",
                    link: "./womenproducts.html"
                }
            ]
        }
    ];

    // Initialize
    function init() {
        // Hide preview image and results initially
        if (aiPreviewImage) aiPreviewImage.style.display = 'none';
        if (aiPreviewPlaceholder) aiPreviewPlaceholder.style.display = 'flex';
        if (aiResults) aiResults.classList.remove('active');
    }

    // Generate outfit
    function generateOutfit() {
        // Show loading
        if (aiLoading) aiLoading.classList.add('active');

        // Get form values
        const occasion = occasionSelect ? occasionSelect.value : '';
        const style = styleSelect ? styleSelect.value : '';
        const season = seasonSelect ? seasonSelect.value : '';
        const colorPreference = colorPreferenceInput ? colorPreferenceInput.value : '';
        const additionalInfo = additionalInfoInput ? additionalInfoInput.value : '';

        // Validate form
        if (!occasion || !style || !season) {
            showNotification('Vui lòng chọn dịp, phong cách và mùa', 'error');
            if (aiLoading) aiLoading.classList.remove('active');
            return;
        }

        // Simulate AI processing
        setTimeout(function() {
            // Hide loading
            if (aiLoading) aiLoading.classList.remove('active');

            // Show preview image
            if (aiPreviewImage) {
                aiPreviewImage.src = getOutfitImage(occasion, style, season);
                aiPreviewImage.style.display = 'block';
            }
            if (aiPreviewPlaceholder) aiPreviewPlaceholder.style.display = 'none';

            // Show results
            if (aiResults) {
                aiResults.classList.add('active');
                displayOutfits();
            }

            // Show notification
            showNotification('Đã tạo trang phục phù hợp cho bạn', 'success');
        }, 2000);
    }

    // Get outfit image based on selections
    function getOutfitImage(occasion, style, season) {
        // In a real implementation, this would call an AI model
        // For demo purposes, return a static image based on women's products
        if (occasion === 'casual') {
            return './img womens/women 6.webp';
        } else if (occasion === 'formal') {
            return './img womens/women 9.webp';
        } else if (occasion === 'party') {
            return './img womens/women 14.webp';
        } else {
            return './img womens/women 25.webp';
        }
    }

    // Display outfits
    function displayOutfits() {
        const resultsContainer = document.querySelector('.ai-results-container');

        if (!resultsContainer) return;

        // Clear previous results
        resultsContainer.innerHTML = '';

        // Add outfits
        outfitData.forEach(outfit => {
            const outfitElement = document.createElement('div');
            outfitElement.className = 'ai-outfit';

            let itemsHTML = '';
            outfit.items.forEach(item => {
                itemsHTML += `
                    <div class="ai-outfit-item">
                        <img src="${item.image}" alt="${item.name}" class="ai-outfit-item-image">
                        <div class="ai-outfit-item-info">
                            <h4 class="ai-outfit-item-name">${item.name}</h4>
                            <p class="ai-outfit-item-price">${item.price}</p>
                        </div>
                        <a href="${item.link}" class="ai-outfit-item-link">Xem chi tiết</a>
                    </div>
                `;
            });

            outfitElement.innerHTML = `
                <div class="ai-outfit-header">
                    <h3 class="ai-outfit-title">${outfit.title}</h3>
                    <div class="ai-outfit-actions">
                        <button class="ai-outfit-btn btn-save" title="Lưu trang phục"><i class="far fa-heart"></i></button>
                        <button class="ai-outfit-btn btn-share" title="Chia sẻ"><i class="fas fa-share-alt"></i></button>
                    </div>
                </div>
                <p class="ai-outfit-description">${outfit.description}</p>
                <div class="ai-outfit-items">
                    ${itemsHTML}
                </div>
            `;

            resultsContainer.appendChild(outfitElement);
        });

        // Add event listeners to outfit buttons
        const saveButtons = document.querySelectorAll('.btn-save');
        const shareButtons = document.querySelectorAll('.btn-share');

        saveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    icon.style.color = '#e74c3c';
                    showNotification('Đã lưu trang phục vào danh sách yêu thích', 'success');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    icon.style.color = '';
                    showNotification('Đã xóa trang phục khỏi danh sách yêu thích', 'info');
                }
            });
        });

        shareButtons.forEach(button => {
            button.addEventListener('click', function() {
                showNotification('Chức năng chia sẻ đang được phát triển', 'info');
            });
        });
    }

    // Reset form
    function resetForm() {
        // Reset form fields
        if (occasionSelect) occasionSelect.selectedIndex = 0;
        if (styleSelect) styleSelect.selectedIndex = 0;
        if (seasonSelect) seasonSelect.selectedIndex = 0;
        if (colorPreferenceInput) colorPreferenceInput.value = '';
        if (additionalInfoInput) additionalInfoInput.value = '';

        // Hide preview image and results
        if (aiPreviewImage) aiPreviewImage.style.display = 'none';
        if (aiPreviewPlaceholder) aiPreviewPlaceholder.style.display = 'flex';
        if (aiResults) aiResults.classList.remove('active');

        // Show notification
        showNotification('Đã đặt lại biểu mẫu', 'info');
    }

    // Event listeners
    if (generateOutfitBtn) {
        generateOutfitBtn.addEventListener('click', generateOutfit);
    }

    if (resetFormBtn) {
        resetFormBtn.addEventListener('click', resetForm);
    }

    // Initialize
    init();
});
