/* AR Virtual Try-On Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

.ar-try-on-section {
    padding: 80px 0;
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.ar-try-on-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/ar-pattern.png');
    opacity: 0.05;
    z-index: 0;
}

.ar-container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.ar-header {
    text-align: center;
    margin-bottom: 50px;
}

.ar-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.ar-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

.ar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
}

.ar-preview {
    flex: 1;
    position: relative;
    height: 500px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.ar-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ar-preview:hover .ar-overlay {
    opacity: 1;
}

.ar-controls {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.ar-control-group {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: var(--box-shadow);
}

.ar-control-group h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.ar-control-group h3 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.ar-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.ar-option {
    width: calc(33.333% - 10px);
    aspect-ratio: 1/1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ar-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.ar-option.active {
    border: 2px solid var(--accent-color);
}

.ar-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ar-option-label {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.8rem;
    text-align: center;
}

.ar-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.ar-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.ar-btn i {
    margin-right: 8px;
}

.ar-btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.ar-btn-primary:hover {
    background-color: var(--accent-hover);
}

.ar-btn-secondary {
    background-color: var(--primary-color);
    color: white;
}

.ar-btn-secondary:hover {
    background-color: var(--primary-dark);
}

.ar-btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.ar-btn-outline:hover {
    background-color: var(--hover-color);
}

.ar-info {
    margin-top: 30px;
    display: flex;
    gap: 20px;
}

.ar-info-item {
    flex: 1;
    display: flex;
    align-items: center;
}

.ar-info-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--accent-color);
    font-size: 1.3rem;
}

.ar-info-content h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: var(--text-color);
}

.ar-info-content p {
    font-size: 0.9rem;
    color: var(--text-light);
}

.ar-camera-feed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: none;
}

.ar-camera-feed.active {
    display: block;
}

.ar-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    display: none;
}

.ar-loading.active {
    display: flex;
}

.ar-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--bg-light);
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.ar-loading-text {
    font-size: 1rem;
    color: var(--text-color);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive */
@media (max-width: 992px) {
    .ar-content {
        flex-direction: column;
    }
    
    .ar-preview {
        width: 100%;
        height: 400px;
    }
    
    .ar-controls {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .ar-option {
        width: calc(50% - 7.5px);
    }
    
    .ar-info {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .ar-header h2 {
        font-size: 2rem;
    }
    
    .ar-preview {
        height: 350px;
    }
    
    .ar-actions {
        flex-direction: column;
    }
}
