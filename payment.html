<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/payment.css">

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />

    <title>Thanh toán | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <ul class="breadcrumb">
                <li><a href="./index.html">Trang chủ</a></li>
                <li><a href="./AddCart.html">Giỏ hàng</a></li>
                <li><a href="./checkout.html">Thanh toán</a></li>
                <li class="active">Phương thức thanh toán</li>
            </ul>
        </div>
    </div>

    <!-- Payment Section -->
    <section class="payment-section">
        <div class="container">
            <div class="payment-wrapper">
                <div class="payment-methods">
                    <h2 class="section-title">Phương thức thanh toán</h2>

                    <div class="payment-options">
                        <div class="payment-option active" data-method="card">
                            <div class="option-icon"><i class="fas fa-credit-card"></i></div>
                            <div class="option-text">Thẻ tín dụng/ghi nợ</div>
                        </div>
                        <div class="payment-option" data-method="banking">
                            <div class="option-icon"><i class="fas fa-university"></i></div>
                            <div class="option-text">Chuyển khoản ngân hàng</div>
                        </div>
                        <div class="payment-option" data-method="ewallet">
                            <div class="option-icon"><i class="fas fa-wallet"></i></div>
                            <div class="option-text">Ví điện tử</div>
                        </div>
                        <div class="payment-option" data-method="cod">
                            <div class="option-icon"><i class="fas fa-money-bill-wave"></i></div>
                            <div class="option-text">Thanh toán khi nhận hàng</div>
                        </div>
                    </div>

                    <!-- Credit Card Form -->
                    <div class="payment-form card-form active" id="card-payment">
                        <div class="card-container">
                            <div class="card-preview">
                                <div class="card-front">
                                    <div class="card-type">
                                        <img src="./img cards/Visa.webp" alt="Visa" class="card-logo visa active">
                                        <img src="./img cards/MasterCard_Logo.svg.png" alt="Mastercard" class="card-logo mastercard">
                                        <img src="./img cards/JCB_logo.svg.png" alt="JCB" class="card-logo jcb">
                                    </div>
                                    <div class="card-number">
                                        <span class="number-group">XXXX</span>
                                        <span class="number-group">XXXX</span>
                                        <span class="number-group">XXXX</span>
                                        <span class="number-group">XXXX</span>
                                    </div>
                                    <div class="card-details">
                                        <div class="card-holder">
                                            <label>Chủ thẻ</label>
                                            <div class="value">TÊN CHỦ THẺ</div>
                                        </div>
                                        <div class="card-expires">
                                            <label>Hết hạn</label>
                                            <div class="value">MM/YY</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-back">
                                    <div class="card-stripe"></div>
                                    <div class="card-cvv">
                                        <label>CVV</label>
                                        <div class="value">XXX</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form id="credit-card-form">
                            <div class="form-group">
                                <label for="card-number">Số thẻ</label>
                                <div class="input-with-icon">
                                    <input type="text" id="card-number" placeholder="XXXX XXXX XXXX XXXX" maxlength="19" required>
                                    <i class="fas fa-credit-card"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="card-holder">Tên chủ thẻ</label>
                                <div class="input-with-icon">
                                    <input type="text" id="card-holder" placeholder="Tên trên thẻ" required>
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group half">
                                    <label for="expiry-date">Ngày hết hạn</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="expiry-date" placeholder="MM/YY" maxlength="5" required>
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                </div>

                                <div class="form-group half">
                                    <label for="cvv">Mã bảo mật (CVV)</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="cvv" placeholder="XXX" maxlength="3" required>
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="save-card">
                                    <span class="checkmark"></span>
                                    Lưu thông tin thẻ cho lần sau
                                </label>
                            </div>

                            <div class="secure-payment">
                                <i class="fas fa-lock"></i> Thanh toán an toàn với mã hóa SSL
                            </div>

                            <div class="accepted-cards">
                                <img src="./img cards/Visa.webp" alt="Visa">
                                <img src="./img cards/MasterCard_Logo.svg.png" alt="Mastercard">
                                <img src="./img cards/JCB_logo.svg.png" alt="JCB">
                                <img src="./img cards/American_Express_logo_(2018).svg.png" alt="American Express">
                            </div>
                        </form>
                    </div>

                    <!-- Bank Transfer Form -->
                    <div class="payment-form" id="banking-payment">
                        <div class="bank-info">
                            <h3>Thông tin chuyển khoản</h3>
                            <p>Vui lòng chuyển khoản đến tài khoản ngân hàng dưới đây:</p>

                            <div class="bank-details">
                                <div class="bank-detail-item">
                                    <span class="label">Ngân hàng:</span>
                                    <span class="value">Vietcombank</span>
                                </div>
                                <div class="bank-detail-item">
                                    <span class="label">Số tài khoản:</span>
                                    <span class="value">**********</span>
                                    <button class="copy-btn" data-clipboard-text="**********"><i class="fas fa-copy"></i></button>
                                </div>
                                <div class="bank-detail-item">
                                    <span class="label">Chủ tài khoản:</span>
                                    <span class="value">CÔNG TY TNHH FASHION STORE</span>
                                </div>
                                <div class="bank-detail-item">
                                    <span class="label">Nội dung chuyển khoản:</span>
                                    <span class="value">FS12345</span>
                                    <button class="copy-btn" data-clipboard-text="FS12345"><i class="fas fa-copy"></i></button>
                                </div>
                            </div>

                            <div class="bank-note">
                                <p><strong>Lưu ý:</strong> Vui lòng ghi đúng nội dung chuyển khoản để chúng tôi có thể xác nhận thanh toán của bạn. Đơn hàng sẽ được xử lý sau khi chúng tôi nhận được thanh toán.</p>
                            </div>
                        </div>
                    </div>

                    <!-- E-wallet Form -->
                    <div class="payment-form" id="ewallet-payment">
                        <div class="ewallet-options">
                            <h3>Chọn ví điện tử</h3>

                            <div class="ewallet-list">
                                <div class="ewallet-item active">
                                    <img src="./img cards/MoMo_Logo.png" alt="MoMo">
                                    <span>MoMo</span>
                                </div>
                                <div class="ewallet-item">
                                    <img src="/img cards/images.png" alt="ZaloPay">
                                    <span>ZaloPay</span>
                                </div>
                                <div class="ewallet-item">
                                    <img src="./img cards/vnpay-logo-vinadesign-25-12-57-55.jpg" alt="VNPay">
                                    <span>VNPay</span>
                                </div>
                                <div class="ewallet-item">
                                    <img src="./img cards/shopee-pay-logo-png_seeklogo-406839.png" alt="ShopeePay">
                                    <span>ShopeePay</span>
                                </div>
                            </div>

                            <div class="qr-container">
                                <div class="qr-code">
                                    <img src="./img/qr-code.png" alt="QR Code">
                                </div>
                                <div class="qr-instructions">
                                    <p>1. Mở ứng dụng MoMo trên điện thoại</p>
                                    <p>2. Quét mã QR</p>
                                    <p>3. Xác nhận thanh toán</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- COD Form -->
                    <div class="payment-form" id="cod-payment">
                        <div class="cod-info">
                            <h3>Thanh toán khi nhận hàng</h3>
                            <p>Bạn sẽ thanh toán bằng tiền mặt khi nhận được hàng.</p>

                            <div class="cod-note">
                                <p><strong>Lưu ý:</strong></p>
                                <ul>
                                    <li>Vui lòng kiểm tra hàng trước khi thanh toán</li>
                                    <li>Chuẩn bị đúng số tiền để thanh toán</li>
                                    <li>Phí vận chuyển sẽ được tính vào tổng số tiền thanh toán</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="order-summary">
                    <h2 class="summary-title">Tóm tắt đơn hàng</h2>

                    <div class="summary-items">
                        <div class="summary-item">
                            <span class="item-name">Tổng tiền hàng</span>
                            <span class="item-value" id="subtotal">0đ</span>
                        </div>
                        <div class="summary-item">
                            <span class="item-name">Phí vận chuyển</span>
                            <span class="item-value" id="shipping">Miễn phí</span>
                        </div>
                        <div class="summary-item discount">
                            <span class="item-name">Giảm giá</span>
                            <span class="item-value" id="discount">0đ</span>
                        </div>
                        <div class="summary-total">
                            <span class="total-label">Tổng thanh toán</span>
                            <span class="total-value" id="total">0đ</span>
                        </div>
                    </div>

                    <button type="button" class="btn-complete-payment">Hoàn tất thanh toán</button>

                    <div class="back-to-cart">
                        <a href="./checkout.html"><i class="fas fa-arrow-left"></i> Quay lại thông tin giao hàng</a>
                    </div>

                    <div class="payment-security">
                        <h4>Thanh toán an toàn</h4>
                        <div class="security-icons">
                            <img src="./img cards/Visa.webp" alt="Visa">
                            <img src="./img cards/MasterCard_Logo.svg.png" alt="Mastercard">
                            <img src="./img cards/JCB_logo.svg.png" alt="JCB">
                            <img src="./img cards/American_Express_logo_(2018).svg.png" alt="American Express">
                            <img src="./img cards/paypal-logo-png_seeklogo-390894.png" alt="PayPal">
                        </div>
                        <p><i class="fas fa-lock"></i> Thông tin thanh toán của bạn được bảo mật 100%</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Chatbot Liên hệ -->
    <div id="chat-bot">
        <div class="chat-header">💬 Liên hệ với chúng tôi <span id="chat-close">×</span></div>
        <div class="chat-body">
          <div class="chat-message bot">👋 Xin chào! Bạn cần hỗ trợ gì?</div>
          <input type="text" id="chat-input" placeholder="Nhập tin nhắn..." />
        </div>
    </div>

    <!-- Nút mở chat -->
    <div id="chat-toggle"><i class="fas fa-comments"></i></div>

    <!-- Wheel Spin Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
          <span class="close-spin">&times;</span>
          <h2>🎉 Quay vòng may mắn để nhận mã giảm giá!</h2>
          <canvas id="wheel" width="300" height="300"></canvas>
          <button id="spin-btn">Quay ngay</button>
          <div id="spin-result"></div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/payment.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/popup.js"></script>
    <script src="./script/lucky-wheel.js"></script>
    <script src="./script/chatbot.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>

    <script>
        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Thêm class fade-in cho các phần tử cần animation
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.classList.add('fade-in');
            });
        });

        // Xử lý chatbot
        const chatToggle = document.getElementById('chat-toggle');
        const chatBot = document.getElementById('chat-bot');
        const chatClose = document.getElementById('chat-close');
        const chatInput = document.getElementById('chat-input');
        const chatBody = document.querySelector('.chat-body');

        if (chatToggle && chatBot && chatClose) {
            chatToggle.addEventListener('click', () => {
                chatBot.style.display = 'flex';
                chatToggle.style.display = 'none';
            });

            chatClose.addEventListener('click', () => {
                chatBot.style.display = 'none';
                chatToggle.style.display = 'block';
            });

            if (chatInput && chatBody) {
                chatInput.addEventListener('keypress', function (e) {
                    if (e.key === 'Enter' && chatInput.value.trim() !== '') {
                        const userMsg = document.createElement('div');
                        userMsg.className = 'chat-message';
                        userMsg.textContent = chatInput.value;
                        chatBody.insertBefore(userMsg, chatInput);

                        // Giả lập phản hồi
                        const botReply = document.createElement('div');
                        botReply.className = 'chat-message bot';
                        botReply.textContent = 'Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm!';
                        setTimeout(() => {
                            chatBody.insertBefore(botReply, chatInput);
                        }, 1000);

                        chatInput.value = '';
                    }
                });
            }
        }
    </script>
</body>
</html>
