const bcrypt = require('bcryptjs');

// Pre-hashed passwords for demo users (password: 'password123')
const hashedPassword = '$2a$10$X7.H/XqV.e.jJzZ0UPYi5O8xSwHIY9UXgZOK0A2r2VUHxDy3IvZGe';

const users = [
  {
    id: 1,
    username: 'user1',
    email: '<EMAIL>',
    password: hashedPassword,
    fullName: '<PERSON>uyễn Văn A',
    phone: '0901234567',
    address: '123 Đường Nguyễn Trãi, Quận 1, TP. <PERSON><PERSON>',
    gender: 'male',
    birthDate: '1990-01-15',
    avatar: '../img/avatars/avatar1.jpg',
    role: 'user',
    createdAt: '2023-01-15T08:30:00.000Z',
    lastLogin: '2023-06-20T14:25:00.000Z'
  },
  {
    id: 2,
    username: 'user2',
    email: '<EMAIL>',
    password: hashedPassword,
    fullName: '<PERSON><PERSON><PERSON><PERSON>',
    phone: '0912345678',
    address: '456 <PERSON><PERSON><PERSON>, Quận 3, TP. <PERSON><PERSON> <PERSON><PERSON>',
    gender: 'female',
    birthDate: '1992-05-20',
    avatar: '../img/avatars/avatar2.jpg',
    role: 'user',
    createdAt: '2023-02-10T10:15:00.000Z',
    lastLogin: '2023-06-18T09:45:00.000Z'
  },
  {
    id: 3,
    username: 'admin',
    email: '<EMAIL>',
    password: hashedPassword,
    fullName: 'Admin User',
    phone: '0987654321',
    address: '789 Đường Lê Duẩn, Quận 1, TP. Hồ Chí Minh',
    gender: 'male',
    birthDate: '1985-12-10',
    avatar: '../img/avatars/admin.jpg',
    role: 'admin',
    createdAt: '2023-01-01T00:00:00.000Z',
    lastLogin: '2023-06-21T08:00:00.000Z'
  }
];

module.exports = users;
