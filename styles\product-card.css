/* Product Card Styles */
.product-card {
    position: relative;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 20px;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay button {
    background-color: #fff;
    color: #333;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 0.9rem;
}

.product-overlay button:hover {
    background-color: #ff6b6b;
    color: #fff;
    transform: translateY(-2px);
}

.product-overlay .btn-shop::before {
    content: '\f07a';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.9rem;
}

.product-overlay .btn-quick-view::before {
    content: '\f06e';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.9rem;
}

.product-info {
    padding: 15px;
}

.product-name {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
    transition: color 0.2s;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 2.4em;
}

.product-card:hover .product-name {
    color: #ff6b6b;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ff6b6b;
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 3px;
    z-index: 2;
}

.badge-new {
    background-color: #4CAF50;
    color: white;
}

.badge-sale {
    background-color: #ff6b6b;
    color: white;
}

.product-rating {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.rating-stars {
    color: #FFD700;
    font-size: 0.9rem;
}

.rating-count {
    margin-left: 5px;
    font-size: 0.8rem;
    color: #666;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .product-image {
        height: 220px;
    }
}

@media (max-width: 768px) {
    .product-image {
        height: 200px;
    }
    
    .product-overlay button {
        padding: 8px 15px;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .product-image {
        height: 180px;
    }
    
    .product-name {
        font-size: 0.9rem;
    }
    
    .product-price {
        font-size: 1rem;
    }
}
