/* Authentication Pages Styles */
:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}
.auth-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.auth-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
}

.auth-content {
    flex: 1;
    padding: 40px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    font-size: 28px;
    color: #333;
    margin-bottom: 10px;
}

.auth-header p {
    color: #666;
    font-size: 16px;
}

.auth-form {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group .required {
    color: var(--accent-color);
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    border-color: var(--accent-color);
    outline: none;
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

.password-strength {
    margin-top: 10px;
}

.strength-meter {
    height: 5px;
    background-color: #eee;
    border-radius: 3px;
    margin-bottom: 5px;
}

.strength-meter-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s, background-color 0.3s;
}

.strength-meter-fill[data-strength="0"] {
    width: 20%;
    background-color: #ff4d4d;
}

.strength-meter-fill[data-strength="1"] {
    width: 40%;
    background-color: #ffa64d;
}

.strength-meter-fill[data-strength="2"] {
    width: 60%;
    background-color: #ffff4d;
}

.strength-meter-fill[data-strength="3"] {
    width: 80%;
    background-color: #4dff4d;
}

.strength-meter-fill[data-strength="4"] {
    width: 100%;
    background-color: #4d4dff;
}

.strength-text {
    font-size: 12px;
    color: #666;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: var(--accent-color);
    font-size: 14px;
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
}

.btn-block {
    display: block;
    width: 100%;
}

.social-login {
    margin: 30px 0;
    text-align: center;
}

.social-login p {
    position: relative;
    margin-bottom: 20px;
    color: #666;
}

.social-login p::before,
.social-login p::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background-color: #ddd;
}

.social-login p::before {
    left: 0;
}

.social-login p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: 15px;
}

.social-btn {
    flex: 1;
    padding: 12px 15px;
    border-radius: 5px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: opacity 0.3s;
}

.social-btn:hover {
    opacity: 0.9;
}

.social-btn.facebook {
    background-color: #3b5998;
    color: white;
}

.social-btn.google {
    background-color: #db4437;
    color: white;
}

.auth-footer {
    text-align: center;
    margin-top: 20px;
}

.auth-footer p {
    color: #666;
}

.auth-footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.auth-image {
    flex: 1;
    position: relative;
    display: none;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: white;
}

.auth-image-overlay h2 {
    font-size: 28px;
    margin-bottom: 10px;
}

.auth-image-overlay p {
    font-size: 16px;
    opacity: 0.9;
}

/* Username and Password Suggestion Styles */
.username-suggestion,
.password-suggestion {
    margin-top: 10px;
}

.suggestion-text {
    color: #666;
    font-style: italic;
}

.suggest-btn {
    background-color: transparent;
    border: 1px solid #ddd;
    color: #666;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.suggest-btn:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.saved-emails {
    position: absolute;
    width: 100%;
    background-color: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
    max-height: 150px;
    overflow-y: auto;
    z-index: 10;
    display: none;
}

.saved-email-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.saved-email-item:hover {
    background-color: #f5f5f5;
}

/* Recent Activity Styles */
.recent-activity {
    margin-top: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.recent-activity h4 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.activity-list {
    max-height: 150px;
    overflow-y: auto;
}

.activity-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    margin-right: 10px;
    width: 30px;
    height: 30px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-details {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: #333;
    margin-bottom: 3px;
}

.activity-time {
    font-size: 12px;
    color: #999;
}

.no-activity {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

/* Responsive Styles */
@media (min-width: 768px) {
    .auth-image {
        display: block;
    }
}

@media (max-width: 767px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .auth-content {
        padding: 30px 20px;
    }
}
























