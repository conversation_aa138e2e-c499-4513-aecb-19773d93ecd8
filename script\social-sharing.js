// Social Sharing Feature

document.addEventListener('DOMContentLoaded', function() {
    // Initialize social sharing
    initSocialSharing();
    
    // Add social sharing buttons to product cards
    addSharingToProducts();
});

// Initialize social sharing
function initSocialSharing() {
    // Add social sharing styles
    addSocialSharingStyles();
    
    // Add sharing modal
    addSharingModal();
}

// Add social sharing buttons to product cards
function addSharingToProducts() {
    // Get all product cards
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        // Check if sharing button already exists
        if (!card.querySelector('.share-product-btn')) {
            // Get product overlay
            const productOverlay = card.querySelector('.product-overlay');
            
            if (productOverlay) {
                // Create share button
                const shareButton = document.createElement('button');
                shareButton.className = 'share-product-btn';
                shareButton.innerHTML = '<i class="fas fa-share-alt"></i>';
                shareButton.title = 'Chia sẻ sản phẩm';
                
                // Add click event
                shareButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Get product info
                    const productName = card.querySelector('.product-name').textContent;
                    const productPrice = card.querySelector('.product-price').textContent;
                    const productImage = card.querySelector('.product-image img').src;
                    
                    // Show sharing modal
                    showSharingModal(productName, productPrice, productImage);
                });
                
                // Add to overlay
                productOverlay.appendChild(shareButton);
            }
        }
    });
    
    // Add sharing buttons to product detail page
    const productDetailPage = document.querySelector('.product-detail');
    if (productDetailPage && !productDetailPage.querySelector('.product-sharing')) {
        // Get product info
        const productName = productDetailPage.querySelector('.product-title')?.textContent || 'Sản phẩm thời trang';
        const productPrice = productDetailPage.querySelector('.product-price')?.textContent || '';
        const productImage = productDetailPage.querySelector('.product-gallery img')?.src || '';
        
        // Create sharing section
        const sharingSection = document.createElement('div');
        sharingSection.className = 'product-sharing';
        
        sharingSection.innerHTML = `
            <div class="sharing-title">Chia sẻ sản phẩm:</div>
            <div class="sharing-buttons">
                <button class="sharing-btn facebook" data-platform="facebook">
                    <i class="fab fa-facebook-f"></i>
                </button>
                <button class="sharing-btn twitter" data-platform="twitter">
                    <i class="fab fa-twitter"></i>
                </button>
                <button class="sharing-btn pinterest" data-platform="pinterest">
                    <i class="fab fa-pinterest-p"></i>
                </button>
                <button class="sharing-btn telegram" data-platform="telegram">
                    <i class="fab fa-telegram-plane"></i>
                </button>
                <button class="sharing-btn email" data-platform="email">
                    <i class="fas fa-envelope"></i>
                </button>
                <button class="sharing-btn copy-link" data-platform="copy">
                    <i class="fas fa-link"></i>
                </button>
            </div>
        `;
        
        // Add click events to sharing buttons
        sharingSection.querySelectorAll('.sharing-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const platform = this.dataset.platform;
                shareProduct(platform, productName, productPrice, productImage);
            });
        });
        
        // Find where to insert sharing section
        const addToCartBtn = productDetailPage.querySelector('.add-to-cart-btn');
        if (addToCartBtn) {
            // Insert after add to cart button
            addToCartBtn.parentNode.insertBefore(sharingSection, addToCartBtn.nextSibling);
        } else {
            // Append to product info
            const productInfo = productDetailPage.querySelector('.product-info');
            if (productInfo) {
                productInfo.appendChild(sharingSection);
            }
        }
    }
}

// Add sharing modal to page
function addSharingModal() {
    // Check if modal already exists
    if (document.getElementById('sharing-modal')) return;
    
    // Create modal
    const modal = document.createElement('div');
    modal.id = 'sharing-modal';
    modal.className = 'sharing-modal';
    
    modal.innerHTML = `
        <div class="sharing-modal-content">
            <div class="sharing-modal-header">
                <h3>Chia sẻ sản phẩm</h3>
                <button class="sharing-modal-close">&times;</button>
            </div>
            <div class="sharing-modal-body">
                <div class="sharing-product-info">
                    <div class="sharing-product-image">
                        <img src="" alt="Product Image">
                    </div>
                    <div class="sharing-product-details">
                        <h4 class="sharing-product-name"></h4>
                        <p class="sharing-product-price"></p>
                    </div>
                </div>
                <div class="sharing-options">
                    <div class="sharing-option" data-platform="facebook">
                        <div class="sharing-icon facebook">
                            <i class="fab fa-facebook-f"></i>
                        </div>
                        <div class="sharing-platform-name">Facebook</div>
                    </div>
                    <div class="sharing-option" data-platform="twitter">
                        <div class="sharing-icon twitter">
                            <i class="fab fa-twitter"></i>
                        </div>
                        <div class="sharing-platform-name">Twitter</div>
                    </div>
                    <div class="sharing-option" data-platform="pinterest">
                        <div class="sharing-icon pinterest">
                            <i class="fab fa-pinterest-p"></i>
                        </div>
                        <div class="sharing-platform-name">Pinterest</div>
                    </div>
                    <div class="sharing-option" data-platform="telegram">
                        <div class="sharing-icon telegram">
                            <i class="fab fa-telegram-plane"></i>
                        </div>
                        <div class="sharing-platform-name">Telegram</div>
                    </div>
                    <div class="sharing-option" data-platform="email">
                        <div class="sharing-icon email">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="sharing-platform-name">Email</div>
                    </div>
                    <div class="sharing-option" data-platform="copy">
                        <div class="sharing-icon copy-link">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="sharing-platform-name">Sao chép liên kết</div>
                    </div>
                </div>
                <div class="sharing-qr-code">
                    <div class="qr-code-container" id="sharing-qr-code"></div>
                    <p>Quét mã QR để xem sản phẩm</p>
                </div>
            </div>
        </div>
    `;
    
    // Add to body
    document.body.appendChild(modal);
    
    // Add close event
    const closeBtn = modal.querySelector('.sharing-modal-close');
    closeBtn.addEventListener('click', function() {
        modal.classList.remove('show');
    });
    
    // Add click events to sharing options
    const sharingOptions = modal.querySelectorAll('.sharing-option');
    sharingOptions.forEach(option => {
        option.addEventListener('click', function() {
            const platform = this.dataset.platform;
            const productName = modal.querySelector('.sharing-product-name').textContent;
            const productPrice = modal.querySelector('.sharing-product-price').textContent;
            const productImage = modal.querySelector('.sharing-product-image img').src;
            
            shareProduct(platform, productName, productPrice, productImage);
        });
    });
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    });
}

// Show sharing modal
function showSharingModal(productName, productPrice, productImage) {
    const modal = document.getElementById('sharing-modal');
    if (!modal) return;
    
    // Set product info
    modal.querySelector('.sharing-product-name').textContent = productName;
    modal.querySelector('.sharing-product-price').textContent = productPrice;
    modal.querySelector('.sharing-product-image img').src = productImage;
    
    // Generate QR code
    generateQRCode(window.location.href);
    
    // Show modal
    modal.classList.add('show');
}

// Generate QR code
function generateQRCode(url) {
    const qrContainer = document.getElementById('sharing-qr-code');
    if (!qrContainer) return;
    
    // Clear container
    qrContainer.innerHTML = '';
    
    // Create QR code image (in a real app, you would use a QR code library or API)
    const qrImage = document.createElement('img');
    qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(url)}`;
    qrImage.alt = 'QR Code';
    
    // Add to container
    qrContainer.appendChild(qrImage);
}

// Share product
function shareProduct(platform, productName, productPrice, productImage) {
    // Get current URL
    const url = window.location.href;
    
    // Create share text
    const shareText = `Xem sản phẩm "${productName}" với giá ${productPrice} tại Fashion Store`;
    
    // Share based on platform
    switch (platform) {
        case 'facebook':
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
            break;
        case 'twitter':
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(url)}`, '_blank');
            break;
        case 'pinterest':
            window.open(`https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&media=${encodeURIComponent(productImage)}&description=${encodeURIComponent(shareText)}`, '_blank');
            break;
        case 'telegram':
            window.open(`https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(shareText)}`, '_blank');
            break;
        case 'email':
            window.location.href = `mailto:?subject=${encodeURIComponent('Chia sẻ sản phẩm từ Fashion Store')}&body=${encodeURIComponent(shareText + '\n\n' + url)}`;
            break;
        case 'copy':
            // Copy URL to clipboard
            navigator.clipboard.writeText(url).then(() => {
                showNotification('Đã sao chép liên kết vào clipboard', 'success');
            }).catch(err => {
                showNotification('Không thể sao chép liên kết', 'error');
            });
            break;
    }
    
    // Close modal
    const modal = document.getElementById('sharing-modal');
    if (modal) {
        modal.classList.remove('show');
    }
    
    // Show notification
    if (platform !== 'copy') {
        showNotification(`Đã chia sẻ sản phẩm qua ${getPlatformName(platform)}`, 'success');
    }
}

// Get platform name
function getPlatformName(platform) {
    switch (platform) {
        case 'facebook': return 'Facebook';
        case 'twitter': return 'Twitter';
        case 'pinterest': return 'Pinterest';
        case 'telegram': return 'Telegram';
        case 'email': return 'Email';
        case 'copy': return 'Clipboard';
        default: return platform;
    }
}

// Add social sharing styles
function addSocialSharingStyles() {
    // Check if styles already exist
    if (document.getElementById('social-sharing-styles')) return;
    
    // Create style element
    const style = document.createElement('style');
    style.id = 'social-sharing-styles';
    
    // Add CSS
    style.innerHTML = `
        /* Share Button on Product Cards */
        .share-product-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            color: #343a40;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
            opacity: 0;
            transform: translateY(-10px);
        }
        
        .product-card:hover .share-product-btn {
            opacity: 1;
            transform: translateY(0);
        }
        
        .share-product-btn:hover {
            background-color: #ff6b6b;
            color: white;
        }
        
        /* Product Sharing Section */
        .product-sharing {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        .sharing-title {
            font-size: 0.9rem;
            color: #868e96;
            margin-bottom: 10px;
        }
        
        .sharing-buttons {
            display: flex;
            gap: 10px;
        }
        
        .sharing-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }
        
        .sharing-btn:hover {
            transform: translateY(-3px);
        }
        
        .sharing-btn.facebook {
            background-color: #1877f2;
        }
        
        .sharing-btn.twitter {
            background-color: #1da1f2;
        }
        
        .sharing-btn.pinterest {
            background-color: #bd081c;
        }
        
        .sharing-btn.telegram {
            background-color: #0088cc;
        }
        
        .sharing-btn.email {
            background-color: #ea4335;
        }
        
        .sharing-btn.copy-link {
            background-color: #6c757d;
        }
        
        /* Sharing Modal */
        .sharing-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .sharing-modal.show {
            display: flex;
            opacity: 1;
        }
        
        .sharing-modal-content {
            background-color: white;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .sharing-modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .sharing-modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: #343a40;
        }
        
        .sharing-modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #adb5bd;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .sharing-modal-close:hover {
            color: #ff6b6b;
        }
        
        .sharing-modal-body {
            padding: 20px;
        }
        
        .sharing-product-info {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .sharing-product-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .sharing-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .sharing-product-details {
            flex: 1;
        }
        
        .sharing-product-name {
            font-size: 1rem;
            color: #343a40;
            margin: 0 0 5px 0;
        }
        
        .sharing-product-price {
            font-size: 1.1rem;
            color: #ff6b6b;
            font-weight: 600;
            margin: 0;
        }
        
        .sharing-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .sharing-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .sharing-option:hover {
            transform: translateY(-3px);
        }
        
        .sharing-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .sharing-icon.facebook {
            background-color: #1877f2;
        }
        
        .sharing-icon.twitter {
            background-color: #1da1f2;
        }
        
        .sharing-icon.pinterest {
            background-color: #bd081c;
        }
        
        .sharing-icon.telegram {
            background-color: #0088cc;
        }
        
        .sharing-icon.email {
            background-color: #ea4335;
        }
        
        .sharing-icon.copy-link {
            background-color: #6c757d;
        }
        
        .sharing-platform-name {
            font-size: 0.8rem;
            color: #495057;
        }
        
        .sharing-qr-code {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        .qr-code-container {
            width: 150px;
            height: 150px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .qr-code-container img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .sharing-qr-code p {
            font-size: 0.9rem;
            color: #868e96;
            margin: 0;
        }
        
        /* Responsive */
        @media (max-width: 576px) {
            .sharing-options {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    `;
    
    // Add to head
    document.head.appendChild(style);
}

// Initialize on load
document.addEventListener('DOMContentLoaded', initSocialSharing);
