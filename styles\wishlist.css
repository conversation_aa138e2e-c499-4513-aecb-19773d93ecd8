/* Wishlist Page Styles */
@import url('style.css');

/* Wishlist Container */
.wishlist-container {
    padding: 3rem 0;
    background-color: var(--background-color);
}

.wishlist-header {
    text-align: center;
    margin-bottom: 2rem;
}

.wishlist-header h1 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.wishlist-header p {
    color: var(--text-color);
    font-size: 1rem;
}

/* Not Logged In Message */
.not-logged-in {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background-color: #fff;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

.login-message {
    text-align: center;
    max-width: 500px;
}

.login-message h2 {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.login-message p {
    color: var(--text-color);
    margin-bottom: 2rem;
}

.login-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Empty Wishlist */
.empty-wishlist {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background-color: #fff;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

.empty-wishlist-icon {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
}

.empty-wishlist h2 {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.empty-wishlist p {
    color: var(--text-color);
    margin-bottom: 2rem;
    text-align: center;
}

/* Wishlist Content */
.wishlist-content {
    margin-bottom: 3rem;
}

.wishlist-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Wishlist Grid */
.wishlist-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

/* Wishlist Item */
.wishlist-item {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid #f0f0f0;
}

.wishlist-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
    border-color: #e0e0e0;
}

.wishlist-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--accent-color), var(--primary-color));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.wishlist-item:hover::after {
    transform: scaleX(1);
}

.wishlist-item-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.wishlist-item-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.5));
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wishlist-item:hover .wishlist-item-image::before {
    opacity: 1;
}

.wishlist-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.wishlist-item:hover .wishlist-item-image img {
    transform: scale(1.08);
}

.wishlist-item-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 2;
}

.wishlist-item:hover .wishlist-item-actions {
    opacity: 1;
    transform: translateY(0);
}

.btn-add-to-cart {
    background-color: var(--accent-color);
    color: #fff;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.btn-add-to-cart i {
    font-size: 0.8rem;
}

.btn-add-to-cart:hover {
    background-color: var(--accent-hover);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
    transform: translateY(-2px);
}

.btn-remove {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--accent-color);
    border: none;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.btn-remove:hover {
    background-color: var(--accent-color);
    color: #fff;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    transform: rotate(90deg);
}

/* Wishlist Item Info */
.wishlist-item-info {
    padding: 1.5rem;
    position: relative;
}

.wishlist-item-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 3px;
}

.wishlist-item-name {
    font-size: 1.1rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    color: var(--primary-color);
    height: 3em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-weight: 600;
    transition: color 0.3s ease;
}

.wishlist-item:hover .wishlist-item-name {
    color: var(--accent-color);
}

.wishlist-item-price {
    font-weight: 700;
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.wishlist-item-colors {
    display: flex;
    gap: 0.6rem;
    border-top: 1px solid #f0f0f0;
    padding-top: 1rem;
    margin-top: 0.5rem;
}

.color-option {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
}

.color-option:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.color-option.active {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.color-option.active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

/* Color Definitions */
.color-option.black { background-color: #000; }
.color-option.white { background-color: #fff; border: 1px solid #ddd; }
.color-option.red { background-color: #e74c3c; }
.color-option.blue { background-color: #3498db; }
.color-option.green { background-color: #2ecc71; }
.color-option.yellow { background-color: #f1c40f; }
.color-option.purple { background-color: #9b59b6; }
.color-option.orange { background-color: #e67e22; }
.color-option.pink { background-color: #e84393; }
.color-option.brown { background-color: #795548; }
.color-option.gray { background-color: #95a5a6; }

/* Responsive */
@media (max-width: 1200px) {
    .wishlist-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .wishlist-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .wishlist-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .wishlist-grid {
        grid-template-columns: 1fr;
    }
}
