/* Payment Page Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

/* Breadcrumb */
.breadcrumb-container {
    background-color: var(--bg-light);
    padding: 15px 0;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
}

.breadcrumb li {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #6c757d;
}

.breadcrumb li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: #adb5bd;
}

.breadcrumb li a {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb li a:hover {
    color: var(--accent-color);
}

.breadcrumb li.active {
    color: var(--accent-color);
    font-weight: 500;
}

/* Payment Section */
.payment-section {
    padding: 40px 0 80px;
    background-color: var(--bg-light);
}

.payment-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

/* Fade-in Animation */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInAnimation 0.8s ease forwards;
}

@keyframes fadeInAnimation {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Payment Methods */
.payment-methods {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
}

.section-title {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
}

/* Payment Options */
.payment-options {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.payment-option {
    flex: 1;
    min-width: 120px;
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: var(--accent-color);
    background-color: #fff;
    transform: translateY(-3px);
}

.payment-option.active {
    border-color: var(--accent-color);
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.1);
}

.option-icon {
    font-size: 1.5rem;
    color: #495057;
    margin-bottom: 10px;
}

.payment-option.active .option-icon {
    color: var(--accent-color);
}

.option-text {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

/* Payment Forms */
.payment-form {
    display: none;
    animation: fadeIn 0.5s ease;
}

.payment-form.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Credit Card Form */
.card-container {
    margin-bottom: 30px;
}

.card-preview {
    width: 100%;
    max-width: 400px;
    height: 220px;
    margin: 0 auto 30px;
    perspective: 1000px;
    position: relative;
}

.card-front, .card-back {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.6s ease;
    backface-visibility: hidden;
}

.card-front {
    background: linear-gradient(135deg, #5a67d8, #3c366b);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 2;
}

.card-back {
    background: linear-gradient(135deg, #4c51bf, #2d3748);
    transform: rotateY(180deg);
    z-index: 1;
}

.card-preview.flipped .card-front {
    transform: rotateY(180deg);
}

.card-preview.flipped .card-back {
    transform: rotateY(0);
}

.card-type {
    display: flex;
    justify-content: flex-end;
    height: 40px;
}

.card-logo {
    height: 100%;
    display: none;
}

.card-logo.active {
    display: block;
}

.card-number {
    font-size: 1.5rem;
    letter-spacing: 2px;
    text-align: center;
    margin: 20px 0;
}

.number-group {
    margin: 0 5px;
}

.card-details {
    display: flex;
    justify-content: space-between;
}

.card-holder, .card-expires {
    display: flex;
    flex-direction: column;
}

.card-holder label, .card-expires label {
    font-size: 0.7rem;
    text-transform: uppercase;
    opacity: 0.7;
    margin-bottom: 5px;
}

.card-holder .value, .card-expires .value {
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.card-stripe {
    height: 40px;
    background-color: #000;
    margin: 20px 0;
}

.card-cvv {
    background-color: white;
    color: #000;
    width: 60px;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    margin-left: auto;
}

.card-cvv label {
    font-size: 0.7rem;
    display: block;
    margin-bottom: 5px;
}

.card-cvv .value {
    font-size: 0.9rem;
    letter-spacing: 1px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-group.half {
    flex: 1;
}

label {
    display: block;
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 8px;
}

.input-with-icon {
    position: relative;
}

.input-with-icon input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-with-icon input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    outline: none;
}

.input-with-icon i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #adb5bd;
}

/* Checkbox Style */
.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #495057;
    user-select: none;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 3px;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: #e9ecef;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.secure-payment {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #6c757d;
    margin: 20px 0;
}

.secure-payment i {
    color: #28a745;
}

.accepted-cards {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.accepted-cards img {
    height: 30px;
}

/* Bank Transfer Form */
.bank-info {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.bank-info h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
}

.bank-details {
    margin: 20px 0;
}

.bank-detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.bank-detail-item .label {
    width: 150px;
    font-weight: 500;
    color: #495057;
}

.bank-detail-item .value {
    flex: 1;
    font-family: monospace;
    font-size: 1rem;
    color: #343a40;
}

.copy-btn {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    padding: 5px;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    color: var(--accent-hover);
    transform: scale(1.1);
}

.bank-note {
    padding: 15px;
    background-color: #fff;
    border-left: 3px solid var(--accent-color);
    border-radius: 0 5px 5px 0;
}

.bank-note p {
    font-size: 0.9rem;
    color: #495057;
    margin: 0;
}

/* E-wallet Form */
.ewallet-options h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 20px;
}

.ewallet-list {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.ewallet-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100px;
}

.ewallet-item:hover {
    border-color: var(--accent-color);
    transform: translateY(-3px);
}

.ewallet-item.active {
    border-color: var(--accent-color);
    background-color: rgba(231, 76, 60, 0.05);
}

.ewallet-item img {
    height: 40px;
}

.ewallet-item span {
    font-size: 0.9rem;
    color: #495057;
}

.qr-container {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.qr-code {
    width: 150px;
    height: 150px;
}

.qr-code img {
    width: 100%;
    height: 100%;
}

.qr-instructions p {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 10px;
}

/* COD Form */
.cod-info {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.cod-info h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
}

.cod-info p {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 20px;
}

.cod-note {
    padding: 15px;
    background-color: #fff;
    border-left: 3px solid var(--accent-color);
    border-radius: 0 5px 5px 0;
}

.cod-note ul {
    padding-left: 20px;
    margin-top: 10px;
}

.cod-note li {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 5px;
}

/* Order Summary */
.order-summary {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    position: sticky;
    top: 20px;
}

.summary-title {
    font-size: 1.3rem;
    color: #343a40;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.summary-items {
    margin-bottom: 30px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.item-name {
    font-size: 0.9rem;
    color: #6c757d;
}

.item-value {
    font-size: 0.9rem;
    color: #343a40;
    font-weight: 500;
}

.summary-item.discount .item-value {
    color: #28a745;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.total-label {
    font-size: 1.1rem;
    color: #343a40;
    font-weight: 500;
}

.total-value {
    font-size: 1.3rem;
    color: var(--accent-color);
    font-weight: 600;
}

.btn-complete-payment {
    width: 100%;
    padding: 15px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-complete-payment::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
    z-index: -1;
}

.btn-complete-payment:hover {
    background-color: var(--accent-hover);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-complete-payment:hover::before {
    left: 100%;
}

.btn-complete-payment:active {
    transform: translateY(-1px);
}

.back-to-cart {
    text-align: center;
}

.back-to-cart a {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
}

.back-to-cart a:hover {
    color: var(--accent-color);
    background-color: rgba(231, 76, 60, 0.05);
}

/* Payment Security */
.payment-security {
    margin-top: 30px;
    text-align: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #dee2e6;
}

.payment-security h4 {
    font-size: 1rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.security-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.security-icons img {
    height: 30px;
    filter: grayscale(0.2);
    transition: all 0.3s ease;
}

.security-icons img:hover {
    filter: grayscale(0);
    transform: translateY(-3px);
}

.payment-security p {
    font-size: 0.8rem;
    color: var(--text-light);
}

.payment-security p i {
    color: var(--success-color);
    margin-right: 5px;
}

/* Chatbot Styles */
#chat-bot {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    display: none;
    flex-direction: column;
    z-index: 1000;
    overflow: hidden;
}

.chat-header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
}

#chat-close {
    cursor: pointer;
    font-size: 1.2rem;
}

.chat-body {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.chat-message {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
    background-color: #f1f3f5;
    align-self: flex-end;
}

.chat-message.bot {
    background-color: var(--accent-color);
    color: white;
    align-self: flex-start;
}

#chat-input {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    outline: none;
}

#chat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
    z-index: 999;
    transition: all 0.3s ease;
}

#chat-toggle:hover {
    transform: scale(1.1);
}

/* Lucky Wheel Styles */
.spin-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.hide-spin {
    display: none;
}

.spin-container {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 400px;
    position: relative;
}

.close-spin {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #adb5bd;
}

.close-spin:hover {
    color: var(--accent-color);
}

#wheel {
    margin: 20px auto;
}

#spin-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

#spin-btn:hover {
    background-color: var(--accent-hover);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

#spin-result {
    margin-top: 20px;
    font-weight: 500;
    color: var(--accent-color);
    font-size: 1.2rem;
}

/* Responsive */
@media (max-width: 992px) {
    .payment-wrapper {
        grid-template-columns: 1fr;
    }

    .order-summary {
        position: static;
    }
}

@media (max-width: 768px) {
    .payment-options {
        flex-wrap: wrap;
    }

    .payment-option {
        flex: 1 0 calc(50% - 15px);
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .qr-container {
        flex-direction: column;
    }

    #chat-bot {
        width: 280px;
        bottom: 80px;
        right: 10px;
    }
}

@media (max-width: 576px) {
    .payment-option {
        flex: 1 0 100%;
    }

    .bank-detail-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .bank-detail-item .label {
        width: 100%;
        margin-bottom: 5px;
    }

    .ewallet-list {
        justify-content: center;
    }

    .spin-container {
        padding: 20px;
        max-width: 300px;
    }
}
