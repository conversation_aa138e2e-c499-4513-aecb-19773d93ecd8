
/* Cart Page Styles */
@import url('style.css');

/* <PERSON><PERSON>er */
.cart-header {
    padding: 2rem 0;
    text-align: center;
    background-color: var(--secondary-color);
    margin-bottom: 2rem;
}

.cart-header h1 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.cart-header p {
    color: var(--text-color);
    font-size: 1rem;
}

.cart-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.5rem 0;
    font-size: 0.9rem;
}

.cart-breadcrumb a {
    color: var(--text-color);
}

.cart-breadcrumb .current {
    color: var(--accent-color);
    font-weight: 500;
}

.cart-breadcrumb .separator {
    margin: 0 0.8rem;
    color: #999;
}

/* Main Cart Container */
#maincart {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto 3rem;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

/* Cart Items */
#cartdiv {
    flex: 3;
    background-color: #fff;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    min-width: 300px;
}

/* Cart Table */
.cart-table {
    width: 100%;
    border-collapse: collapse;
}

.cart-table th {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    font-weight: 600;
    text-align: left;
    padding: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cart-table td {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.cart-product {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-product-img {
    width: 100px;
    height: 100px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.cart-product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-product-info {
    flex: 1;
}

.cart-product-name {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.cart-product-variant {
    font-size: 0.85rem;
    color: #777;
}

.cart-quantity {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.cart-quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.cart-quantity-btn:hover {
    background-color: var(--secondary-color);
}

.cart-quantity-input {
    width: 40px;
    height: 30px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
    font-size: 0.9rem;
}

.cart-price {
    font-weight: 600;
    color: var(--primary-color);
}

.cart-remove {
    color: #999;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.cart-remove:hover {
    color: var(--accent-color);
}

/* Cart Summary */
#promocode {
    flex: 1;
    min-width: 300px;
}

#promodiv, #orderdiv {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

#promodiv h4, #orderdiv h4 {
    font-family: var(--heading-font);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
}

.promo-form {
    display: flex;
    gap: 0.5rem;
}

#code {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
}

#apply {
    padding: 0 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

#apply:hover {
    background-color: var(--accent-color);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    padding: 0.8rem 0;
    font-size: 0.95rem;
}

.summary-row.total {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--primary-color);
    border-top: 1px solid var(--border-color);
    margin-top: 0.5rem;
    padding-top: 1rem;
}

.checkout-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-top: 1rem;
}

#checkoutbtn {
    width: 100%;
    padding: 1rem;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: background-color var(--transition-fast), transform var(--transition-fast);
    text-transform: uppercase;
    letter-spacing: 1px;
}

#checkoutbtn:hover {
    background-color: var(--accent-hover);
    transform: translateY(-2px);
}

.btn-clear-cart {
    width: 100%;
    padding: 0.8rem;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-clear-cart:hover {
    background-color: #d32f2f;
}

/* Empty Cart */
#cartempty {
    text-align: center;
    padding: 4rem 2rem;
    background-color: #fff;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin: 2rem auto;
    max-width: 800px;
    display: none;
}

#cartempty h1 {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

#cartempty p {
    color: #777;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

#cartempty .btn {
    display: inline-block;
    padding: 1rem 2rem;
}

.cart-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

#clearCartBtn {
    background-color: #f44336;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

#clearCartBtn:hover {
    background-color: #d32f2f;
}

.btn-success {
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-success:hover {
    background-color: #388E3C;
}

/* Recommended Products */
#Recommend {
    width: 90%;
    max-width: 1200px;
    margin: 4rem auto;
}

#Recommend > h2 {
    font-family: var(--heading-font);
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    color: var(--primary-color);
}

#rcmndData {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

.recommend-product {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.recommend-product:hover {
    transform: translateY(-5px);
}

.recommend-img {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.recommend-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.recommend-product:hover .recommend-img img {
    transform: scale(1.05);
}

.recommend-info {
    padding: 1.2rem;
}

.recommend-name {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-size: 0.95rem;
    line-height: 1.4;
    height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
     -webkit-line-clamp: 2; 
    -webkit-box-orient: vertical;
}

.recommend-price {
    color: var(--accent-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.recommend-colors {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.8rem;
}

.color-option {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 1px solid #ddd;
    transition: transform var(--transition-fast);
}

.color-option:hover {
    transform: scale(1.2);
}

/* Responsive */
@media (max-width: 992px) {
    #rcmndData {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    #maincart {
        flex-direction: column;
    }

    #rcmndData {
        grid-template-columns: repeat(2, 1fr);
    }

    .cart-table th:nth-child(3),
    .cart-table td:nth-child(3) {
        display: none;
    }
}

@media (max-width: 576px) {
    #rcmndData {
        grid-template-columns: 1fr;
    }

    .cart-product-img {
        width: 80px;
        height: 80px;
    }

    .cart-table th:nth-child(4),
    .cart-table td:nth-child(4) {
        display: none;
    }
}




#applyCoins {
    padding: 0 1rem;
    background-color: #009688;
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

#applyCoins:hover {
    background-color: #00796B;
}