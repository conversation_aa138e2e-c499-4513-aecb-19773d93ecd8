// Product Alerts Feature

document.addEventListener('DOMContentLoaded', function() {
    // Initialize product alerts
    initProductAlerts();
    
    // Add subscription form to product pages
    addSubscriptionForm();
});

// Initialize product alerts
function initProductAlerts() {
    // Add product alerts styles
    addProductAlertsStyles();
    
    // Show new product notification if user has subscribed
    showNewProductNotification();
}

// Show new product notification
function showNewProductNotification() {
    // Check if user has subscribed to alerts
    const subscriptions = getSubscriptions();
    
    if (subscriptions.length > 0) {
        // Check if there are new products
        const newProducts = getNewProducts();
        
        if (newProducts.length > 0) {
            // Filter new products based on user subscriptions
            const relevantProducts = newProducts.filter(product => {
                return subscriptions.some(sub => {
                    // Check if product matches subscription criteria
                    return (sub.category === 'all' || sub.category === product.category) &&
                           (sub.gender === 'all' || sub.gender === product.gender);
                });
            });
            
            if (relevantProducts.length > 0) {
                // Show notification
                setTimeout(() => {
                    showProductNotification(relevantProducts);
                }, 3000);
            }
        }
    }
}

// Get user subscriptions
function getSubscriptions() {
    const savedSubscriptions = localStorage.getItem('productAlertSubscriptions');
    return savedSubscriptions ? JSON.parse(savedSubscriptions) : [];
}

// Save user subscription
function saveSubscription(subscription) {
    const subscriptions = getSubscriptions();
    
    // Check if subscription already exists
    const exists = subscriptions.some(sub => 
        sub.email === subscription.email && 
        sub.category === subscription.category && 
        sub.gender === subscription.gender
    );
    
    if (!exists) {
        // Add new subscription
        subscriptions.push(subscription);
        
        // Save to localStorage
        localStorage.setItem('productAlertSubscriptions', JSON.stringify(subscriptions));
        
        return true;
    }
    
    return false;
}

// Get new products (in a real app, this would come from a backend API)
function getNewProducts() {
    // Simulate new products
    return [
        {
            id: 101,
            name: 'Váy Ngủ 2 Dây Liền Thân Dáng Xuông',
            price: '550.000đ',
            category: 'tops',
            gender: 'female',
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lpelwtsprg3y68@resize_w450_nl.webp',
            dateAdded: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
        },
        {
            id: 102,
            name: 'Áo Thun Nữ Form Vừa SUNTEE Phong Cách Basic In Chữ Nhỏ Vải Cotton 250GSM Cao Cấp Thoáng Mát ',
            price: '480.000đ',
            category: 'bottoms',
            gender: 'male',
            image: 'https://down-vn.img.susercontent.com/file/vn-11134201-7ra0g-m9eroxt1cbr6a8@resize_w450_nl.webp',
            dateAdded: new Date(Date.now() - 48 * 60 * 60 * 1000) // 2 days ago
        },
        {
            id: 103,
            name: 'Meixuemeilin Váy hoa chữ A nhiều lớp thời trang đáng yêu dành cho nữ',
            price: '620.000đ',
            category: 'dresses',
            gender: 'female',
            image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lp3hjpt8l6qk1f@resize_w450_nl.webp',
            dateAdded: new Date(Date.now() - 72 * 60 * 60 * 1000) // 3 days ago
        }
    ];
}

// Show product notification
function showProductNotification(products) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'product-alert-notification';
    
    // Create notification content
    let content = `
        <div class="product-alert-header">
            <h3>Sản phẩm mới dành cho bạn</h3>
            <button class="product-alert-close">&times;</button>
        </div>
        <div class="product-alert-body">
    `;
    
    // Add products (limit to 3)
    const displayProducts = products.slice(0, 3);
    
    displayProducts.forEach(product => {
        content += `
            <div class="alert-product-item" data-product-id="${product.id}">
                <div class="alert-product-image">
                    <img src="${product.image}" alt="${product.name}">
                </div>
                <div class="alert-product-info">
                    <div class="alert-product-name">${product.name}</div>
                    <div class="alert-product-price">${product.price}</div>
                </div>
                <button class="alert-view-btn">Xem</button>
            </div>
        `;
    });
    
    // Add footer if there are more products
    if (products.length > 3) {
        content += `
            <div class="product-alert-footer">
                <button class="view-all-products-btn">Xem tất cả ${products.length} sản phẩm mới</button>
            </div>
        `;
    }
    
    content += `</div>`;
    
    // Set notification content
    notification.innerHTML = content;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Add close event
    const closeBtn = notification.querySelector('.product-alert-close');
    closeBtn.addEventListener('click', function() {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Add click events to product items
    const productItems = notification.querySelectorAll('.alert-product-item');
    productItems.forEach(item => {
        item.addEventListener('click', function() {
            const productId = this.dataset.productId;
            // In a real app, this would navigate to the product page
            window.location.href = './productdetail.html?id=' + productId;
        });
    });
    
    // Add click event to view all button
    const viewAllBtn = notification.querySelector('.view-all-products-btn');
    if (viewAllBtn) {
        viewAllBtn.addEventListener('click', function() {
            // In a real app, this would navigate to a page with all new products
            window.location.href = './womenproducts.html?filter=new';
        });
    }
    
    // Auto hide after 10 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 10000);
}

// Add subscription form to product pages
function addSubscriptionForm() {
    // Check if on product detail page
    const productDetailPage = document.querySelector('.product-detail');
    
    if (productDetailPage) {
        // Create subscription form
        const subscriptionForm = document.createElement('div');
        subscriptionForm.className = 'product-alert-subscription';
        
        subscriptionForm.innerHTML = `
            <div class="subscription-header">
                <h3>Nhận thông báo khi có sản phẩm mới</h3>
                <p>Đăng ký để nhận thông báo khi có sản phẩm mới phù hợp với sở thích của bạn</p>
            </div>
            <form id="alert-subscription-form">
                <div class="subscription-form-group">
                    <input type="email" id="subscription-email" placeholder="Email của bạn" required>
                </div>
                <div class="subscription-form-group">
                    <label>Danh mục:</label>
                    <select id="subscription-category">
                        <option value="all">Tất cả danh mục</option>
                        <option value="tops">Áo</option>
                        <option value="bottoms">Quần</option>
                        <option value="dresses">Váy đầm</option>
                        <option value="outerwear">Áo khoác</option>
                        <option value="accessories">Phụ kiện</option>
                    </select>
                </div>
                <div class="subscription-form-group">
                    <label>Giới tính:</label>
                    <select id="subscription-gender">
                        <option value="all">Tất cả</option>
                        <option value="female">Nữ</option>
                        <option value="male">Nam</option>
                    </select>
                </div>
                <button type="submit" class="subscription-submit-btn">Đăng ký</button>
            </form>
        `;
        
        // Find where to insert subscription form
        const productInfo = productDetailPage.querySelector('.product-info');
        if (productInfo) {
            // Append to product info
            productInfo.appendChild(subscriptionForm);
            
            // Add submit event
            const form = subscriptionForm.querySelector('#alert-subscription-form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form values
                const email = document.getElementById('subscription-email').value;
                const category = document.getElementById('subscription-category').value;
                const gender = document.getElementById('subscription-gender').value;
                
                // Save subscription
                const subscription = {
                    email: email,
                    category: category,
                    gender: gender,
                    date: new Date().toISOString()
                };
                
                const saved = saveSubscription(subscription);
                
                if (saved) {
                    showNotification('Đăng ký nhận thông báo thành công!', 'success');
                    form.reset();
                } else {
                    showNotification('Bạn đã đăng ký nhận thông báo này rồi', 'info');
                }
            });
        }
    }
    
    // Add subscription section to footer
    const footer = document.querySelector('.footer');
    if (footer && !footer.querySelector('.footer-subscription')) {
        // Create subscription section
        const subscriptionSection = document.createElement('div');
        subscriptionSection.className = 'footer-subscription';
        
        subscriptionSection.innerHTML = `
            <h3>Đăng ký nhận thông báo</h3>
            <p>Nhận thông báo về sản phẩm mới và khuyến mãi</p>
            <form id="footer-subscription-form">
                <div class="footer-subscription-form">
                    <input type="email" id="footer-subscription-email" placeholder="Email của bạn" required>
                    <button type="submit">Đăng ký</button>
                </div>
            </form>
        `;
        
        // Find where to insert subscription section
        const footerContent = footer.querySelector('.footer-content');
        if (footerContent) {
            // Create new column
            const subscriptionColumn = document.createElement('div');
            subscriptionColumn.className = 'footer-column';
            subscriptionColumn.appendChild(subscriptionSection);
            
            // Append to footer content
            footerContent.appendChild(subscriptionColumn);
            
            // Add submit event
            const form = subscriptionSection.querySelector('#footer-subscription-form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form values
                const email = document.getElementById('footer-subscription-email').value;
                
                // Save subscription
                const subscription = {
                    email: email,
                    category: 'all',
                    gender: 'all',
                    date: new Date().toISOString()
                };
                
                const saved = saveSubscription(subscription);
                
                if (saved) {
                    showNotification('Đăng ký nhận thông báo thành công!', 'success');
                    form.reset();
                } else {
                    showNotification('Bạn đã đăng ký nhận thông báo này rồi', 'info');
                }
            });
        }
    }
}

// Add product alerts styles
function addProductAlertsStyles() {
    // Check if styles already exist
    if (document.getElementById('product-alerts-styles')) return;
    
    // Create style element
    const style = document.createElement('style');
    style.id = 'product-alerts-styles';
    
    // Add CSS
    style.innerHTML = `
        /* Product Alert Notification */
        .product-alert-notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            z-index: 9998;
            transform: translateX(120%);
            transition: transform 0.3s ease;
            overflow: hidden;
        }
        
        .product-alert-notification.show {
            transform: translateX(0);
        }
        
        .product-alert-header {
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .product-alert-header h3 {
            margin: 0;
            font-size: 1rem;
            color: #343a40;
        }
        
        .product-alert-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #adb5bd;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .product-alert-close:hover {
            color: #ff6b6b;
        }
        
        .product-alert-body {
            padding: 15px;
        }
        
        .alert-product-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-bottom: 10px;
        }
        
        .alert-product-item:hover {
            background-color: #f8f9fa;
        }
        
        .alert-product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .alert-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .alert-product-info {
            flex: 1;
        }
        
        .alert-product-name {
            font-size: 0.9rem;
            color: #343a40;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .alert-product-price {
            font-size: 1rem;
            color: #ff6b6b;
            font-weight: 600;
        }
        
        .alert-view-btn {
            padding: 6px 12px;
            background-color: #ff6b6b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-size: 0.8rem;
        }
        
        .alert-view-btn:hover {
            background-color: #fa5252;
        }
        
        .product-alert-footer {
            padding-top: 10px;
            border-top: 1px solid #dee2e6;
            text-align: center;
        }
        
        .view-all-products-btn {
            background: none;
            border: none;
            color: #339af0;
            cursor: pointer;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .view-all-products-btn:hover {
            color: #1c7ed6;
            text-decoration: underline;
        }
        
        /* Subscription Form */
        .product-alert-subscription {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        
        .subscription-header {
            margin-bottom: 15px;
        }
        
        .subscription-header h3 {
            font-size: 1.1rem;
            color: #343a40;
            margin: 0 0 5px 0;
        }
        
        .subscription-header p {
            font-size: 0.9rem;
            color: #868e96;
            margin: 0;
        }
        
        .subscription-form-group {
            margin-bottom: 15px;
        }
        
        .subscription-form-group label {
            display: block;
            font-size: 0.9rem;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .subscription-form-group input,
        .subscription-form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }
        
        .subscription-form-group input:focus,
        .subscription-form-group select:focus {
            border-color: #ff6b6b;
            outline: none;
        }
        
        .subscription-submit-btn {
            width: 100%;
            padding: 10px;
            background-color: #ff6b6b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .subscription-submit-btn:hover {
            background-color: #fa5252;
        }
        
        /* Footer Subscription */
        .footer-subscription {
            margin-bottom: 20px;
        }
        
        .footer-subscription h3 {
            font-size: 1.1rem;
            color: #f8f9fa;
            margin: 0 0 10px 0;
        }
        
        .footer-subscription p {
            font-size: 0.9rem;
            color: #adb5bd;
            margin: 0 0 15px 0;
        }
        
        .footer-subscription-form {
            display: flex;
            gap: 10px;
        }
        
        .footer-subscription-form input {
            flex: 1;
            padding: 10px;
            border: 1px solid #495057;
            background-color: #343a40;
            color: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .footer-subscription-form input:focus {
            outline: none;
            border-color: #ff6b6b;
        }
        
        .footer-subscription-form button {
            padding: 10px 15px;
            background-color: #ff6b6b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .footer-subscription-form button:hover {
            background-color: #fa5252;
        }
        
        /* Responsive */
        @media (max-width: 576px) {
            .product-alert-notification {
                width: calc(100% - 40px);
                bottom: 70px;
            }
            
            .footer-subscription-form {
                flex-direction: column;
            }
        }
    `;
    
    // Add to head
    document.head.appendChild(style);
}

// Initialize on load
document.addEventListener('DOMContentLoaded', initProductAlerts);
