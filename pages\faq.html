<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="../styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <title>Câu hỏi thường gặp | Fashion Store</title>

    <style>
        /* FAQ Page Styles */
        .faq-container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .faq-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .faq-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .faq-header h1 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .faq-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .faq-search {
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 40px;
            text-align: center;
        }

        .faq-search h2 {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
        }

        .search-form {
            display: flex;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-form input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
        }

        .search-form button {
            background-color: #ff6b6b;
            color: white;
            border: none;
            padding: 0 20px;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .search-form button:hover {
            background-color: #ff5252;
        }

        .faq-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 40px;
            justify-content: center;
        }

        .category-btn {
            background-color: #f0f0f0;
            color: #666;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .category-btn:hover, .category-btn.active {
            background-color: #ff6b6b;
            color: white;
        }

        .faq-section {
            margin-bottom: 40px;
        }

        .faq-section h2 {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .faq-item {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
        }

        .faq-question {
            background-color: #f9f9f9;
            padding: 15px 20px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }

        .faq-question:hover {
            background-color: #f0f0f0;
        }

        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Pro';
            color: #ff6b6b;
            transition: transform 0.3s;
        }

        .faq-question.active {
            background-color: #ff6b6b;
            color: white;
        }

        .faq-question.active::after {
            transform: rotate(180deg);
            color: white;
        }

        .faq-answer {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.show {
            padding: 20px;
            max-height: 1000px;
        }

        .faq-answer p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .faq-answer ul {
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .faq-answer li {
            color: #666;
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .contact-box {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-top: 40px;
        }

        .contact-box h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .contact-box p {
            color: #666;
            margin-bottom: 20px;
        }

        .contact-btn {
            display: inline-block;
            background-color: #ff6b6b;
            color: white;
            padding: 12px 25px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .contact-btn:hover {
            background-color: #ff5252;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }

            .search-form input {
                border-radius: 4px;
                margin-bottom: 10px;
            }

            .search-form button {
                border-radius: 4px;
                padding: 10px;
            }

            .faq-categories {
                flex-direction: column;
                align-items: center;
            }

            .category-btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
        </div>
    </div>

    <!-- Header Section -->
    <header id="header">
        <!-- Top Announcement Bar -->
        <div class="announcement-bar">
            <p><i class="fas fa-truck"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ</p>
        </div>

        <!-- Main Navigation -->
        <nav class="main-nav">
            <div class="container">
                <div class="nav-wrapper">
                    <!-- Mobile Menu Toggle -->
                    <div class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </div>

                    <!-- Logo -->
                    <div class="logo">
                        <a href="../index.html">
                            <img src="../img logo/logo 1.jpg" alt="Fashion Store Logo">
                        </a>
                    </div>

                    <!-- Navigation Menu -->
                    <ul class="nav-menu">
                        <li class="nav-item has-dropdown">
                            <a href="../women.html">Nữ</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="../womenproducts.html">Áo sơ mi</a></li>
                                        <li><a href="../womenproducts.html">Áo thun</a></li>
                                        <li><a href="../womenproducts.html">Quần jean</a></li>
                                        <li><a href="../womenproducts.html">Váy & Đầm</a></li>
                                        <li><a href="../womenproducts.html">Áo khoác</a></li>
                                        <li><a href="../womenproducts.html">Đồ thể thao</a></li>
                                        <li><a href="../womenproducts.html">Đồ ngủ</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="../womenproducts.html">Mùa hè 2024</a></li>
                                        <li><a href="../womenproducts.html">Công sở thanh lịch</a></li>
                                        <li><a href="../womenproducts.html">Dạo phố năng động</a></li>
                                        <li><a href="../womenproducts.html">Dự tiệc sang trọng</a></li>
                                        <li><a href="../womenproducts.html">Đồ thể thao</a></li>
                                        <li><a href="../womenproducts.html">Hàng mới về</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img womens/women 15.webp" alt="Women's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="../womenproducts.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="../men.html">Nam</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="../menproducts.html">Áo sơ mi</a></li>
                                        <li><a href="../menproducts.html">Áo thun</a></li>
                                        <li><a href="../menproducts.html">Quần jean</a></li>
                                        <li><a href="../menproducts.html">Quần kaki</a></li>
                                        <li><a href="../menproducts.html">Áo khoác</a></li>
                                        <li><a href="../menproducts.html">Đồ thể thao</a></li>
                                        <li><a href="../menproducts.html">Đồ công sở</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="../menproducts.html">Mùa hè 2024</a></li>
                                        <li><a href="../menproducts.html">Công sở lịch lãm</a></li>
                                        <li><a href="../menproducts.html">Thể thao năng động</a></li>
                                        <li><a href="../menproducts.html">Dạo phố cá tính</a></li>
                                        <li><a href="../menproducts.html">Hàng mới về</a></li>
                                        <li><a href="../menproducts.html">Bán chạy nhất</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img mens/men 10.jpg" alt="Men's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="../menproducts.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="./kids.html">Trẻ em</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Bé gái</h3>
                                    <ul>
                                        <li><a href="./kids.html">Áo</a></li>
                                        <li><a href="./kids.html">Quần</a></li>
                                        <li><a href="./kids.html">Váy đầm</a></li>
                                        <li><a href="./kids.html">Đồ ngủ</a></li>
                                        <li><a href="./kids.html">Đồ thể thao</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bé trai</h3>
                                    <ul>
                                        <li><a href="./kids.html">Áo</a></li>
                                        <li><a href="./kids.html">Quần</a></li>
                                        <li><a href="./kids.html">Đồ ngủ</a></li>
                                        <li><a href="./kids.html">Đồ thể thao</a></li>
                                        <li><a href="./kids.html">Bộ quần áo</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img womens/women 12.webp" alt="Kids Collection">
                                    <h4>Bộ sưu tập mới cho bé</h4>
                                    <a href="./kids.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="./sale.html">Khuyến mãi</a>
                        </li>
                        <li class="nav-item">
                            <a href="../about.html">Giới thiệu</a>
                        </li>
                        <li class="nav-item">
                            <a href="../blog.html">Blog</a>
                        </li>
                    </ul>

                    <!-- Right Navigation -->
                    <div class="nav-right">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="Tìm kiếm...">
                            <button id="search-btn"><i class="fas fa-search"></i></button>
                        </div>
                        <div class="nav-icons">
                            <a href="./account.html" class="nav-icon" title="Tài khoản">
                                <i class="fas fa-user"></i>
                            </a>
                            <a href="./wishlist.html" class="nav-icon" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </a>
                            <a href="../AddCart.html" class="nav-icon cart-icon" title="Giỏ hàng">
                                <i class="fas fa-shopping-bag"></i>
                                <span class="cart-count">0</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- FAQ Content -->
    <div class="faq-container">
        <div class="faq-header">
            <h1>Câu hỏi thường gặp</h1>
            <p>Tìm câu trả lời cho những câu hỏi phổ biến về sản phẩm, đơn hàng, vận chuyển và các dịch vụ khác của Fashion Store.</p>
        </div>

        <div class="faq-search">
            <h2>Bạn cần hỗ trợ gì?</h2>
            <form class="search-form">
                <input type="text" placeholder="Nhập câu hỏi của bạn..." id="faq-search-input">
                <button type="button" id="faq-search-btn"><i class="fas fa-search"></i></button>
            </form>
        </div>

        <div class="faq-categories">
            <button class="category-btn active" data-category="all">Tất cả</button>
            <button class="category-btn" data-category="order">Đặt hàng & Thanh toán</button>
            <button class="category-btn" data-category="shipping">Vận chuyển & Giao hàng</button>
            <button class="category-btn" data-category="return">Đổi trả & Hoàn tiền</button>
            <button class="category-btn" data-category="product">Sản phẩm & Kích cỡ</button>
            <button class="category-btn" data-category="account">Tài khoản & Bảo mật</button>
        </div>

        <!-- Order & Payment Section -->
        <div class="faq-section" data-category="order">
            <h2>Đặt hàng & Thanh toán</h2>

            <div class="faq-item">
                <div class="faq-question">Làm thế nào để đặt hàng trên website?</div>
                <div class="faq-answer">
                    <p>Để đặt hàng trên website Fashion Store, bạn có thể thực hiện theo các bước sau:</p>
                    <ul>
                        <li>Tìm kiếm và chọn sản phẩm bạn muốn mua</li>
                        <li>Chọn kích cỡ, màu sắc và số lượng</li>
                        <li>Nhấn nút "Thêm vào giỏ hàng"</li>
                        <li>Kiểm tra giỏ hàng và nhấn "Thanh toán"</li>
                        <li>Điền thông tin giao hàng và chọn phương thức thanh toán</li>
                        <li>Xác nhận đơn hàng</li>
                    </ul>
                    <p>Sau khi đặt hàng thành công, bạn sẽ nhận được email xác nhận đơn hàng.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Fashion Store chấp nhận những phương thức thanh toán nào?</div>
                <div class="faq-answer">
                    <p>Fashion Store chấp nhận nhiều phương thức thanh toán khác nhau để mang đến sự thuận tiện cho khách hàng:</p>
                    <ul>
                        <li>Thanh toán khi nhận hàng (COD)</li>
                        <li>Thẻ tín dụng/ghi nợ (Visa, MasterCard, JCB)</li>
                        <li>Chuyển khoản ngân hàng</li>
                        <li>Ví điện tử (MoMo, ZaloPay, VNPay)</li>
                        <li>Trả góp qua thẻ tín dụng</li>
                    </ul>
                    <p>Lưu ý: Đối với thanh toán COD, chúng tôi chỉ áp dụng cho đơn hàng có giá trị dưới 5.000.000đ.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi có thể thay đổi hoặc hủy đơn hàng sau khi đã đặt không?</div>
                <div class="faq-answer">
                    <p>Bạn có thể thay đổi hoặc hủy đơn hàng trong vòng 24 giờ sau khi đặt hàng, với điều kiện đơn hàng chưa được xử lý hoặc giao cho đơn vị vận chuyển.</p>
                    <p>Để thay đổi hoặc hủy đơn hàng, bạn có thể:</p>
                    <ul>
                        <li>Đăng nhập vào tài khoản và truy cập mục "Đơn hàng của tôi"</li>
                        <li>Liên hệ với bộ phận Chăm sóc Khách hàng qua số điện thoại 1900 1234 hoặ<NAME_EMAIL></li>
                    </ul>
                    <p>Lưu ý: Đối với đơn hàng đã được giao cho đơn vị vận chuyển, bạn sẽ cần tuân theo chính sách đổi trả của chúng tôi.</p>
                </div>
            </div>
        </div>

        <!-- Shipping & Delivery Section -->
        <div class="faq-section" data-category="shipping">
            <h2>Vận chuyển & Giao hàng</h2>

            <div class="faq-item">
                <div class="faq-question">Thời gian giao hàng là bao lâu?</div>
                <div class="faq-answer">
                    <p>Thời gian giao hàng phụ thuộc vào khu vực của bạn:</p>
                    <ul>
                        <li>Nội thành Hà Nội và TP. Hồ Chí Minh: 1-2 ngày làm việc</li>
                        <li>Các thành phố lớn khác: 2-3 ngày làm việc</li>
                        <li>Các tỉnh thành khác: 3-5 ngày làm việc</li>
                        <li>Vùng sâu, vùng xa: 5-7 ngày làm việc</li>
                    </ul>
                    <p>Lưu ý: Thời gian trên không bao gồm thời gian xử lý đơn hàng (1-2 ngày làm việc) và có thể bị ảnh hưởng bởi điều kiện thời tiết, lễ tết hoặc các yếu tố khách quan khác.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Phí vận chuyển được tính như thế nào?</div>
                <div class="faq-answer">
                    <p>Phí vận chuyển được tính dựa trên khoảng cách, trọng lượng và kích thước của đơn hàng:</p>
                    <ul>
                        <li>Nội thành Hà Nội và TP. Hồ Chí Minh: 20.000đ - 30.000đ</li>
                        <li>Các thành phố lớn khác: 30.000đ - 40.000đ</li>
                        <li>Các tỉnh thành khác: 40.000đ - 60.000đ</li>
                        <li>Vùng sâu, vùng xa: 60.000đ - 100.000đ</li>
                    </ul>
                    <p>Đơn hàng có giá trị từ 500.000đ trở lên sẽ được miễn phí vận chuyển (áp dụng cho khu vực nội thành và các thành phố lớn).</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Làm thế nào để theo dõi đơn hàng của tôi?</div>
                <div class="faq-answer">
                    <p>Bạn có thể theo dõi đơn hàng của mình bằng một trong các cách sau:</p>
                    <ul>
                        <li>Đăng nhập vào tài khoản và truy cập mục "Đơn hàng của tôi"</li>
                        <li>Sử dụng công cụ "Theo dõi đơn hàng" trên website bằng cách nhập mã đơn hàng và email</li>
                        <li>Kiểm tra email xác nhận đơn hàng, trong đó có đường link theo dõi</li>
                        <li>Liên hệ với bộ phận Chăm sóc Khách hàng qua số điện thoại 1900 1234</li>
                    </ul>
                    <p>Chúng tôi cũng sẽ gửi thông báo qua email hoặc SMS khi đơn hàng của bạn được xử lý, giao cho đơn vị vận chuyển và khi giao hàng thành công.</p>
                </div>
            </div>
        </div>

        <!-- Return & Refund Section -->
        <div class="faq-section" data-category="return">
            <h2>Đổi trả & Hoàn tiền</h2>

            <div class="faq-item">
                <div class="faq-question">Chính sách đổi trả của Fashion Store như thế nào?</div>
                <div class="faq-answer">
                    <p>Fashion Store chấp nhận đổi trả sản phẩm trong vòng 7 ngày kể từ ngày nhận hàng, với các điều kiện sau:</p>
                    <ul>
                        <li>Sản phẩm còn nguyên tem, nhãn mác, thẻ bài và trong tình trạng chưa qua sử dụng</li>
                        <li>Sản phẩm không bị hư hỏng, bẩn, có mùi lạ hoặc đã qua giặt ủi</li>
                        <li>Sản phẩm còn đầy đủ bao bì, hộp đựng như ban đầu (nếu có)</li>
                        <li>Khách hàng có hóa đơn mua hàng hoặc đơn hàng online</li>
                    </ul>
                    <p>Lưu ý: Một số sản phẩm đặc biệt như đồ lót, đồ bơi, phụ kiện tóc, trang sức và các sản phẩm trong chương trình khuyến mãi đặc biệt có thể không được áp dụng chính sách đổi trả.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Quy trình đổi trả sản phẩm như thế nào?</div>
                <div class="faq-answer">
                    <p>Để đổi trả sản phẩm, bạn có thể thực hiện theo các bước sau:</p>
                    <ol>
                        <li>Liên hệ với bộ phận Chăm sóc Khách hàng qua số điện thoại 1900 1234 hoặ<NAME_EMAIL> để thông báo về việc muốn đổi trả sản phẩm</li>
                        <li>Chuẩn bị sản phẩm cần đổi trả với đầy đủ phụ kiện, tem nhãn và hóa đơn mua hàng</li>
                        <li>Mang sản phẩm đến cửa hàng gần nhất hoặc gửi qua đơn vị vận chuyển theo hướng dẫn của nhân viên chăm sóc khách hàng</li>
                        <li>Sau khi kiểm tra, chúng tôi sẽ tiến hành đổi sản phẩm mới hoặc hoàn tiền theo yêu cầu của bạn</li>
                    </ol>
                    <p>Lưu ý: Trong trường hợp đổi trả qua đơn vị vận chuyển, khách hàng sẽ chịu phí vận chuyển hai chiều, trừ trường hợp sản phẩm bị lỗi từ nhà sản xuất.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Thời gian hoàn tiền là bao lâu?</div>
                <div class="faq-answer">
                    <p>Thời gian hoàn tiền phụ thuộc vào phương thức thanh toán ban đầu:</p>
                    <ul>
                        <li>Thanh toán bằng thẻ tín dụng/ghi nợ: 5-15 ngày làm việc (tùy theo ngân hàng)</li>
                        <li>Thanh toán qua ví điện tử: 1-5 ngày làm việc</li>
                        <li>Thanh toán khi nhận hàng (COD): 3-7 ngày làm việc (hoàn tiền qua tài khoản ngân hàng)</li>
                        <li>Chuyển khoản ngân hàng: 3-7 ngày làm việc</li>
                    </ul>
                    <p>Lưu ý: Thời gian trên được tính từ khi chúng tôi nhận được sản phẩm trả lại và xác nhận đủ điều kiện đổi trả.</p>
                </div>
            </div>
        </div>

        <!-- Product & Size Section -->
        <div class="faq-section" data-category="product">
            <h2>Sản phẩm & Kích cỡ</h2>

            <div class="faq-item">
                <div class="faq-question">Làm thế nào để chọn đúng kích cỡ?</div>
                <div class="faq-answer">
                    <p>Để chọn đúng kích cỡ, bạn có thể tham khảo bảng size của chúng tôi trên trang chi tiết sản phẩm hoặc trong mục "Hướng dẫn chọn size". Chúng tôi khuyên bạn nên:</p>
                    <ul>
                        <li>Đo các số đo cơ thể của bạn (vòng ngực, vòng eo, vòng hông, chiều dài chân...)</li>
                        <li>So sánh số đo của bạn với bảng size của chúng tôi</li>
                        <li>Đọc các đánh giá của khách hàng khác về sản phẩm</li>
                        <li>Tham khảo mô tả sản phẩm để biết thêm thông tin về form dáng (rộng, vừa, ôm...)</li>
                    </ul>
                    <p>Nếu bạn vẫn không chắc chắn, hãy liên hệ với bộ phận Chăm sóc Khách hàng để được tư vấn.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Sản phẩm của Fashion Store được làm từ chất liệu gì?</div>
                <div class="faq-answer">
                    <p>Fashion Store cung cấp các sản phẩm với nhiều chất liệu khác nhau, tùy thuộc vào từng loại sản phẩm. Một số chất liệu phổ biến bao gồm:</p>
                    <ul>
                        <li>Cotton: Mềm mại, thoáng khí, thích hợp cho áo thun, áo sơ mi</li>
                        <li>Linen: Nhẹ, thoáng mát, thích hợp cho trang phục mùa hè</li>
                        <li>Polyester: Bền, ít nhăn, thích hợp cho nhiều loại trang phục</li>
                        <li>Denim: Bền, chắc chắn, thích hợp cho quần jeans</li>
                        <li>Len: Ấm áp, thích hợp cho trang phục mùa đông</li>
                        <li>Lụa: Mềm mại, sang trọng, thích hợp cho trang phục dự tiệc</li>
                    </ul>
                    <p>Thông tin chi tiết về chất liệu của từng sản phẩm được ghi rõ trong phần mô tả sản phẩm.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Làm thế nào để chăm sóc và bảo quản sản phẩm?</div>
                <div class="faq-answer">
                    <p>Để sản phẩm bền đẹp, bạn nên tuân thủ các hướng dẫn chăm sóc và bảo quản sau:</p>
                    <ul>
                        <li>Đọc kỹ hướng dẫn giặt ủi trên tem nhãn sản phẩm</li>
                        <li>Giặt riêng các sản phẩm có màu sắc khác nhau để tránh phai màu</li>
                        <li>Sử dụng nước giặt phù hợp với từng loại vải</li>
                        <li>Không ngâm sản phẩm quá lâu</li>
                        <li>Phơi sản phẩm ở nơi thoáng mát, tránh ánh nắng trực tiếp</li>
                        <li>Bảo quản sản phẩm ở nơi khô ráo, thoáng mát</li>
                    </ul>
                    <p>Đối với các sản phẩm đặc biệt như lụa, len, da... chúng tôi khuyên bạn nên giặt khô hoặc giặt tay nhẹ nhàng.</p>
                </div>
            </div>
        </div>

        <!-- Account & Security Section -->
        <div class="faq-section" data-category="account">
            <h2>Tài khoản & Bảo mật</h2>

            <div class="faq-item">
                <div class="faq-question">Làm thế nào để tạo tài khoản trên Fashion Store?</div>
                <div class="faq-answer">
                    <p>Để tạo tài khoản trên Fashion Store, bạn có thể thực hiện theo các bước sau:</p>
                    <ol>
                        <li>Truy cập website Fashion Store và nhấn vào biểu tượng người dùng ở góc trên bên phải</li>
                        <li>Chọn "Đăng ký"</li>
                        <li>Điền đầy đủ thông tin cá nhân (họ tên, email, số điện thoại, mật khẩu)</li>
                        <li>Đọc và đồng ý với Điều khoản sử dụng và Chính sách bảo mật</li>
                        <li>Nhấn "Đăng ký"</li>
                        <li>Xác nhận email (nếu được yêu cầu)</li>
                    </ol>
                    <p>Bạn cũng có thể đăng ký tài khoản bằng cách đăng nhập qua Facebook hoặc Google.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi quên mật khẩu, phải làm thế nào?</div>
                <div class="faq-answer">
                    <p>Nếu bạn quên mật khẩu, bạn có thể thực hiện các bước sau để đặt lại mật khẩu:</p>
                    <ol>
                        <li>Truy cập trang đăng nhập và nhấn vào "Quên mật khẩu"</li>
                        <li>Nhập địa chỉ email đã đăng ký tài khoản</li>
                        <li>Nhấn "Gửi yêu cầu"</li>
                        <li>Kiểm tra email và làm theo hướng dẫn để đặt lại mật khẩu</li>
                    </ol>
                    <p>Lưu ý: Link đặt lại mật khẩu chỉ có hiệu lực trong vòng 24 giờ. Nếu bạn không nhận được email, hãy kiểm tra thư mục spam hoặc liên hệ với bộ phận Chăm sóc Khách hàng.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Fashion Store bảo vệ thông tin cá nhân của tôi như thế nào?</div>
                <div class="faq-answer">
                    <p>Fashion Store cam kết bảo vệ thông tin cá nhân của khách hàng bằng các biện pháp sau:</p>
                    <ul>
                        <li>Sử dụng công nghệ mã hóa SSL để bảo vệ thông tin trong quá trình truyền tải</li>
                        <li>Lưu trữ thông tin cá nhân trên hệ thống máy chủ an toàn, được bảo vệ bởi tường lửa</li>
                        <li>Chỉ thu thập thông tin cần thiết cho việc xử lý đơn hàng và cải thiện trải nghiệm mua sắm</li>
                        <li>Không chia sẻ thông tin cá nhân với bên thứ ba mà không có sự đồng ý của khách hàng</li>
                        <li>Thường xuyên cập nhật và nâng cấp hệ thống bảo mật</li>
                    </ul>
                    <p>Để biết thêm chi tiết, bạn có thể tham khảo Chính sách bảo mật của chúng tôi.</p>
                </div>
            </div>
        </div>

        <div class="contact-box">
            <h3>Không tìm thấy câu trả lời bạn cần?</h3>
            <p>Nếu bạn có bất kỳ câu hỏi nào khác, vui lòng liên hệ với đội ngũ chăm sóc khách hàng của chúng tôi.</p>
            <a href="./contact.html" class="contact-btn">Liên hệ ngay</a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Thông tin</h3>
                    <ul>
                        <li><a href="./about.html">Về chúng tôi</a></li>
                        <li><a href="./contact.html">Liên hệ</a></li>
                        <li><a href="../blog.html">Tin tức & Xu hướng</a></li>
                        <li><a href="./faq.html">Câu hỏi thường gặp</a></li>
                        <li><a href="./stores.html">Hệ thống cửa hàng</a></li>
                        <li><a href="./careers.html">Tuyển dụng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Dịch vụ khách hàng</h3>
                    <ul>
                        <li><a href="./account.html">Tài khoản của tôi</a></li>
                        <li><a href="./orders.html">Theo dõi đơn hàng</a></li>
                        <li><a href="./shipping.html">Chính sách vận chuyển</a></li>
                        <li><a href="./returns.html">Chính sách đổi trả</a></li>
                        <li><a href="./size-guide.html">Hướng dẫn chọn size</a></li>
                        <li><a href="./gift-cards.html">Thẻ quà tặng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Liên hệ với chúng tôi</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                123 Đường Nguyễn Trãi, Quận 1, TP. Hồ Chí Minh
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-text">
                                +84 (0) 123 456 789
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <EMAIL>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                Thứ Hai - Chủ Nhật: 9:00 - 21:00
                            </div>
                        </div>
                    </div>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <div class="footer-newsletter">
                        <h4>Đăng ký nhận tin</h4>
                        <p>Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
                        <form class="footer-newsletter-form">
                            <input type="email" placeholder="Email của bạn" required>
                            <button type="submit"><i class="fas fa-paper-plane"></i></button>
                        </form>
                    </div>
                    <div class="app-download">
                        <h4>Tải ứng dụng</h4>
                        <div class="app-buttons">
                            <a href="#"><img src="../img logo/images.png" alt="App Store"></a>
                            <a href="#"><img src="../img logo/img 2.png" alt="Google Play"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Fashion Store. Tất cả các quyền được bảo lưu. | Thiết kế bởi <a href="#">MindX JSA08</a></p>
            </div>
        </div>
    </footer>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/lucky-wheel.js"></script>
    <script src="../script/chatbot.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/search.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ Toggle
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const answer = this.nextElementSibling;
                    answer.classList.toggle('show');
                });
            });

            // FAQ Category Filter
            const categoryButtons = document.querySelectorAll('.category-btn');
            const faqSections = document.querySelectorAll('.faq-section');

            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    categoryButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get category value
                    const categoryValue = this.getAttribute('data-category');

                    // Show/hide sections based on category
                    if (categoryValue === 'all') {
                        faqSections.forEach(section => {
                            section.style.display = 'block';
                        });
                    } else {
                        faqSections.forEach(section => {
                            if (section.getAttribute('data-category') === categoryValue) {
                                section.style.display = 'block';
                            } else {
                                section.style.display = 'none';
                            }
                        });
                    }
                });
            });

            // FAQ Search
            const searchInput = document.getElementById('faq-search-input');
            const searchButton = document.getElementById('faq-search-btn');
            const faqItems = document.querySelectorAll('.faq-item');

            function searchFAQ() {
                const searchTerm = searchInput.value.toLowerCase().trim();

                if (searchTerm === '') {
                    // Show all sections and items
                    faqSections.forEach(section => {
                        section.style.display = 'block';
                    });

                    faqItems.forEach(item => {
                        item.style.display = 'block';
                    });

                    return;
                }

                // Show all sections
                faqSections.forEach(section => {
                    section.style.display = 'block';
                });

                // Filter items
                let hasResults = false;

                faqItems.forEach(item => {
                    const question = item.querySelector('.faq-question').textContent.toLowerCase();
                    const answer = item.querySelector('.faq-answer').textContent.toLowerCase();

                    if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                        item.style.display = 'block';
                        hasResults = true;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Hide sections with no visible items
                faqSections.forEach(section => {
                    const visibleItems = section.querySelectorAll('.faq-item[style="display: block;"]');
                    if (visibleItems.length === 0) {
                        section.style.display = 'none';
                    }
                });
            }

            searchButton.addEventListener('click', searchFAQ);

            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchFAQ();
                }
            });

            // Hiển thị tên người dùng đã đăng nhập
            function displayUsername() {
                const user = JSON.parse(localStorage.getItem('user'));
                if (user && user.isLoggedIn) {
                    const userIcon = document.querySelector('.nav-icons .nav-icon[title="Tài khoản"]');
                    if (userIcon) {
                        // Thay đổi icon thành tên người dùng
                        const userName = user.name || user.email.split('@')[0];
                        userIcon.innerHTML = `<span class="user-name">${userName}</span>`;
                        userIcon.title = `Xin chào, ${userName}`;

                        // Thêm sự kiện click để hiển thị menu tài khoản
                        userIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showAccountMenu(this);
                        });
                    }
                }
            }

            // Hiển thị menu tài khoản
            function showAccountMenu(element) {
                // Xóa menu cũ nếu có
                const existingMenu = document.querySelector('.account-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }

                // Tạo menu tài khoản
                const accountMenu = document.createElement('div');
                accountMenu.className = 'account-menu';

                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user'));
                const userName = user.name || user.email.split('@')[0];

                // Thiết lập nội dung menu
                accountMenu.innerHTML = `
                    <div class="account-menu-header">
                        <div class="account-menu-user">
                            <div class="account-menu-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="account-menu-info">
                                <div class="account-menu-name">${userName}</div>
                                <div class="account-menu-email">${user.email}</div>
                            </div>
                        </div>
                    </div>
                    <div class="account-menu-body">
                        <a href="./account.html" class="account-menu-item">
                            <i class="fas fa-user"></i> Tài khoản của tôi
                        </a>
                        <a href="./orders.html" class="account-menu-item">
                            <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                        </a>
                        <a href="./wishlist.html" class="account-menu-item">
                            <i class="fas fa-heart"></i> Danh sách yêu thích
                        </a>
                        <a href="./settings.html" class="account-menu-item">
                            <i class="fas fa-cog"></i> Cài đặt
                        </a>
                        <div class="account-menu-divider"></div>
                        <a href="#" class="account-menu-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Đăng xuất
                        </a>
                    </div>
                `;

                // Định vị menu
                const rect = element.getBoundingClientRect();
                accountMenu.style.top = `${rect.bottom + window.scrollY}px`;
                accountMenu.style.right = `${window.innerWidth - rect.right}px`;

                // Thêm vào body
                document.body.appendChild(accountMenu);

                // Hiển thị với hiệu ứng
                setTimeout(() => {
                    accountMenu.classList.add('show');
                }, 10);

                // Thêm sự kiện cho nút đăng xuất
                const logoutBtn = accountMenu.querySelector('.logout-btn');
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });

                // Đóng menu khi click bên ngoài
                document.addEventListener('click', function closeMenu(e) {
                    if (!accountMenu.contains(e.target) && e.target !== element) {
                        accountMenu.classList.remove('show');
                        setTimeout(() => {
                            accountMenu.remove();
                        }, 300);
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }

            // Hàm đăng xuất
            function logout() {
                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user')) || {};

                // Cập nhật trạng thái đăng nhập
                user.isLoggedIn = false;

                // Lưu vào localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Hiển thị thông báo
                alert('Đăng xuất thành công');

                // Khôi phục icon người dùng
                const userIcon = document.querySelector('.nav-icons .nav-icon[title="Xin chào, ' + (user.name || user.email.split('@')[0]) + '"]');
                if (userIcon) {
                    userIcon.innerHTML = '<i class="fas fa-user"></i>';
                    userIcon.title = 'Tài khoản';
                }

                // Chuyển hướng về trang chủ sau một khoảng thời gian ngắn
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            }

            // Gọi hàm hiển thị tên người dùng
            displayUsername();
        });
    </script>
</body>
</html>