/* Checkout Page Styles */

/* General Styles */
:root {
    --primary-color: #ff6b6b;
    --primary-dark: #fa5252;
    --secondary-color: #339af0;
    --text-color: #343a40;
    --text-light: #6c757d;
    --border-color: #e9ecef;
    --bg-light: #f8f9fa;
    --success-color: #40c057;
    --warning-color: #fd7e14;
    --danger-color: #fa5252;
}

/* Breadcrumb */
.breadcrumb-container {
    background-color: var(--bg-light);
    padding: 15px 0;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
}

.breadcrumb li {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

.breadcrumb li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: #adb5bd;
}

.breadcrumb li a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb li a:hover {
    color: var(--primary-color);
}

.breadcrumb li.active {
    color: var(--primary-color);
    font-weight: 500;
}

/* Checkout Container */
.checkout-container {
    padding: 40px 0 80px;
}

.checkout-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

/* Checkout Form */
.checkout-form {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
}

.checkout-title {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.checkout-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
}

/* Form Styles */
.form-section {
    margin-bottom: 30px;
}

.form-section-title {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.form-section-title i {
    margin-right: 10px;
    color: var(--primary-color);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 8px;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

input:focus,
select:focus,
textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    outline: none;
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* Checkbox Style */
.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
    user-select: none;
    margin-bottom: 15px;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 3px;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: #e9ecef;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Radio Button Style */
.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.radio-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
    user-select: none;
}

.radio-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.radio-mark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 50%;
}

.radio-container:hover input ~ .radio-mark {
    background-color: #e9ecef;
}

.radio-container input:checked ~ .radio-mark {
    background-color: white;
    border-color: var(--primary-color);
}

.radio-mark:after {
    content: "";
    position: absolute;
    display: none;
}

.radio-container input:checked ~ .radio-mark:after {
    display: block;
}

.radio-container .radio-mark:after {
    top: 5px;
    left: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
}

/* Address Selection */
.saved-addresses {
    margin-bottom: 20px;
}

.address-item {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.address-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.address-item.selected {
    border-color: var(--primary-color);
    background-color: rgba(255, 107, 107, 0.05);
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.address-name {
    font-weight: 500;
    color: var(--text-color);
}

.default-badge {
    background-color: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    padding: 2px 8px;
    border-radius: 10px;
    margin-left: 10px;
}

.address-details {
    font-size: 0.9rem;
    color: var(--text-light);
}

.address-details p {
    margin: 5px 0;
}

/* Payment Methods */
.payment-methods {
    margin-bottom: 30px;
}

.payment-option {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.payment-option:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.payment-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(255, 107, 107, 0.05);
}

.payment-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.payment-details {
    flex: 1;
}

.payment-title {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 5px;
}

.payment-description {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Order Summary */
.order-summary {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    position: sticky;
    top: 20px;
}

.summary-title {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.summary-items {
    margin-bottom: 30px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.item-name {
    font-size: 0.9rem;
    color: var(--text-light);
}

.item-value {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
}

.summary-item.discount .item-value {
    color: var(--success-color);
}

.summary-total {
    display: flex;
    justify-content: space-between;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.total-label {
    font-size: 1.1rem;
    color: var(--text-color);
    font-weight: 500;
}

.total-value {
    font-size: 1.3rem;
    color: var(--primary-color);
    font-weight: 600;
}

.btn-place-order {
    width: 100%;
    padding: 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.btn-place-order:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.btn-place-order:active {
    transform: translateY(-1px);
}

.back-to-cart {
    text-align: center;
}

.back-to-cart a {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-to-cart a:hover {
    color: var(--primary-color);
}

/* Responsive */
@media (max-width: 992px) {
    .checkout-wrapper {
        grid-template-columns: 1fr;
    }
    
    .order-summary {
        position: static;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .payment-option {
        flex-direction: column;
        align-items: flex-start;
    }
}
