<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/sections.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/wishlist.css">

    <!-- Slick Slider CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>
    <link rel="stylesheet" type="text/css" href="../styles/slider.css"/>

    <title>Danh sách yêu thích | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Hero Banner Slider -->
    <section class="hero-slider">
        <div class="slide">
            <img src="../img banner/img banner 1.avif" alt="Banner 1">
            <div class="slide-content">
                <h1>Danh sách yêu thích</h1>
                <p>Các sản phẩm bạn đã đánh dấu yêu thích</p>
                <div class="slide-buttons">
                    <a href="../index.html" class="btn btn-primary">Tiếp tục mua sắm</a>
                    <a href="../AddCart.html" class="btn btn-secondary">Giỏ hàng</a>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="../img banner/banner 2.avif" alt="Banner 2">
            <div class="slide-content">
                <h1>Sản phẩm yêu thích</h1>
                <p>Lưu lại những sản phẩm bạn quan tâm để mua sau</p>
                <div class="slide-buttons">
                    <a href="../womenproducts-new.html" class="btn btn-primary">Thời trang nữ</a>
                    <a href="../menproducts-new.html" class="btn btn-secondary">Thời trang nam</a>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="../img banner/img banner 3.avif" alt="Banner 3">
            <div class="slide-content">
                <h1>Ưu đãi đặc biệt</h1>
                <p>Giảm giá lên đến 50% cho các sản phẩm yêu thích</p>
                <div class="slide-buttons">
                    <a href="./sale.html" class="btn btn-primary">Xem khuyến mãi</a>
                    <a href="../membership.html" class="btn btn-secondary">Thành viên</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Wishlist Page Content -->
    <div class="wishlist-container">
        <div class="container">
            <div class="wishlist-header">
                <h1>Danh sách yêu thích</h1>
                <p>Các sản phẩm bạn đã đánh dấu yêu thích</p>
            </div>

            <!-- Not Logged In Message -->
            <div class="not-logged-in" id="not-logged-in" style="display: none;">
                <div class="login-message">
                    <h2>Vui lòng đăng nhập</h2>
                    <p>Bạn cần đăng nhập để xem danh sách yêu thích của mình</p>
                    <div class="login-buttons">
                        <a href="login.html" class="btn btn-primary">Đăng nhập</a>
                        <a href="register.html" class="btn btn-secondary">Đăng ký</a>
                    </div>
                </div>
            </div>

            <!-- Wishlist Content (visible when logged in) -->
            <div class="wishlist-content" id="wishlist-content">
                <!-- Empty Wishlist Message -->
                <div class="empty-wishlist" id="empty-wishlist" style="display: none;">
                    <div class="empty-wishlist-icon">
                        <i class="far fa-heart"></i>
                    </div>
                    <h2>Danh sách yêu thích trống</h2>
                    <p>Bạn chưa thêm sản phẩm nào vào danh sách yêu thích</p>
                    <a href="../index.html" class="btn btn-primary">Tiếp tục mua sắm</a>
                </div>

                <!-- Wishlist Items -->
                <div class="wishlist-items" id="wishlist-items">
                    <div class="wishlist-actions">
                        <button class="btn btn-secondary" id="clear-wishlist">Xóa tất cả</button>
                        <button class="btn btn-primary" id="add-all-to-cart">Thêm tất cả vào giỏ hàng</button>
                    </div>

                    <div class="wishlist-grid" id="wishlist-grid">
                        <!-- Wishlist items will be added dynamically with JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/api-service.js"></script>
    <script src="../script/wishlist.js"></script>
    <script src="../script/user-display.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/wishlist-handler.js"></script>
    <script src="../script/marquee.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>
</body>
</html>
