/* Sale Page Styles */
@import url('style.css');

/* Sale Hero Section */
.sale-hero {
    height: 600px;
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../img banner/banner 2.avif');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    margin-bottom: 4rem;
    position: relative;
}

.sale-hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--accent-color) 0%, rgba(0, 0, 0, 0.5) 100%);
    opacity: 0.3;
}

.sale-hero-content {
    max-width: 800px;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

.sale-hero h1 {
    font-family: var(--heading-font);
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.sale-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* Countdown Timer */
.countdown-container {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.countdown-item {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    min-width: 80px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.countdown-item span {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.5rem;
}

.countdown-item p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.8;
}

/* Sale Categories Section */
.sale-categories {
    padding: 4rem 0;
}

.sale-categories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.sale-category-card {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 400px;
}

.sale-category-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.sale-category-card:hover img {
    transform: scale(1.05);
}

.sale-category-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: #fff;
    text-align: center;
}

.sale-category-content h3 {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.sale-category-content p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Sale Products Section */
.sale-products {
    padding: 4rem 0;
}

.product-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--primary-color);
}

.filter-group select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: #fff;
    font-size: 0.9rem;
    min-width: 150px;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Sale Badge */
.product-card .sale-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--accent-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 2;
}

/* Original Price */
.product-card .original-price {
    text-decoration: line-through;
    color: #999;
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

/* Load More Button */
.load-more-container {
    text-align: center;
}

/* Sale Benefits Section */
.sale-benefits {
    padding: 4rem 0;
    background-color: var(--secondary-color);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.benefit-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.benefit-card:hover {
    transform: translateY(-10px);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background-color: var(--accent-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
}

.benefit-card h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.benefit-card p {
    color: var(--text-color);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Newsletter Section */
.newsletter {
    padding: 4rem 0;
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../img banner/banner 1.avif');
    background-size: cover;
    background-position: center;
    color: #fff;
    text-align: center;
}

.newsletter-content {
    max-width: 600px;
    margin: 0 auto;
}

.newsletter h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.newsletter p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    font-size: 1rem;
}

.btn-subscribe {
    background-color: var(--accent-color);
    color: #fff;
    border: none;
    padding: 0 2rem;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    font-weight: 600;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-subscribe:hover {
    background-color: var(--accent-hover);
}

/* Responsive */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .sale-categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .sale-hero h1 {
        font-size: 2.5rem;
    }
    
    .countdown-item {
        min-width: 70px;
    }
    
    .countdown-item span {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .sale-categories-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .product-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .sale-hero {
        height: 500px;
    }
    
    .sale-hero h1 {
        font-size: 2rem;
    }
    
    .countdown-container {
        gap: 0.8rem;
    }
    
    .countdown-item {
        min-width: 60px;
        padding: 0.8rem;
    }
    
    .countdown-item span {
        font-size: 1.5rem;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .newsletter-form input {
        border-radius: var(--border-radius-sm);
        margin-bottom: 1rem;
    }
    
    .btn-subscribe {
        border-radius: var(--border-radius-sm);
        padding: 1rem;
    }
}
