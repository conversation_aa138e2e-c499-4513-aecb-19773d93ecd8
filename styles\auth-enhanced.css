/* Enhanced Authentication Styles */

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #f8f9fa;
    --accent-color: #339af0;
    --success-color: #40c057;
    --warning-color: #fab005;
    --danger-color: #fa5252;
    --text-color: #343a40;
    --light-text: #868e96;
    --border-color: #dee2e6;
    --background-color: #ffffff;
    --hover-color: #ff5252;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* Registration Steps */
.registration-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.registration-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #868e96;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-text {
    font-size: 0.85rem;
    color: #868e96;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background-color: var(--primary-color);
    color: white;
}

.step.active .step-text {
    color: var(--primary-color);
    font-weight: 600;
}

.step.completed .step-number {
    background-color: var(--success-color);
    color: white;
}

.step.completed .step-number::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

/* Form Steps */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn-prev {
    background-color: #e9ecef;
    color: #495057;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-prev:hover {
    background-color: #dee2e6;
}

.btn-next {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-next:hover {
    background-color: var(--hover-color);
}

/* Enhanced Auth Container */
.auth-container {
    padding: 60px 0;
    background-color: var(--secondary-color);
    min-height: 80vh;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/auth-pattern.svg');
    background-size: cover;
    opacity: 0.05;
    z-index: 0;
}

.auth-wrapper {
    position: relative;
    z-index: 1;
    display: flex;
    background-color: var(--background-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    max-width: 1000px;
    margin: 0 auto;
}

/* Form Container */
.auth-form-container {
    flex: 1;
    padding: 40px;
    position: relative;
    overflow: hidden;
}

.auth-header {
    margin-bottom: 30px;
    position: relative;
}

.auth-header h1 {
    font-size: 2rem;
    color: var(--text-color);
    margin-bottom: 10px;
    position: relative;
    display: inline-block;
}

.auth-header h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.auth-header:hover h1::after {
    width: 100%;
}

.auth-header p {
    color: var(--light-text);
    font-size: 1rem;
}

/* Form Styles */
.auth-form {
    margin-bottom: 30px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
    width: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
}

.form-group input:focus + label {
    color: var(--primary-color);
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    outline: none;
}

.required {
    color: var(--danger-color);
}

/* Password Input */
.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--light-text);
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* Password Strength Meter */
.password-strength {
    margin-top: 10px;
}

.strength-meter {
    height: 5px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-meter-fill {
    height: 100%;
    border-radius: 3px;
    transition: var(--transition);
}

.strength-meter-fill[data-strength="0"] {
    width: 0%;
    background-color: transparent;
}

.strength-meter-fill[data-strength="1"] {
    width: 25%;
    background-color: var(--danger-color);
}

.strength-meter-fill[data-strength="2"] {
    width: 50%;
    background-color: var(--warning-color);
}

.strength-meter-fill[data-strength="3"] {
    width: 75%;
    background-color: var(--accent-color);
}

.strength-meter-fill[data-strength="4"] {
    width: 100%;
    background-color: var(--success-color);
}

.strength-text {
    font-size: 0.8rem;
    color: var(--light-text);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
}

.forgot-password {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--hover-color);
    transform: translateY(-2px);
}

.btn-block {
    display: block;
    width: 100%;
}

/* Social Login */
.social-login {
    margin-bottom: 30px;
    text-align: center;
}

.social-login p {
    color: var(--light-text);
    margin-bottom: 15px;
    position: relative;
}

.social-login p::before,
.social-login p::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background-color: var(--border-color);
}

.social-login p::before {
    left: 0;
}

.social-login p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.social-btn {
    flex: 1;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    max-width: 180px;
}

.social-btn i {
    font-size: 1.2rem;
}

.facebook {
    background-color: #1877f2;
    color: white;
}

.facebook:hover {
    background-color: #166fe5;
    transform: translateY(-2px);
}

.google {
    background-color: white;
    color: #4285f4;
    border: 1px solid #dadce0;
}

.google:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

/* Auth Image */
.auth-image {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: none;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 10s ease;
}

.auth-wrapper:hover .auth-image img {
    transform: scale(1.1);
}

.auth-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 40px;
    color: white;
}

.auth-image-overlay h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.auth-image-overlay p {
    font-size: 1rem;
    max-width: 80%;
    line-height: 1.6;
}

/* Benefits Section */
.benefits {
    margin-top: 30px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.benefit-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    margin-top: 20px;
}

.auth-footer p {
    color: var(--light-text);
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Responsive */
@media (min-width: 768px) {
    .auth-image {
        display: block;
    }
}

@media (max-width: 767px) {
    .auth-wrapper {
        flex-direction: column;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .registration-steps {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .registration-steps::before {
        display: none;
    }

    .step {
        flex-direction: row;
        gap: 10px;
    }

    .form-navigation {
        flex-direction: column;
        gap: 10px;
    }

    .btn-prev, .btn-next {
        width: 100%;
    }

    .benefits {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-form-container {
    animation: fadeIn 0.5s ease-out;
}

/* Floating Labels (New Feature) */
.floating-label {
    position: relative;
    margin-bottom: 20px;
}

.floating-label input {
    width: 100%;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    transition: var(--transition);
}

.floating-label label {
    position: absolute;
    top: 15px;
    left: 15px;
    color: var(--light-text);
    transition: var(--transition);
    pointer-events: none;
}

.floating-label input:focus,
.floating-label input:not(:placeholder-shown) {
    border-color: var(--primary-color);
    padding-top: 24px;
    padding-bottom: 6px;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label {
    top: 6px;
    left: 15px;
    font-size: 0.7rem;
    color: var(--primary-color);
}

/* Email Verification */
.email-verification {
    margin-top: 8px;
}

.verify-btn {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0;
    transition: color 0.3s ease;
}

.verify-btn:hover {
    color: #1c7ed6;
    text-decoration: underline;
}

.verify-btn.verified {
    color: var(--success-color);
    cursor: default;
}

.verify-btn.verified:hover {
    text-decoration: none;
}

/* Verification Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--light-text);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 20px;
}

.verification-code {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
}

.code-input {
    width: 40px;
    height: 50px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    transition: border-color 0.3s ease;
}

.code-input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.verification-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.btn-text {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    transition: color 0.3s ease;
}

.btn-text:hover {
    color: #1c7ed6;
    text-decoration: underline;
}

/* Login Methods Tabs */
.login-methods {
    margin-bottom: 30px;
}

.login-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.login-tab {
    flex: 1;
    padding: 12px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--light-text);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Avatar Upload */
.avatar-upload {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 10px;
}

.avatar-preview {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    border: 3px solid var(--primary-color);
}

#avatar-preview {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.avatar-edit {
    position: relative;
}

.avatar-edit input {
    display: none;
}

.avatar-edit label {
    display: inline-block;
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.avatar-edit label:hover {
    background-color: var(--hover-color);
}

/* Gender Options */
.gender-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.gender-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.gender-option input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    outline: none;
    transition: all 0.3s ease;
    position: relative;
}

.gender-option input[type="radio"]:checked {
    border-color: var(--primary-color);
}

.gender-option input[type="radio"]:checked::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

/* Password Requirements */
.password-requirements {
    margin-top: 10px;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.password-requirements li {
    font-size: 0.85rem;
    color: var(--light-text);
    display: flex;
    align-items: center;
    gap: 5px;
}

.password-requirements li.valid {
    color: var(--success-color);
}

.password-requirements li i {
    font-size: 0.8rem;
}

.password-requirements li.valid i {
    color: var(--success-color);
}

/* Security Options */
.security-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.security-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-option input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    outline: none;
    transition: all 0.3s ease;
    position: relative;
}

.security-option input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.security-option input[type="checkbox"]:checked::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.7rem;
}

/* Fashion Preferences */
.fashion-preferences {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 10px;
}

.preference-category h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--text-color);
}

.preference-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.preference-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-preferences {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.size-preferences select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.size-preferences select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Notification Options */
.notification-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.notification-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.login-tab i {
    font-size: 1rem;
}

.login-tab:hover {
    color: var(--primary-color);
}

.login-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.login-tab-content {
    display: none;
}

.login-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* Phone Login */
.phone-input {
    display: flex;
    gap: 10px;
}

.phone-input select {
    width: 150px;
    flex-shrink: 0;
}

.phone-input input {
    flex: 1;
}

.otp-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.otp-input {
    width: 40px;
    height: 50px;
    text-align: center;
    font-size: 1.2rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    transition: var(--transition);
}

.otp-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    outline: none;
}

.otp-timer {
    font-size: 0.9rem;
    color: var(--light-text);
    margin-top: 10px;
    display: none;
}

/* QR Login */
.qr-login-container {
    display: flex;
    gap: 30px;
    align-items: center;
    padding: 20px 0;
}

.qr-code {
    flex: 0 0 200px;
    height: 200px;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-code img {
    max-width: 100%;
    max-height: 100%;
}

.qr-instructions {
    flex: 1;
}

.qr-instructions h3 {
    margin-bottom: 15px;
    color: var(--text-color);
}

.qr-instructions ol {
    padding-left: 20px;
    margin-bottom: 20px;
}

.qr-instructions li {
    margin-bottom: 10px;
    color: var(--text-color);
}

.qr-note {
    font-size: 0.9rem;
    color: var(--light-text);
    margin-bottom: 15px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
}

.modal-close {
    font-size: 1.5rem;
    color: var(--light-text);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin-bottom: 20px;
    color: var(--text-color);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Auth Benefits */
.auth-benefits {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 30px;
    transition: var(--transition);
}

.benefit-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.benefit-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* Apple Button */
.apple {
    background-color: #000;
    color: white;
}

.apple:hover {
    background-color: #333;
    transform: translateY(-2px);
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .qr-login-container {
        flex-direction: column;
        align-items: center;
    }

    .otp-input {
        width: 35px;
        height: 45px;
        font-size: 1rem;
    }

    .social-buttons {
        flex-direction: column;
        align-items: center;
    }

    .social-btn {
        width: 100%;
        max-width: 100%;
    }
}
