// Wishlist Handler - Qu<PERSON>n lý danh sách yêu thích

// Khởi tạo khi trang được tải
document.addEventListener('DOMContentLoaded', function() {
    console.log('Wishlist handler loaded');

    // Khởi tạo danh sách yêu thích nếu chưa có
    initWishlist();

    // Thêm sự kiện cho các nút yêu thích
    setupWishlistButtons();

    // Cập nhật số lượng sản phẩm yêu thích
    updateWishlistCount();
});

// Khởi tạo danh sách yêu thích
function initWishlist() {
    if (!localStorage.getItem('wishlist')) {
        localStorage.setItem('wishlist', JSON.stringify([]));
    }
}

// Thiết lập các nút yêu thích
function setupWishlistButtons() {
    // Tìm tất cả các nút yêu thích (heart icon) - cấu trúc cũ
    const oldWishlistButtons = document.querySelectorAll('.heart-icon');
    console.log('Found old wishlist buttons:', oldWishlistButtons.length);

    // Tìm tất cả các nút yêu thích - cấu trúc mới (product-action-btn)
    const newWishlistButtons = document.querySelectorAll('.product-action-btn[title="Thêm vào yêu thích"]');
    console.log('Found new wishlist buttons:', newWishlistButtons.length);

    // Xử lý các nút yêu thích cấu trúc cũ
    oldWishlistButtons.forEach(button => {
        button.addEventListener('click', handleWishlistToggle);

        // Kiểm tra xem sản phẩm đã trong danh sách yêu thích chưa
        const productCard = button.closest('.product-img').parentElement;
        if (productCard) {
            const productInfo = productCard.querySelector('.products-name');
            if (productInfo) {
                const productName = productInfo.querySelector('p:nth-child(2)');
                if (productName) {
                    const productTitle = productName.textContent.trim();
                    const isInWishlist = checkProductInWishlist(productTitle);

                    // Cập nhật trạng thái nút
                    if (isInWishlist) {
                        button.classList.remove('fa-heart-o');
                        button.classList.add('fa-heart');
                        button.style.color = '#ff6b6b';
                    }
                }
            }
        }
    });

    // Xử lý các nút yêu thích cấu trúc mới
    newWishlistButtons.forEach(button => {
        button.addEventListener('click', handleNewWishlistToggle);

        // Kiểm tra xem sản phẩm đã trong danh sách yêu thích chưa
        const productCard = button.closest('.product-card');
        if (productCard) {
            const productTitle = productCard.querySelector('.product-title').textContent.trim();
            const isInWishlist = checkProductInWishlist(productTitle);

            // Cập nhật trạng thái nút
            const heartIcon = button.querySelector('i');
            if (isInWishlist && heartIcon) {
                heartIcon.classList.remove('far');
                heartIcon.classList.add('fas');
                heartIcon.style.color = '#ff6b6b';
            }
        }
    });
}

// Xử lý khi nhấn nút yêu thích (cấu trúc cũ)
function handleWishlistToggle(event) {
    event.stopPropagation(); // Ngăn sự kiện lan ra ngoài

    console.log('Old wishlist button clicked');

    const button = event.target;
    const productCard = button.closest('.product-img').parentElement;

    if (!productCard) {
        console.error('Product card not found');
        return;
    }

    // Lấy thông tin sản phẩm
    const productImg = productCard.querySelector('.product-img img');
    const productInfo = productCard.querySelector('.products-name');

    if (!productImg || !productInfo) {
        console.error('Product image or info not found');
        return;
    }

    const productName = productInfo.querySelector('p:nth-child(2)');
    const productPrice = productInfo.querySelector('p:nth-child(3)');

    if (!productName || !productPrice) {
        console.error('Product name or price not found');
        return;
    }

    const productTitle = productName.textContent.trim();
    const productPriceText = productPrice.textContent.trim();
    const productImgSrc = productImg.src;

    console.log('Product info:', {
        title: productTitle,
        price: productPriceText,
        image: productImgSrc
    });

    // Tạo đối tượng sản phẩm
    const product = {
        id: generateProductId(),
        title: productTitle,
        name: productTitle,
        price: productPriceText,
        image: productImgSrc,
        addedAt: new Date().toISOString()
    };

    // Kiểm tra xem sản phẩm đã có trong danh sách yêu thích chưa
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    const existingIndex = wishlist.findIndex(item => item.title === product.title);

    if (existingIndex !== -1) {
        // Nếu đã có, xóa khỏi danh sách
        wishlist.splice(existingIndex, 1);
        button.classList.remove('fa-heart');
        button.classList.add('fa-heart-o');
        button.style.color = '';
        showNotification('Đã xóa khỏi danh sách yêu thích', 'info');
        console.log('Removed from wishlist');
    } else {
        // Nếu chưa có, thêm vào danh sách
        wishlist.push(product);
        button.classList.remove('fa-heart-o');
        button.classList.add('fa-heart');
        button.style.color = '#ff6b6b';
        showNotification('Đã thêm vào danh sách yêu thích', 'success');
        console.log('Added to wishlist');
    }

    // Lưu danh sách mới
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    console.log('Wishlist saved:', wishlist);

    // Cập nhật số lượng
    updateWishlistCount();
}

// Xử lý khi nhấn nút yêu thích (cấu trúc mới)
function handleNewWishlistToggle(event) {
    event.stopPropagation(); // Ngăn sự kiện lan ra ngoài

    console.log('New wishlist button clicked');

    // Kiểm tra xem người dùng đã đăng nhập chưa
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || !user.isLoggedIn) {
        showNotification('Vui lòng đăng nhập để thêm sản phẩm vào danh sách yêu thích', 'warning');
        return;
    }

    // Xác định button và icon
    let button = event.target;
    let heartIcon;

    // Nếu click vào icon thay vì button
    if (button.tagName.toLowerCase() === 'i') {
        heartIcon = button;
        button = button.parentElement;
    } else {
        heartIcon = button.querySelector('i');
    }

    const productCard = button.closest('.product-card');

    if (!productCard) {
        console.error('Product card not found');
        return;
    }

    // Lấy thông tin sản phẩm
    const productImg = productCard.querySelector('.product-image img');
    const productTitle = productCard.querySelector('.product-title');
    const productPrice = productCard.querySelector('.current-price');

    if (!productImg || !productTitle || !productPrice) {
        console.error('Product details not found');
        return;
    }

    const productTitleText = productTitle.textContent.trim();
    const productPriceText = productPrice.textContent.trim();
    const productImgSrc = productImg.src;

    console.log('Product info (new structure):', {
        title: productTitleText,
        price: productPriceText,
        image: productImgSrc
    });

    // Tạo đối tượng sản phẩm
    const product = {
        id: generateProductId(),
        title: productTitleText,
        name: productTitleText,
        price: productPriceText,
        image: productImgSrc,
        addedAt: new Date().toISOString()
    };

    // Kiểm tra xem sản phẩm đã có trong danh sách yêu thích chưa
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    const existingIndex = wishlist.findIndex(item => item.title === product.title);

    if (existingIndex !== -1) {
        // Nếu đã có, xóa khỏi danh sách
        wishlist.splice(existingIndex, 1);
        if (heartIcon) {
            heartIcon.classList.remove('fas');
            heartIcon.classList.add('far');
            heartIcon.style.color = '';
        }
        showNotification('Đã xóa khỏi danh sách yêu thích', 'info');
        console.log('Removed from wishlist (new structure)');
    } else {
        // Nếu chưa có, thêm vào danh sách
        wishlist.push(product);
        if (heartIcon) {
            heartIcon.classList.remove('far');
            heartIcon.classList.add('fas');
            heartIcon.style.color = '#ff6b6b';
        }
        showNotification('Đã thêm vào danh sách yêu thích', 'success');
        console.log('Added to wishlist (new structure)');
    }

    // Lưu danh sách mới
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    console.log('Wishlist saved:', wishlist);

    // Cập nhật số lượng
    updateWishlistCount();
}

// Kiểm tra sản phẩm có trong danh sách yêu thích không
function checkProductInWishlist(productTitle) {
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    return wishlist.some(item => item.title === productTitle);
}

// Cập nhật số lượng sản phẩm yêu thích
function updateWishlistCount() {
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    const wishlistCount = document.querySelector('.wishlist-count');

    if (wishlistCount) {
        wishlistCount.textContent = wishlist.length;

        // Hiển thị/ẩn số lượng
        if (wishlist.length > 0) {
            wishlistCount.style.display = 'flex';
        } else {
            wishlistCount.style.display = 'none';
        }
    }
}

// Tạo ID ngẫu nhiên cho sản phẩm
function generateProductId() {
    return Date.now() + Math.random().toString(36).substring(2, 7);
}

// Hiển thị thông báo
function showNotification(message, type = 'info') {
    // Tạo container nếu chưa có
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }

    // Tạo thông báo
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                       type === 'error' ? '#F44336' :
                                       type === 'warning' ? '#FF9800' : '#2196F3';
    notification.style.color = '#fff';
    notification.style.padding = '15px 20px';
    notification.style.marginBottom = '10px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(50px)';
    notification.style.transition = 'opacity 0.3s, transform 0.3s';

    // Icon dựa trên loại thông báo
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
    }

    // Nội dung thông báo
    notification.innerHTML = `${icon} ${message}`;

    // Thêm vào container
    container.appendChild(notification);

    // Hiển thị thông báo với animation
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);

    // Xóa thông báo sau 3 giây
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(50px)';

        // Xóa khỏi DOM sau khi animation hoàn tất
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
