/* Header và Navigation CSS */
@import url('style.css');

/* Announcement Bar */
.announcement-bar {
    background-color: var(--primary-color);
    color: var(--light-text);
    text-align: center;
    padding: 0.6rem 0;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Main Navigation */
.main-nav {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    z-index: 1000;
    transition: all var(--transition-medium);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    padding: 0 15px;
}

/* Logo */
.logo {
    padding: 0.5rem 0;
}

.logo img {
    height: 50px;
    width: auto;
    transition: height var(--transition-medium);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    gap: 2.5rem;
}

.nav-item {
    position: relative;
}

.nav-item > a {
    display: block;
    padding: 0.5rem 0;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    color: var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
}

.nav-item > a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
}

.nav-item > a:hover {
    color: var(--accent-color);
}

.nav-item > a:hover::after {
    width: 100%;
}

/* Dropdown Menu */
.has-dropdown::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-left: 5px;
    font-size: 0.8rem;
    transition: transform var(--transition-fast);
}

.has-dropdown:hover::after {
    transform: rotate(180deg);
}

.has-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: -20px;
    background-color: #fff;
    min-width: 800px;
    display: flex;
    padding: 2rem;
    box-shadow: var(--box-shadow);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all var(--transition-medium);
    z-index: 100;
    border-radius: var(--border-radius-sm);
}

.dropdown-column {
    flex: 1;
    padding: 0 1rem;
}

.dropdown-column h3 {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 8px;
}

.dropdown-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--accent-color);
}

.dropdown-column ul li {
    margin-bottom: 0.7rem;
}

.dropdown-column ul li a {
    font-size: 0.9rem;
    color: #666;
    transition: color var(--transition-fast), transform var(--transition-fast);
    display: inline-block;
}

.dropdown-column ul li a:hover {
    color: var(--accent-color);
    transform: translateX(5px);
}

.dropdown-featured {
    flex: 1.2;
}

.dropdown-featured img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    margin-bottom: 1rem;
    border-radius: var(--border-radius-sm);
    transition: transform var(--transition-medium);
}

.dropdown-featured:hover img {
    transform: scale(1.03);
}

.dropdown-featured h4 {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
}

/* Right Navigation */
.nav-right {
    display: flex;
    align-items: center;
    gap: 1.8rem;
}

.search-box {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    transition: border-color var(--transition-fast);
}

.search-box:focus-within {
    border-color: var(--accent-color);
}

.search-box input {
    border: none;
    background: transparent;
    padding: 0.5rem;
    font-family: var(--body-font);
    font-size: 0.9rem;
    width: 180px;
    color: var(--text-color);
}

.search-box input:focus {
    outline: none;
}

.search-box button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    color: var(--primary-color);
    transition: color var(--transition-fast);
}

.search-box button:hover {
    color: var(--accent-color);
}

.nav-icons {
    display: flex;
    gap: 1.8rem;
}

.nav-icon {
    position: relative;
    font-size: 1.3rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.nav-icon:hover {
    color: var(--accent-color);
    transform: translateY(-3px);
    background-color: rgba(0, 0, 0, 0.03);
}

.nav-icon:active {
    transform: translateY(0);
}

.cart-count, .wishlist-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--accent-color);
    color: var(--light-text);
    font-size: 0.7rem;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb), 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--accent-color-rgb), 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb), 0);
    }
}

.wishlist-icon {
    position: relative;
}

/* Username Display */
.nav-icon.user-logged-in {
    width: auto;
    min-width: 40px;
    padding: 0 12px;
    border-radius: 20px;
    background-color: rgba(var(--primary-color-rgb, 44, 62, 80), 0.1);
    border: 1px solid rgba(var(--primary-color-rgb, 44, 62, 80), 0.2);
    transition: all 0.3s ease;
}

.nav-icon.user-logged-in:hover {
    background-color: rgba(var(--accent-color-rgb, 231, 76, 60), 0.1);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.username-display {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}

.nav-icon.user-logged-in:hover .username-display {
    color: var(--accent-color);
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary-color);
    transition: color var(--transition-fast);
}

.menu-toggle:hover {
    color: var(--accent-color);
}

/* Sticky Navigation */
.sticky-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    animation: slideDown 0.3s ease-out;
}

.sticky-nav .nav-wrapper {
    height: 70px;
}

.sticky-nav .logo img {
    height: 40px;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .dropdown-menu {
        min-width: 600px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 80%;
        height: calc(100vh - 80px);
        background-color: #fff;
        flex-direction: column;
        gap: 0;
        padding: 2rem;
        transition: left var(--transition-medium);
        overflow-y: auto;
        z-index: 1000;
        box-shadow: var(--box-shadow);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item > a {
        padding: 1rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .dropdown-menu {
        position: static;
        flex-direction: column;
        min-width: auto;
        box-shadow: none;
        padding: 0 0 0 1rem;
        opacity: 1;
        visibility: visible;
        transform: none;
        display: none;
    }

    .dropdown-column {
        padding: 1rem 0;
    }

    .menu-toggle {
        display: block;
    }

    .search-box {
        display: none;
    }

    .sticky-nav .nav-menu {
        top: 70px;
    }
}

@media (max-width: 576px) {
    .nav-wrapper {
        height: 70px;
    }

    .logo img {
        height: 40px;
    }

    .nav-menu {
        top: 70px;
        width: 100%;
    }

    .nav-right {
        gap: 1rem;
    }

    .nav-icon {
        font-size: 1.2rem;
    }
}
