<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">

    <title>Thanh toán | Fashion Store</title>
    <style>
        /* Checkout Styles */
        .checkout-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }

        .checkout-header {
            background-color: #f8f9fa;
            padding: 30px 0;
            margin-bottom: 30px;
            text-align: center;
        }

        .checkout-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }

        .checkout-breadcrumb {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 15px;
            font-size: 14px;
        }

        .checkout-breadcrumb a {
            color: #666;
            text-decoration: none;
        }

        .checkout-breadcrumb a:hover {
            color: #ff6b6b;
        }

        .checkout-breadcrumb .separator {
            margin: 0 10px;
            color: #ccc;
        }

        .checkout-breadcrumb .current {
            color: #ff6b6b;
            font-weight: 500;
        }

        .checkout-content {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
        }

        .checkout-form {
            flex: 1;
            min-width: 300px;
        }

        .order-summary {
            width: 350px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .required {
            color: #ff6b6b;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            border-color: #ff6b6b;
            outline: none;
        }

        .payment-methods {
            margin-top: 20px;
        }

        .payment-method {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .payment-method:hover {
            border-color: #ff6b6b;
            background-color: #fff9f9;
        }

        .payment-method input {
            margin-right: 10px;
            width: auto;
        }

        .payment-method label {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            cursor: pointer;
            width: 100%;
        }

        .payment-icon {
            margin-right: 10px;
            font-size: 18px;
            color: #666;
        }

        .payment-name {
            font-weight: 500;
        }

        .order-items {
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .order-item-info {
            flex: 1;
        }

        .order-item-name {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .order-item-variant {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .order-item-quantity {
            font-size: 12px;
            color: #666;
        }

        .order-item-price {
            font-weight: 500;
        }

        .order-totals {
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        .order-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .order-row.total {
            font-size: 18px;
            font-weight: 600;
            color: #ff6b6b;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }

        .btn-place-order {
            display: block;
            width: 100%;
            padding: 15px;
            background-color: #ff6b6b;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 20px;
        }

        .btn-place-order:hover {
            background-color: #ff5252;
        }

        .secure-checkout {
            margin-top: 20px;
            font-size: 13px;
            color: #666;
        }

        .secure-checkout p {
            margin-bottom: 8px;
        }

        .secure-checkout i {
            margin-right: 5px;
            color: #4CAF50;
        }

        @media (max-width: 768px) {
            .checkout-content {
                flex-direction: column;
            }

            .order-summary {
                width: 100%;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Checkout Header -->
    <div class="checkout-header">
        <div class="container">
            <h1>Thanh toán</h1>
            <p>Hoàn tất thông tin đơn hàng của bạn</p>

            <div class="checkout-breadcrumb">
                <a href="index.html">Trang chủ</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <a href="AddCart.html">Giỏ hàng</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <span class="current">Thanh toán</span>
            </div>
        </div>
    </div>

    <!-- Main Checkout Content -->
    <div class="checkout-container">
        <div class="checkout-content">
            <!-- Checkout Form -->
            <div class="checkout-form">
                <h2>Thông tin giao hàng</h2>

                <form id="checkout-form">
                    <div class="form-group">
                        <label for="fullname">Họ và tên <span class="required">*</span></label>
                        <input type="text" id="fullname" name="name" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email <span class="required">*</span></label>
                            <input type="email" id="email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">Số điện thoại <span class="required">*</span></label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address">Địa chỉ <span class="required">*</span></label>
                        <input type="text" id="address" name="address" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="city">Tỉnh/Thành phố <span class="required">*</span></label>
                            <select id="city" name="city" required>
                                <option value="">Chọn tỉnh/thành phố</option>
                                <option value="hanoi">Hà Nội</option>
                                <option value="hcm">TP. Hồ Chí Minh</option>
                                <option value="danang">Đà Nẵng</option>
                                <option value="other">Khác</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="district">Quận/Huyện <span class="required">*</span></label>
                            <select id="district" name="district" required>
                                <option value="">Chọn quận/huyện</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="note">Ghi chú</label>
                        <textarea id="note" name="note" rows="3" placeholder="Ghi chú về đơn hàng, ví dụ: thời gian hay chỉ dẫn địa điểm giao hàng chi tiết hơn."></textarea>
                    </div>
                </form>

                <h2>Phương thức thanh toán</h2>

                <div class="payment-methods">
                    <div class="payment-method">
                        <input type="radio" id="cod" name="payment" value="cod" checked>
                        <label for="cod">
                            <span class="payment-icon"><i class="fas fa-money-bill-wave"></i></span>
                            <span class="payment-name">Thanh toán khi nhận hàng (COD)</span>
                        </label>
                    </div>

                    <div class="payment-method">
                        <input type="radio" id="bank-transfer" name="payment" value="bank-transfer">
                        <label for="bank-transfer">
                            <span class="payment-icon"><i class="fas fa-university"></i></span>
                            <span class="payment-name">Chuyển khoản ngân hàng</span>
                        </label>
                    </div>

                    <div class="payment-method">
                        <input type="radio" id="credit-card" name="payment" value="credit-card">
                        <label for="credit-card">
                            <span class="payment-icon"><i class="far fa-credit-card"></i></span>
                            <span class="payment-name">Thẻ tín dụng/Ghi nợ</span>
                        </label>
                    </div>

                    <div class="payment-method">
                        <input type="radio" id="momo" name="payment" value="momo">
                        <label for="momo">
                            <span class="payment-icon"><i class="fas fa-wallet"></i></span>
                            <span class="payment-name">Ví điện tử MoMo</span>

                        </label>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <h2>Đơn hàng của bạn</h2>

                <div class="order-items" id="order-items">
                    <!-- Order items will be added dynamically with JavaScript -->
                </div>

                <div class="order-totals">
                    <div class="order-row">
                        <span>Tạm tính:</span>
                        <span id="subtotal"></span>
                    </div>

                    <div class="order-row">
                        <span>Phí vận chuyển:</span>
                        <span>Miễn phí</span>
                    </div>

                    <div class="order-row">
                        <span>Giảm giá:</span>
                        <span id="discount">0đ</span>
                    </div>

                    <div class="order-row total">
                        <span>Tổng cộng:</span>
                        <span id="total"></span>
                    </div>
                </div>

                <button id="place-order" class="btn-place-order">
                    <i class="fas fa-lock"></i> Đặt hàng
                </button>

                <div class="secure-checkout">
                    <p><i class="fas fa-shield-alt"></i> Thanh toán an toàn & bảo mật</p>
                    <p><i class="fas fa-truck"></i> Giao hàng miễn phí cho đơn hàng từ 500.000đ</p>
                    <p><i class="fas fa-undo"></i> Đổi trả trong vòng 30 ngày</p>
                </div>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Hiển thị thông tin đơn hàng
        document.addEventListener('DOMContentLoaded', function() {
            // Kiểm tra đăng nhập
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user || !user.isLoggedIn) {
                // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
                localStorage.setItem('redirectAfterLogin', window.location.href);

                // Chuyển hướng đến trang đăng nhập
                window.location.href = './pages/login.html';
                return;
            }

            // Lấy thông tin giỏ hàng từ localStorage
            const cart = JSON.parse(localStorage.getItem('cart')) || [];

            // Lấy giá trị từ localStorage và chuyển đổi thành số
            const cartTotal = parseInt(localStorage.getItem('cart_total') || '0');
            const discount = parseInt(localStorage.getItem('cart_discount') || '0');
            const finalTotal = parseInt(localStorage.getItem('cart_final') || cartTotal.toString());

            console.log('checkout.html: Đã lấy giá trị từ localStorage:', {
                cart_total: cartTotal,
                cart_discount: discount,
                cart_final: finalTotal
            });

            // Kiểm tra nếu giỏ hàng trống thì chuyển về trang giỏ hàng
            if (cart.length === 0) {
                window.location.href = 'AddCart.html';
                return;
            }

            // Hiển thị các sản phẩm trong đơn hàng
            const orderItems = document.getElementById('order-items');

            cart.forEach(item => {
                const orderItem = document.createElement('div');
                orderItem.className = 'order-item';

                orderItem.innerHTML = `
                    <div class="order-item-info">
                        <span class="order-item-name">${item.name}</span>
                        <span class="order-item-variant">${item.size ? 'Size: ' + item.size : ''} ${item.color ? '| Màu: ' + item.color : ''}</span>
                        <span class="order-item-quantity">x${item.quantity || 1}</span>
                    </div>
                    <div class="order-item-price">${item.price}</div>
                `;

                orderItems.appendChild(orderItem);
            });

            // Hiển thị tổng tiền
            document.getElementById('subtotal').textContent = parseInt(cartTotal).toLocaleString() + 'đ';
            document.getElementById('discount').textContent = parseInt(discount) > 0 ? '-' + parseInt(discount).toLocaleString() + 'đ' : '0đ';
            document.getElementById('total').textContent = parseInt(finalTotal).toLocaleString() + 'đ';

            // Xử lý nút đặt hàng
            document.getElementById('place-order').addEventListener('click', function() {
                const form = document.getElementById('checkout-form');

                if (form.checkValidity()) {
                    // Lưu thông tin đơn hàng vào localStorage
                    const orderInfo = {
                        customer: {
                            fullname: document.getElementById('fullname').value,
                            email: document.getElementById('email').value,
                            phone: document.getElementById('phone').value,
                            address: document.getElementById('address').value,
                            city: document.getElementById('city').value,
                            district: document.getElementById('district').value,
                            note: document.getElementById('note').value
                        },
                        payment: document.querySelector('input[name="payment"]:checked').value,
                        items: cart,
                        subtotal: parseInt(cartTotal),
                        discount: parseInt(discount),
                        total: parseInt(finalTotal),
                        orderDate: new Date().toISOString(),
                        orderNumber: 'ORD' + Date.now()
                    };

                    localStorage.setItem('order_info', JSON.stringify(orderInfo));

                    // Lưu địa chỉ giao hàng để sử dụng ở trang thanh toán
                    const shippingAddress = {
                        fullname: document.getElementById('fullname').value,
                        email: document.getElementById('email').value,
                        phone: document.getElementById('phone').value,
                        address: document.getElementById('address').value,
                        city: document.getElementById('city').value,
                        district: document.getElementById('district').value
                    };
                    localStorage.setItem('shippingAddress', JSON.stringify(shippingAddress));

                    // Kiểm tra phương thức thanh toán
                    const paymentMethod = document.querySelector('input[name="payment"]:checked').value;

                    // Nếu là thẻ tín dụng hoặc chuyển khoản, chuyển đến trang thanh toán
                    if (paymentMethod === 'credit-card' || paymentMethod === 'bank-transfer' || paymentMethod === 'momo') {
                        window.location.href = 'payment.html';
                    } else {
                        // Nếu là COD, chuyển đến trang xác nhận đơn hàng
                        window.location.href = 'confirm.html';
                    }
                } else {
                    // Hiển thị thông báo lỗi
                    form.reportValidity();
                }
            });

            // Xử lý thay đổi tỉnh/thành phố
            document.getElementById('city').addEventListener('change', function() {
                const city = this.value;
                const districtSelect = document.getElementById('district');

                // Xóa tất cả các option cũ
                districtSelect.innerHTML = '<option value="">Chọn quận/huyện</option>';

                // Thêm các option mới dựa trên tỉnh/thành phố đã chọn
                if (city === 'hanoi') {
                    const districts = ['Ba Đình', 'Hoàn Kiếm', 'Hai Bà Trưng', 'Đống Đa', 'Tây Hồ', 'Cầu Giấy', 'Thanh Xuân', 'Hoàng Mai'];
                    districts.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.toLowerCase().replace(/\s/g, '-');
                        option.textContent = district;
                        districtSelect.appendChild(option);
                    });
                } else if (city === 'hcm') {
                    const districts = ['Quận 1', 'Quận 2', 'Quận 3', 'Quận 4', 'Quận 5', 'Quận 6', 'Quận 7', 'Quận 8', 'Quận 9', 'Quận 10', 'Quận 11', 'Quận 12', 'Thủ Đức'];
                    districts.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.toLowerCase().replace(/\s/g, '-');
                        option.textContent = district;
                        districtSelect.appendChild(option);
                    });
                } else if (city === 'danang') {
                    const districts = ['Hải Châu', 'Thanh Khê', 'Sơn Trà', 'Ngũ Hành Sơn', 'Liên Chiểu', 'Cẩm Lệ'];
                    districts.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.toLowerCase().replace(/\s/g, '-');
                        option.textContent = district;
                        districtSelect.appendChild(option);
                    });
                }
            });
        });
    </script>
</body>
</html>
