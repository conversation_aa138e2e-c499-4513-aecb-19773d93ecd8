# Fashion Store Website

Website bán hàng thời trang với đầy đủ chức năng và giao diện đẹp mắt.

## Tính năng

- Hiển thị sản phẩm theo danh mục (nam, nữ, đ<PERSON> lót)
- Tì<PERSON> kiếm sản phẩm
- Giỏ hàng và thanh toán
- Danh sách yêu thích
- Đăng ký và đăng nhập
- Quản lý tài khoản
- API thực tế (không giả lập)

## Cấu trúc thư mục

```
/
├── api/                  # API server
│   ├── data/             # Dữ liệu mẫu
│   ├── routes/           # Các route API
│   ├── server.js         # File chính của server
│   └── package.json      # Cấu hình npm
├── img/                  # Hình ảnh
├── pages/                # Các trang con
├── script/               # JavaScript
├── styles/               # CSS
└── index.html            # Trang chủ
```

## Cài đặt và chạy

### 1. Cài đặt website

Không cần cài đặt gì đặc biệt, chỉ cần mở file `index.html` trong trình duyệt.

### 2. Cài đặt và chạy API server

```bash
# Di chuyển vào thư mục api
cd api

# Cài đặt các gói phụ thuộc
npm install

# Chạy server
npm start
```

API server sẽ chạy tại địa chỉ `http://localhost:3000`.

## API Endpoints

### Sản phẩm

- `GET /api/products` - Lấy tất cả sản phẩm
  - Tham số query:
    - `category` - Lọc theo danh mục (ví dụ: women, men, underwear)
    - `subcategory` - Lọc theo danh mục con (ví dụ: shirt, pants, dress)
    - `search` - Tìm kiếm theo tên
    - `sort` - Sắp xếp theo (price-asc, price-desc, newest, bestseller, rating)
    - `limit` - Giới hạn số lượng kết quả

- `GET /api/products/:id` - Lấy sản phẩm theo ID
- `GET /api/products/:id/related` - Lấy các sản phẩm liên quan

### Người dùng

- `POST /api/users/login` - Đăng nhập
  - Body: `{ username, password }`

- `POST /api/users/register` - Đăng ký
  - Body: `{ username, email, password, fullName, phone }`

- `GET /api/users/profile` - Lấy thông tin người dùng (yêu cầu xác thực)
- `PUT /api/users/profile` - Cập nhật thông tin người dùng (yêu cầu xác thực)
  - Body: `{ fullName, phone, address, gender, birthDate }`

### Giỏ hàng

- `GET /api/cart` - Lấy giỏ hàng (yêu cầu xác thực)
- `POST /api/cart/add` - Thêm sản phẩm vào giỏ hàng (yêu cầu xác thực)
  - Body: `{ productId, quantity, color, size }`

- `PUT /api/cart/update/:itemId` - Cập nhật sản phẩm trong giỏ hàng (yêu cầu xác thực)
  - Body: `{ quantity, color, size }`

- `DELETE /api/cart/remove/:itemId` - Xóa sản phẩm khỏi giỏ hàng (yêu cầu xác thực)
- `DELETE /api/cart/clear` - Xóa tất cả sản phẩm trong giỏ hàng (yêu cầu xác thực)

### Danh sách yêu thích

- `GET /api/wishlist` - Lấy danh sách yêu thích (yêu cầu xác thực)
- `POST /api/wishlist/add` - Thêm sản phẩm vào danh sách yêu thích (yêu cầu xác thực)
  - Body: `{ productId }`

- `POST /api/wishlist/toggle` - Thêm/xóa sản phẩm trong danh sách yêu thích (yêu cầu xác thực)
  - Body: `{ productId }`

- `DELETE /api/wishlist/remove/:itemId` - Xóa sản phẩm khỏi danh sách yêu thích (yêu cầu xác thực)
- `DELETE /api/wishlist/clear` - Xóa tất cả sản phẩm trong danh sách yêu thích (yêu cầu xác thực)

## Xác thực

Hầu hết các endpoint yêu cầu xác thực. Để xác thực, thêm token JWT vào header Authorization:

```
Authorization: Bearer <token>
```

Bạn có thể lấy token bằng cách đăng nhập hoặc đăng ký.
