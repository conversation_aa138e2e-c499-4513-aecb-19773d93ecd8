/* Virtual Try-On Styles */

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #f8f9fa;
    --accent-color: #339af0;
    --success-color: #40c057;
    --warning-color: #fab005;
    --danger-color: #fa5252;
    --text-color: #343a40;
    --light-text: #868e96;
    --border-color: #dee2e6;
    --background-color: #ffffff;
    --hover-color: #ff5252;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* Main Container */
.virtual-try-on-container {
    padding: 40px 0;
    background-color: var(--secondary-color);
}

.try-on-header {
    text-align: center;
    margin-bottom: 40px;
}

.try-on-header h1 {
    font-size: 2.5rem;
    color: var(--text-color);
    margin-bottom: 10px;
    position: relative;
    display: inline-block;
}

.try-on-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

.try-on-header p {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
    margin: 0 auto;
}

/* Try-On Content Layout */
.try-on-content {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
}

.try-on-sidebar {
    flex: 0 0 300px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.try-on-preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Avatar Section */
.avatar-section, .clothing-section {
    background-color: var(--background-color);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.avatar-section h3, .clothing-section h3 {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.avatar-options {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.avatar-option {
    flex: 1;
    text-align: center;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.avatar-option:hover {
    background-color: var(--secondary-color);
}

.avatar-option.active {
    border-color: var(--primary-color);
    background-color: rgba(255, 107, 107, 0.1);
}

.avatar-option img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 8px;
    object-fit: cover;
}

.avatar-option span {
    display: block;
    font-size: 0.9rem;
    color: var(--text-color);
}

/* Avatar Customization */
.avatar-customization {
    margin-top: 20px;
}

.avatar-customization h4 {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 15px;
}

.customization-option {
    margin-bottom: 15px;
}

.customization-option label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 8px;
}

.customization-option input[type="range"] {
    width: 100%;
    height: 6px;
    -webkit-appearance: none;
    background: var(--border-color);
    border-radius: 3px;
    outline: none;
}

.customization-option input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.customization-option span {
    display: inline-block;
    margin-top: 5px;
    font-size: 0.9rem;
    color: var(--light-text);
}

.skin-tone-options {
    display: flex;
    gap: 10px;
}

.skin-tone {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.skin-tone[data-tone="light"] {
    background-color: #f8d5c2;
}

.skin-tone[data-tone="medium"] {
    background-color: #e0b193;
}

.skin-tone[data-tone="dark"] {
    background-color: #a67358;
}

.skin-tone.active {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

/* Clothing Section */
.clothing-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.category-btn {
    padding: 8px 12px;
    border-radius: 20px;
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: none;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.category-btn:hover {
    background-color: var(--border-color);
}

.category-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.clothing-items {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

.clothing-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
}

.clothing-item:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.clothing-item.selected {
    border-color: var(--primary-color);
    background-color: rgba(255, 107, 107, 0.1);
}

.clothing-item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
}

.clothing-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.clothing-item-info {
    flex: 1;
}

.clothing-item-name {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 5px;
}

.clothing-item-price {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

/* Preview Container */
.preview-container {
    background-color: var(--background-color);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--box-shadow);
    position: relative;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.avatar-container img {
    height: 90%;
    max-width: 100%;
    object-fit: contain;
}

.preview-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 8px 15px;
    border-radius: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.preview-controls button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-controls button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Outfit Summary */
.outfit-summary {
    background-color: var(--background-color);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.outfit-summary h3 {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.selected-items {
    margin-bottom: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.no-items {
    color: var(--light-text);
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

.selected-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
    position: relative;
}

.selected-item-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
}

.selected-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.selected-item-info {
    flex: 1;
}

.selected-item-name {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 5px;
}

.selected-item-price {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

.remove-item {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--danger-color);
    color: white;
    border: none;
    font-size: 0.8rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.outfit-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.outfit-actions button {
    flex: 1;
    padding: 12px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: var(--hover-color);
}

/* Recommendations */
.try-on-recommendations {
    margin-top: 40px;
}

.try-on-recommendations h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.try-on-recommendations h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary-color);
}

.recommendation-slider {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding: 10px 0 20px;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.recommendation-slider::-webkit-scrollbar {
    height: 6px;
}

.recommendation-slider::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

.recommendation-slider::-webkit-scrollbar-track {
    background-color: transparent;
}

.recommendation-item {
    flex: 0 0 200px;
    background-color: var(--background-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.recommendation-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.recommendation-image {
    height: 200px;
    overflow: hidden;
}

.recommendation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.recommendation-item:hover .recommendation-image img {
    transform: scale(1.05);
}

.recommendation-info {
    padding: 15px;
}

.recommendation-name {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommendation-price {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

.try-btn {
    display: block;
    width: 100%;
    padding: 8px 0;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    margin-top: 10px;
}

.try-btn:hover {
    background-color: var(--hover-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
}

.modal-close {
    font-size: 1.5rem;
    color: var(--light-text);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 20px;
}

.saved-outfit-preview {
    margin: 20px 0;
    text-align: center;
}

.saved-outfit-preview img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
}

.outfit-share-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.share-btn {
    flex: 1;
    padding: 10px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: white;
    border: none;
}

.facebook {
    background-color: #1877f2;
}

.instagram {
    background-color: #e1306c;
}

.twitter {
    background-color: #1da1f2;
}

.pinterest {
    background-color: #bd081c;
}

.share-btn:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 992px) {
    .try-on-content {
        flex-direction: column;
    }
    
    .try-on-sidebar {
        flex-direction: row;
        flex: none;
    }
    
    .avatar-section, .clothing-section {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .try-on-sidebar {
        flex-direction: column;
    }
    
    .preview-container {
        height: 400px;
    }
    
    .outfit-actions {
        flex-direction: column;
    }
    
    .outfit-actions button {
        width: 100%;
    }
}
