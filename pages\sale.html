<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/sections.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/product.css">
    <link rel="stylesheet" type="text/css" href="../styles/sale.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <title>Khuyến mãi | Fashion Store</title>

    <style>
        /* Đảm bảo màu sắc giống với index.html */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #f5f5f5;
            --accent-color: #e74c3c;
            --accent-hover: #c0392b;
            --text-color: #333;
            --light-text: #fff;
            --dark-text: #222;
            --border-color: #ddd;
            --hover-color: #f9f9f9;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Navbar styles */
        .announcement-bar {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .main-nav {
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-item > a {
            color: var(--primary-color);
        }

        .nav-item > a:hover {
            color: var(--accent-color);
        }

        .nav-icon {
            color: var(--primary-color);
        }

        .nav-icon:hover {
            color: var(--accent-color);
        }

        /* Footer styles */
        .footer {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .footer::before {
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
        }

        .footer-column h3 {
            color: var(--light-text);
        }

        .footer-column h3::after {
            background-color: var(--accent-color);
        }

        .footer-column ul li a {
            color: rgba(255, 255, 255, 0.7);
        }

        .footer-column ul li a::before {
            color: var(--accent-color);
        }

        .footer-column ul li a:hover {
            color: var(--light-text);
        }

        .social-icon {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--light-text);
        }

        .social-icon:hover {
            background-color: var(--accent-color);
            color: var(--light-text);
        }

        /* Hiển thị tên đăng nhập */
        .user-account {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--primary-color);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .user-account i {
            font-size: 1.3rem;
        }

        .user-account:hover {
            color: var(--accent-color);
        }

        .user-account.hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Sale Hero Section -->
    <section class="sale-hero">
        <div class="sale-hero-content">
            <h1 data-aos="fade-up">Khuyến mãi đặc biệt</h1>
            <p data-aos="fade-up" data-aos-delay="200">Giảm giá lên đến 50% cho các sản phẩm mùa hè</p>
            <div class="countdown-container" data-aos="fade-up" data-aos-delay="400">
                <div class="countdown-item">
                    <span id="days">00</span>
                    <p>Ngày</p>
                </div>
                <div class="countdown-item">
                    <span id="hours">00</span>
                    <p>Giờ</p>
                </div>
                <div class="countdown-item">
                    <span id="minutes">00</span>
                    <p>Phút</p>
                </div>
                <div class="countdown-item">
                    <span id="seconds">00</span>
                    <p>Giây</p>
                </div>
            </div>
            <a href="#sale-products" class="btn btn-primary" data-aos="fade-up" data-aos-delay="600">Mua ngay</a>
        </div>
    </section>

    <!-- Sale Categories Section -->
    <section class="sale-categories">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Danh mục khuyến mãi</h2>
            <div class="sale-categories-grid">
                <div class="sale-category-card" data-aos="fade-up" data-aos-delay="100">
                    <img src="../img womens/women 15.webp" alt="Women's Sale">
                    <div class="sale-category-content">
                        <h3>Thời trang nữ</h3>
                        <p>Giảm giá lên đến 40%</p>
                        <a href="../womenproducts.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
                <div class="sale-category-card" data-aos="fade-up" data-aos-delay="200">
                    <img src="../img mens/men 10.jpg" alt="Men's Sale">
                    <div class="sale-category-content">
                        <h3>Thời trang nam</h3>
                        <p>Giảm giá lên đến 35%</p>
                        <a href="../menproducts.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
                <div class="sale-category-card" data-aos="fade-up" data-aos-delay="300">
                    <img src="../img womens/women 12.webp" alt="Kids Sale">
                    <div class="sale-category-content">
                        <h3>Thời trang trẻ em</h3>
                        <p>Giảm giá lên đến 50%</p>
                        <a href="kids.html" class="btn-shop">Mua ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sale Products Section -->
    <section id="sale-products" class="sale-products">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Sản phẩm khuyến mãi</h2>

            <!-- Filter and Sort -->
            <div class="product-filters" data-aos="fade-up">
                <div class="filter-group">
                    <label>Danh mục:</label>
                    <select id="category-filter">
                        <option value="all">Tất cả</option>
                        <option value="women">Nữ</option>
                        <option value="men">Nam</option>
                        <option value="kids">Trẻ em</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Mức giảm giá:</label>
                    <select id="discount-filter">
                        <option value="all">Tất cả</option>
                        <option value="30">Giảm 30% trở lên</option>
                        <option value="40">Giảm 40% trở lên</option>
                        <option value="50">Giảm 50% trở lên</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Sắp xếp theo:</label>
                    <select id="sort-filter">
                        <option value="discount-desc">Giảm giá: Cao đến thấp</option>
                        <option value="price-asc">Giá: Thấp đến cao</option>
                        <option value="price-desc">Giá: Cao đến thấp</option>
                        <option value="newest">Mới nhất</option>
                    </select>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="products-container" data-aos="fade-up">
                <!-- Products will be added dynamically with JavaScript -->
            </div>

            <!-- Load More Button -->
            <div class="load-more-container" data-aos="fade-up">
                <button id="load-more" class="btn btn-secondary">Xem thêm</button>
            </div>
        </div>
    </section>

    <!-- Sale Benefits Section -->
    <section class="sale-benefits bg-light">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Ưu đãi đặc biệt</h2>
            <div class="benefits-grid">
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="benefit-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h3>Miễn phí vận chuyển</h3>
                    <p>Miễn phí vận chuyển cho tất cả đơn hàng khuyến mãi trên 300.000đ</p>
                </div>
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="benefit-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h3>Đổi trả dễ dàng</h3>
                    <p>Đổi trả miễn phí trong vòng 30 ngày kể từ ngày mua hàng</p>
                </div>
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="benefit-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <h3>Quà tặng hấp dẫn</h3>
                    <p>Nhận quà tặng đặc biệt cho đơn hàng khuyến mãi trên 500.000đ</p>
                </div>
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="benefit-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <h3>Giảm thêm 10%</h3>
                    <p>Giảm thêm 10% cho thành viên VIP khi mua sản phẩm khuyến mãi</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter">
        <div class="container">
            <div class="newsletter-content" data-aos="fade-up">
                <h2>Đăng ký nhận thông tin khuyến mãi</h2>
                <p>Nhận thông tin về sản phẩm khuyến mãi và ưu đãi đặc biệt</p>
                <form class="newsletter-form">
                    <input type="email" placeholder="Địa chỉ email của bạn" required>
                    <button type="submit" class="btn-subscribe">Đăng ký</button>
                </form>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../script/main-new.js"></script>
    <script src="../script/sale.js"></script>
    <script src="../script/auth-check.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>

    <script>
        // Kiểm tra trạng thái đăng nhập và hiển thị tên người dùng
        document.addEventListener('DOMContentLoaded', function() {
            // Kiểm tra xem người dùng đã đăng nhập chưa
            function checkLoginStatus() {
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const username = localStorage.getItem('username');
                const accountIcon = document.getElementById('account-icon');
                const userAccount = document.getElementById('user-account');
                const usernameDisplay = document.getElementById('username-display');

                if (isLoggedIn === 'true' && username) {
                    // Đã đăng nhập: Hiển thị tên người dùng
                    accountIcon.classList.add('hidden');
                    userAccount.classList.remove('hidden');
                    usernameDisplay.textContent = username;
                } else {
                    // Chưa đăng nhập: Hiển thị icon
                    accountIcon.classList.remove('hidden');
                    userAccount.classList.add('hidden');
                }
            }

            // Kiểm tra trạng thái đăng nhập khi trang được tải
            checkLoginStatus();

            // Kiểm tra trạng thái đăng nhập mỗi khi localStorage thay đổi
            window.addEventListener('storage', function(e) {
                if (e.key === 'isLoggedIn' || e.key === 'username') {
                    checkLoginStatus();
                }
            });
        });
    </script>
</body>
</html>
