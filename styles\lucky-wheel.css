/* Lucky Wheel Styles */
.spin-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s, visibility 0.3s;
}

.spin-popup.hide-spin {
    opacity: 0;
    visibility: hidden;
}

.spin-container {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    text-align: center;
    position: relative;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s;
}

.spin-popup:not(.hide-spin) .spin-container {
    transform: scale(1);
}

.close-spin {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.close-spin:hover {
    color: #ff6b6b;
}

.spin-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
}

#wheel {
    margin: 20px auto;
    display: block;
}

#spin-btn {
    background-color: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    margin: 20px 0;
}

#spin-btn:hover {
    background-color: #ff5252;
}

#spin-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

#spin-result {
    margin-top: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    min-height: 50px;
}

.coupon-code {
    background-color: #f8f8f8;
    border: 2px dashed #ddd;
    padding: 10px 15px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 20px;
    letter-spacing: 2px;
    margin: 10px 0;
    display: inline-block;
}

.copy-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-left: 10px;
}

.copy-btn:hover {
    background-color: #45a049;
}

.wheel-trigger {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: #ff6b6b;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 99;
    transition: transform 0.3s, background-color 0.3s;
}

.wheel-trigger:hover {
    transform: scale(1.1);
    background-color: #ff5252;
}

.wheel-trigger i {
    font-size: 24px;
}

/* Animation */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.spinning {
    animation: spin 3s cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

/* Confetti Animation */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #f00;
    opacity: 0;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(500px) rotate(360deg);
        opacity: 0;
    }
}

/* Responsive Styles */
@media (max-width: 576px) {
    .spin-container {
        padding: 20px;
    }
    
    .spin-container h2 {
        font-size: 20px;
    }
    
    #wheel {
        width: 250px;
        height: 250px;
    }
}
