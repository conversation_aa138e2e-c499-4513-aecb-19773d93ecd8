<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/men-new.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <title>Thời trang Nam | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="men-hero">
        <div class="men-hero-content">
            <h1 data-aos="fade-up">Thời Trang Nam</h1>
            <p data-aos="fade-up" data-aos-delay="200">Phong cách lịch lãm và năng động cho quý ông hiện đại</p>
            <div class="men-hero-buttons" data-aos="fade-up" data-aos-delay="400">
                <a href="./menproducts.html" class="hero-btn hero-btn-primary">Mua sắm ngay</a>
                <a href="#men-categories" class="hero-btn hero-btn-secondary">Xem danh mục</a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="men-categories" id="men-categories">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Danh Mục Sản Phẩm</h2>
            <div class="categories-container">
                <div class="category-item" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0dw9kgvdpxr90@resize_w450_nl.webp" alt="Áo sơ mi" class="category-image">
                    <div class="category-overlay">
                        <h3>Áo sơ mi</h3>
                        <p>Áo sơ mi lịch lãm cho quý ông hiện đại</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7rcdv-lqx2r2l3krr579@resize_w450_nl.webp" alt="Áo thun" class="category-image">
                    <div class="category-overlay">
                        <h3>Áo thun</h3>
                        <p>Áo thun thoải mái cho mọi dịp</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7qvd5-lkahxqlag08sd7@resize_w450_nl.webp" alt="Quần jean" class="category-image">
                    <div class="category-overlay">
                        <h3>Quần jean</h3>
                        <p>Quần jean phong cách và bền bỉ</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="400">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m4ug0nwos27q03@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m4ug0nwos27q03@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m4ug0nwos27q03@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m4ug0nwos27q03@resize_w450_nl.webp" alt="Quần kaki" class="category-image">
                    <div class="category-overlay">
                        <h3>Quần kaki</h3>
                        <p>Quần kaki lịch sự cho công sở và dạo phố</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="500">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lq4pzsoehbg74e@resize_w450_nl.webp" alt="Áo khoác" class="category-image">
                    <div class="category-overlay">
                        <h3>Áo khoác</h3>
                        <p>Áo khoác phong cách cho mọi mùa</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="600">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7d3pkodffcm51@resize_w450_nl.webp" alt="Đồ thể thao" class="category-image">
                    <div class="category-overlay">
                        <h3>Đồ thể thao</h3>
                        <p>Trang phục thể thao năng động</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="700">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7lt91gp9fpi7f@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7lt91gp9fpi7f@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7lt91gp9fpi7f@resize_w450_nl.webp" alt="Đồ công sở" class="category-image">
                    <div class="category-overlay">
                        <h3>Đồ công sở</h3>
                        <p>Trang phục công sở lịch lãm</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="800">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0lvp0ikymq7e8@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0lvp0ikymq7e8@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0lvp0ikymq7e8@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0lvp0ikymq7e8@resize_w450_nl.webp" alt="Phụ kiện" class="category-image">
                    <div class="category-overlay">
                        <h3>Phụ kiện</h3>
                        <p>Phụ kiện thời trang cho quý ông</p>
                        <a href="./menproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="featured-products">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Sản Phẩm Nổi Bật</h2>
            <div class="products-grid">
                <!-- Product 1 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="product-image">
                        <span class="product-badge new">Mới</span>
                        <img src="./img mens/men 3.webp" alt="Bộ quần áo sơ mi cao cấp">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Bộ quần áo sơ mi cao cấp cộc tay chất đũi dày dặn-thoáng mát</h3>
                        <div class="product-price">
                            <span class="current-price">450.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #f8f9fa;" title="Kem"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="product-image">
                        <span class="product-badge sale">-20%</span>
                        <img src="./img mens/men 4.webp" alt="Quần dài nam nữ túi hộp">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Quần dài nam nữ túi hộp Form suông ống rộng Quần Cargo Pants</h3>
                        <div class="product-price">
                            <span class="current-price">550.000đ</span>
                            <span class="old-price">690.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                            <span class="color-option" style="background-color: #3b5998;" title="Xanh"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="product-image">
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m4ugbs19a5tv34@resize_w450_nl.webp" alt="Quần Jean Đen Nam Ống Rộng">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Quần Âu Kaki Ống Suông Có Đai Vibe Hàn Quốc</h3>
                        <div class="product-price">
                            <span class="current-price">650.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #3b5998;" title="Xanh"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="product-image">
                        <img src="./img mens/men 6.webp" alt="Áo Polo nam-nữ oversize">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo Polo nam-nữ oversize, cổ khóa, tay lỡ form rộng</h3>
                        <div class="product-price">
                            <span class="current-price">380.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 5 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="product-image">
                        <span class="product-badge sale">-15%</span>
                        <img src="./img mens/men 7.webp" alt="Áo polo nam tay ngắn">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo polo nam tay ngắn dáng rộng họa tiết bông lúa</h3>
                        <div class="product-price">
                            <span class="current-price">340.000đ</span>
                            <span class="old-price">400.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #3b5998;" title="Xanh"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 6 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="product-image">
                        <img src="./img mens/men 16.webp" alt="Áo Jacket MM PHỐI TAY DA">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo Jacket MM PHỐI TAY DA màu XÁM/KEM Nam Nữ Unisex</h3>
                        <div class="product-price">
                            <span class="current-price">520.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                            <span class="color-option" style="background-color: #f8f9fa;" title="Kem"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 7 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="700">
                    <div class="product-image">
                        <span class="product-badge new">Mới</span>
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lnmge9sy6i2580@resize_w450_nl.webp" alt="Áo khoác bomber nam">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo khoác gió bigsize nam nữ 2 lớp kẻ line phản quang BS COUTURE tráng bạc chống nước</h3>
                        <div class="product-price">
                            <span class="current-price">580.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #3b5998;" title="Xanh"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 8 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="800">
                    <div class="product-image">
                        <span class="product-badge sale">-25%</span>
                        <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m0dwqpti02of32@resize_w450_nl.webp" alt="Áo sơ mi nam dài tay">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo sơ mi nam dài tay công sở lịch lãm</h3>
                        <div class="product-price">
                            <span class="current-price">375.000đ</span>
                            <span class="old-price">500.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #3b5998;" title="Xanh"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>
            </div>
            <a href="./menproducts.html" class="view-all-btn" data-aos="fade-up">Xem tất cả sản phẩm</a>
        </div>
    </section>

    <!-- Trending Section -->
    <section class="trending-section">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Xu Hướng Thời Trang</h2>
            <div class="trending-container">
                <div class="trending-item" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-li5a01wd374ifd@resize_w450_nl.webp" alt="Casual Style" class="trending-image">
                    <div class="trending-overlay">
                        <h3 class="trending-title">Phong Cách Dạo Phố</h3>
                        <p class="trending-description">Thoải mái và phong cách với những thiết kế hiện đại</p>
                        <a href="./menproducts.html" class="trending-btn">Khám phá ngay</a>
                    </div>
                </div>
                <div class="trending-item" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvk2r03cqdy401@resize_w450_nl.webp" alt="Formal Style" class="trending-image">
                    <div class="trending-overlay">
                        <h3 class="trending-title">Phong Cách Công Sở</h3>
                        <p class="trending-description">Lịch lãm và chuyên nghiệp với những bộ trang phục tinh tế</p>
                        <a href="./menproducts.html" class="trending-btn">Khám phá ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>
    <script src="./script/user-display.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Smooth scroll for category link
        $('a[href="#men-categories"]').on('click', function(event) {
            event.preventDefault();
            $('html, body').animate({
                scrollTop: $($(this).attr('href')).offset().top - 100
            }, 800);
        });

        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                // Check if user is logged in
                const user = JSON.parse(localStorage.getItem('user'));
                if (!user || !user.isLoggedIn) {
                    // Show login prompt
                    showLoginPrompt();
                    return;
                }

                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-title').textContent;
                const productPrice = productCard.querySelector('.current-price').textContent;
                const productImage = productCard.querySelector('.product-image img').src;

                // Get cart from localStorage
                let cart = JSON.parse(localStorage.getItem('cart')) || [];

                // Add product to cart
                cart.push({
                    name: productName,
                    price: parseInt(productPrice.replace(/\D/g, '')),
                    image: productImage,
                    quantity: 1
                });

                // Save cart to localStorage
                localStorage.setItem('cart', JSON.stringify(cart));

                // Update cart count
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = cart.length;
                }

                // Show notification
                showNotification('Sản phẩm đã được thêm vào giỏ hàng', 'success');
            });
        });

        /**
         * Show login prompt popup
         */
        function showLoginPrompt() {
            // Create login prompt container
            const loginPrompt = document.createElement('div');
            loginPrompt.className = 'login-prompt-overlay';
            loginPrompt.style.position = 'fixed';
            loginPrompt.style.top = '0';
            loginPrompt.style.left = '0';
            loginPrompt.style.width = '100%';
            loginPrompt.style.height = '100%';
            loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loginPrompt.style.display = 'flex';
            loginPrompt.style.alignItems = 'center';
            loginPrompt.style.justifyContent = 'center';
            loginPrompt.style.zIndex = '9999';
            loginPrompt.style.opacity = '0';
            loginPrompt.style.transition = 'opacity 0.3s ease';

            loginPrompt.innerHTML = `
                <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                    <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                        <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                        <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="login-prompt-body" style="padding: 20px;">
                        <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                        <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                            <a href="./pages/login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                            <a href="./pages/register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                        </div>
                    </div>
                </div>
            `;

            // Add to body
            document.body.appendChild(loginPrompt);

            // Show with animation
            setTimeout(() => {
                loginPrompt.style.opacity = '1';
            }, 10);

            // Close button event
            const closeButton = loginPrompt.querySelector('.login-prompt-close');
            closeButton.addEventListener('click', function() {
                loginPrompt.style.opacity = '0';
                setTimeout(() => {
                    loginPrompt.remove();
                }, 300);
            });

            // Save current URL to redirect back after login
            localStorage.setItem('redirectAfterLogin', window.location.href);
        }

        // Product quick view
        document.querySelectorAll('.product-action-btn[title="Xem nhanh"]').forEach(button => {
            button.addEventListener('click', function() {
                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-title').textContent;
                const productPrice = productCard.querySelector('.current-price').textContent;
                const productImage = productCard.querySelector('.product-image img').src;

                // Redirect to product detail page (in a real implementation)
                // For now, just show a notification
                showNotification('Chức năng xem nhanh đang được phát triển', 'info');
            });
        });

        // Wishlist functionality is now handled by wishlist-handler.js
    </script>
</body>
</html>
