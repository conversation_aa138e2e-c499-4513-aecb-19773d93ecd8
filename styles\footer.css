/* Footer CSS */
@import url('style.css');

.footer {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: var(--spacing-xxl) 0 var(--spacing-md);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--accent-color), var(--primary-color));
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-column h3 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
    position: relative;
    padding-bottom: var(--spacing-sm);
    color: var(--light-text);
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--accent-color);
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-column ul li a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    transition: all var(--transition-fast);
    display: inline-block;
    position: relative;
    padding-left: 15px;
}

.footer-column ul li a::before {
    content: '\f105';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 2px;
    font-size: 0.8rem;
    color: var(--accent-color);
    transition: transform var(--transition-fast);
}

.footer-column ul li a:hover {
    color: var(--light-text);
    transform: translateX(5px);
}

.footer-column ul li a:hover::before {
    transform: translateX(3px);
}

.social-icons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-text);
    transition: all var(--transition-fast);
    font-size: 1.1rem;
}

.social-icon:hover {
    background-color: var(--accent-color);
    color: var(--light-text);
    transform: translateY(-3px);
}

.app-download h4 {
    font-size: 1rem;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--light-text);
}

.app-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.app-buttons img {
    height: 40px;
    width: auto;
    border-radius: var(--border-radius-sm);
    transition: transform var(--transition-fast);
}

.app-buttons img:hover {
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

.footer-bottom p {
    margin: 0;
}

/* Footer Newsletter */
.footer-newsletter {
    background-color: rgba(255, 255, 255, 0.05);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.footer-newsletter h4 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--light-text);
}

.footer-newsletter p {
    margin-bottom: var(--spacing-md);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
}

.footer-newsletter-form {
    display: flex;
    gap: var(--spacing-xs);
}

.footer-newsletter-form input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
    color: var(--light-text);
    font-family: var(--body-font);
}

.footer-newsletter-form input:focus {
    outline: none;
    border-color: var(--accent-color);
}

.footer-newsletter-form button {
    background-color: var(--accent-color);
    color: var(--light-text);
    border: none;
    padding: 0 1.2rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.footer-newsletter-form button:hover {
    background-color: var(--accent-hover);
}

/* Contact Info */
.contact-info {
    margin-top: var(--spacing-md);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.contact-icon {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-top: 3px;
}

.contact-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Responsive */
@media (max-width: 992px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    .social-icons {
        justify-content: center;
    }
    
    .app-buttons {
        justify-content: center;
    }
    
    .footer-newsletter-form {
        flex-direction: column;
    }
    
    .footer-newsletter-form button {
        width: 100%;
        padding: 0.8rem;
    }
    
    .footer-column h3 {
        text-align: center;
    }
    
    .footer-column h3::after {
        left: 50%;
        transform: translateX(-50%);
    }
}
