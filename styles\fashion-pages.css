/* Fashion Pages Styles - For Men and Women Pages */

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #f8f9fa;
    --accent-color: #339af0;
    --text-color: #343a40;
    --light-text: #868e96;
    --border-color: #dee2e6;
    --background-color: #ffffff;
    --hover-color: #ff5252;
    --section-spacing: 80px;
    --card-spacing: 25px;
    --border-radius: 8px;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 600px;
    overflow: hidden;
    margin-bottom: var(--section-spacing);
}

.hero-slider {
    width: 100%;
    height: 100%;
    position: relative;
}

.hero-slide {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-content {
    position: absolute;
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    max-width: 500px;
    color: var(--background-color);
    z-index: 2;
    padding: 30px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    font-weight: 300;
    line-height: 1.6;
}

.hero-btn {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.hero-btn:hover {
    background-color: var(--hover-color);
    transform: translateY(-3px);
}

.slider-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.slider-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Section Styles */
.fashion-section {
    padding: 0 5%;
    margin-bottom: var(--section-spacing);
}

.section-header {
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 15px;
    color: var(--text-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
}

/* Trend Cards */
.trend-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--card-spacing);
}

.trend-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 350px;
}

.trend-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.trend-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.trend-card:hover .trend-image {
    transform: scale(1.05);
}

.trend-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: white;
    transition: var(--transition);
}

.trend-card:hover .trend-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.5));
    padding-bottom: 30px;
}

.trend-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.trend-description {
    font-size: 0.9rem;
    opacity: 0.8;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Product Cards */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--card-spacing);
}

.product-card {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.product-image-container {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 48px;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.product-colors {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.color-option:hover, .color-option.active {
    transform: scale(1.2);
    border-color: var(--border-color);
}

.product-actions {
    display: flex;
    justify-content: space-between;
}

.action-btn {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
}

.add-to-cart {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.add-to-cart:hover {
    background-color: var(--hover-color);
}

.add-to-wishlist {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.add-to-wishlist:hover {
    background-color: var(--secondary-color);
}

/* View More Button */
.view-more-container {
    text-align: center;
    margin-top: 40px;
}

.view-more-btn {
    display: inline-block;
    padding: 12px 30px;
    background-color: transparent;
    color: var(--text-color);
    text-decoration: none;
    border: 2px solid var(--primary-color);
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.view-more-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-section {
        height: 500px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-content {
        left: 5%;
        max-width: 90%;
    }
    
    .trend-grid, .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: 350px;
    }
    
    .hero-title {
        font-size: 1.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .trend-grid, .product-grid {
        grid-template-columns: 1fr 1fr;
    }
}
