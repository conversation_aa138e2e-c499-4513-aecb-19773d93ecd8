/* Neon Effects CSS */

:root {
    --neon-primary: #ff00ff;
    --neon-secondary: #00ffff;
    --neon-tertiary: #ffff00;
    --neon-quaternary: #00ff00;
    --neon-text-shadow: 0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 20px rgba(255, 0, 255, 0.3);
    --neon-box-shadow: 0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 20px rgba(255, 0, 255, 0.3), inset 0 0 5px rgba(255, 0, 255, 0.2), inset 0 0 10px rgba(255, 0, 255, 0.1);
}

/* Neon Form Container */
.neon-form-container {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 40px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--neon-box-shadow);
    animation: neonPulse 2s infinite alternate;
    transition: all 0.3s ease;
}

.neon-form-container:hover {
    box-shadow: 0 0 10px var(--neon-primary), 0 0 20px var(--neon-primary), 0 0 30px var(--neon-primary), inset 0 0 10px var(--neon-primary);
}

/* Neon Title */
.neon-title {
    color: white;
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: var(--neon-text-shadow);
    position: relative;
    animation: neonTextFlicker 3s infinite alternate;
}

/* Neon Input Fields */
.neon-input {
    background-color: rgba(0, 0, 0, 0.6);
    border: 2px solid var(--neon-primary);
    border-radius: 5px;
    color: white;
    padding: 12px 15px;
    width: 100%;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px rgba(255, 0, 255, 0.3);
}

.neon-input:focus {
    outline: none;
    border-color: var(--neon-secondary);
    box-shadow: 0 0 10px var(--neon-secondary), 0 0 20px rgba(0, 255, 255, 0.3), inset 0 0 5px rgba(0, 255, 255, 0.2);
    animation: neonInputPulse 1.5s infinite alternate;
}

.neon-input-container {
    position: relative;
    margin-bottom: 20px;
}

.neon-input-container label {
    position: absolute;
    top: -10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    padding: 0 10px;
    color: var(--neon-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(255, 0, 255, 0.5);
}

.neon-input-container:focus-within label {
    color: var(--neon-secondary);
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Neon Button */
.neon-button {
    background-color: transparent;
    border: 2px solid var(--neon-primary);
    border-radius: 5px;
    color: white;
    padding: 12px 30px;
    width: 100%;
    cursor: pointer;
    font-weight: bold;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-shadow: var(--neon-text-shadow);
    box-shadow: 0 0 5px var(--neon-primary), 0 0 10px rgba(255, 0, 255, 0.3);
    margin-top: 10px;
}

.neon-button:hover {
    background-color: rgba(255, 0, 255, 0.1);
    box-shadow: 0 0 10px var(--neon-primary), 0 0 20px var(--neon-primary), inset 0 0 10px var(--neon-primary);
}

.neon-button:active {
    animation: buttonShake 0.3s ease;
}

/* Electric Border Effect */
.electric-border {
    position: relative;
}

.electric-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 4px);
    height: calc(100% + 4px);
    background: linear-gradient(45deg, 
        var(--neon-primary), 
        var(--neon-secondary), 
        var(--neon-tertiary), 
        var(--neon-quaternary), 
        var(--neon-primary));
    background-size: 400%;
    z-index: -1;
    border-radius: 12px;
    animation: electricBorder 20s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.electric-border:hover::before {
    opacity: 1;
}

/* LED Running Light */
.led-strip {
    height: 3px;
    width: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        var(--neon-primary), 
        var(--neon-secondary), 
        var(--neon-tertiary), 
        var(--neon-quaternary), 
        transparent);
    position: absolute;
    top: 0;
    left: -100%;
    animation: ledRun 3s linear infinite;
}

.led-bottom {
    top: auto;
    bottom: 0;
    animation-delay: 1.5s;
}

/* Electric Wave Effect */
.wave-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.wave {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, 
        rgba(255, 0, 255, 0.3) 0%, 
        rgba(0, 255, 255, 0.2) 30%, 
        rgba(255, 255, 0, 0.1) 70%, 
        transparent 100%);
    transform: scale(0);
    opacity: 0;
}

/* Lightning Effect for Inputs */
.lightning-effect {
    position: relative;
}

.lightning-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-radius: 5px;
    pointer-events: none;
    z-index: 1;
}

.lightning-effect:focus-within::after {
    animation: lightningEffect 1.5s infinite;
}

/* Glowing Background */
.glowing-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, 
        rgba(255, 0, 255, 0.1) 0%, 
        rgba(0, 0, 0, 0) 70%);
    opacity: 0.5;
    z-index: -2;
    animation: glowingBg 5s infinite alternate;
}

/* Animations */
@keyframes neonPulse {
    0% {
        box-shadow: 0 0 5px var(--neon-primary), 0 0 10px var(--neon-primary);
    }
    100% {
        box-shadow: 0 0 10px var(--neon-primary), 0 0 20px var(--neon-primary), 0 0 30px var(--neon-primary);
    }
}

@keyframes neonTextFlicker {
    0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
        text-shadow: 0 0 5px var(--neon-primary), 0 0 10px var(--neon-primary), 0 0 15px var(--neon-primary);
    }
    20%, 24%, 55% {
        text-shadow: none;
    }
}

@keyframes neonInputPulse {
    0% {
        box-shadow: 0 0 5px var(--neon-secondary), 0 0 10px rgba(0, 255, 255, 0.3);
    }
    100% {
        box-shadow: 0 0 10px var(--neon-secondary), 0 0 20px var(--neon-secondary), 0 0 30px rgba(0, 255, 255, 0.3);
    }
}

@keyframes buttonShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    50% {
        transform: translateX(5px);
    }
    75% {
        transform: translateX(-5px);
    }
}

@keyframes electricBorder {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes ledRun {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes lightningEffect {
    0%, 100% {
        border-color: transparent;
        box-shadow: none;
    }
    10% {
        border-top-color: var(--neon-tertiary);
        box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    }
    20% {
        border-right-color: var(--neon-tertiary);
        box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    }
    30% {
        border-bottom-color: var(--neon-tertiary);
        box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    }
    40% {
        border-left-color: var(--neon-tertiary);
        box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    }
    50% {
        border-color: var(--neon-tertiary);
        box-shadow: 0 0 15px rgba(255, 255, 0, 0.7);
    }
    60% {
        border-color: transparent;
        box-shadow: none;
    }
}

@keyframes glowingBg {
    0% {
        opacity: 0.3;
        background: radial-gradient(circle at center, 
            rgba(255, 0, 255, 0.1) 0%, 
            rgba(0, 0, 0, 0) 70%);
    }
    50% {
        opacity: 0.5;
        background: radial-gradient(circle at center, 
            rgba(0, 255, 255, 0.1) 0%, 
            rgba(0, 0, 0, 0) 70%);
    }
    100% {
        opacity: 0.3;
        background: radial-gradient(circle at center, 
            rgba(255, 255, 0, 0.1) 0%, 
            rgba(0, 0, 0, 0) 70%);
    }
}

/* JavaScript will add this class when clicking on the form */
.wave-animation {
    animation: waveExpand 1s ease-out forwards;
}

@keyframes waveExpand {
    0% {
        transform: scale(0);
        opacity: 0.8;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}
