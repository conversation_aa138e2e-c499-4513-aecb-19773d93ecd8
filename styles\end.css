/* Order Confirmation Page Styles */
@import url('style.css');

/* Checkout Header */
.checkout-header {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.checkout-header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkout-header .logo {
    max-width: 150px;
}

.checkout-header .logo img {
    width: 100%;
    height: auto;
}

.checkout-steps {
    display: flex;
    align-items: center;
}

.step {
    display: flex;
    align-items: center;
    margin-right: 2rem;
    opacity: 0.5;
}

.step.active {
    opacity: 1;
}

.step.completed {
    opacity: 1;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 0.5rem;
}

.step.active .step-number {
    background-color: var(--accent-color);
}

.step.completed .step-number {
    background-color: #4CAF50;
}

.step-label {
    font-weight: 500;
}

.secure-checkout {
    display: flex;
    align-items: center;
    color: var(--accent-color);
    font-weight: 500;
}

.secure-checkout i {
    margin-right: 0.5rem;
}

/* Order Confirmation */
.order-confirmation {
    padding: 3rem 0;
    background-color: var(--background-color);
}

.confirmation-content {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.confirmation-header {
    text-align: center;
    margin-bottom: 3rem;
}

.success-icon {
    width: 80px;
    height: 80px;
    background-color: #4CAF50;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto 1.5rem;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 5px 20px rgba(76, 175, 80, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
    }
}

.confirmation-header h1 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.confirmation-header p {
    font-size: 1.1rem;
    color: var(--text-color);
    max-width: 600px;
    margin: 0 auto;
}

/* Order Details */
.order-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.order-info {
    flex: 1;
}

.order-info-item {
    margin-bottom: 1rem;
}

.order-info-item .label {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.shipping-info {
    flex: 1;
    background-color: var(--secondary-color);
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
}

.shipping-info h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.shipping-info p {
    margin-bottom: 0.5rem;
}

/* Order Summary */
.order-summary {
    margin-bottom: 2rem;
}

.order-summary h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.order-items {
    margin-bottom: 2rem;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.order-item {
    display: flex;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.order-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-right: 1rem;
}

.order-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.order-item-details {
    flex: 1;
}

.order-item-name {
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.order-item-variant {
    font-size: 0.9rem;
    color: var(--text-color-light);
    margin-bottom: 0.5rem;
}

.order-item-price {
    display: flex;
    justify-content: space-between;
    font-weight: 500;
}

.order-totals {
    background-color: var(--secondary-color);
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
}

.order-total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.order-total-row.discount {
    color: var(--accent-color);
}

.order-total-row.total {
    font-weight: 700;
    font-size: 1.2rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Confirmation Actions */
.confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.btn-primary {
    background-color: var(--accent-color);
    color: #fff;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
}

.btn-secondary {
    background-color: var(--primary-color);
    color: #fff;
}

.btn-secondary:hover {
    background-color: var(--primary-hover);
}

.order-note {
    text-align: center;
    color: var(--text-color-light);
    font-size: 0.9rem;
}

/* Recommended Products */
.recommended-products {
    margin-bottom: 3rem;
}

.section-title {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 2rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.product-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    padding: 1.2rem;
}

.product-name {
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
    height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    font-weight: 600;
    color: var(--accent-color);
    font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 992px) {
    .order-details {
        flex-direction: column;
    }
    
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .checkout-header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .checkout-steps {
        width: 100%;
        justify-content: space-between;
    }
    
    .step {
        margin-right: 0;
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .confirmation-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
}
