// Virtual Try-On Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Sample clothing data (in a real app, this would come from a database)
    const clothingItems = [
        {
            id: 1,
            name: "<PERSON><PERSON>hun <PERSON> Hở Vai Cỡ Lớn <PERSON>ản Thời <PERSON>",
            price: "150.000đ",
            category: "tops",
            gender: "female",
            image: "https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lqcun413ojsl5d@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lqcun413ojsl5d@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lqcun413ojsl5d@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lqcun413ojsl5d@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/white-shirt.png"
            // ../img/clothing-layers/white-shirt.png
        },
        {
            id: 2,
            name: "Áo Thun Nữ Cộc Tay In Chữ Thời Trang Năng Động",
            price: "110.000đ",
            category: "bottoms",
            gender: "female",
            image: "https://down-vn.img.susercontent.com/file/cn-11134211-7r98o-llzqaibbn2lg8f@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134211-7r98o-llzqaibbn2lg8f@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/blue-jeans.png"
        },
        {
            id: 3,
            name: " Xếp ly eo giảm béo kiểu Pháp váy không tay cổ điển dành cho nữ",
            price: "159.000đ",
            category: "dresses",
            gender: "female",
            image: "https://down-vn.img.susercontent.com/file/6e01dd00a2cb5cf0e3032386048ee7bb@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/floral-dress.png"
        },
        {
            id: 4,
            name: "Áo khoác dây kéo gấu nhỏ siêu dễ thương dành cho nữ Áo Hoodie dáng rộng phong cách ngọt ngào Hàn Quốc",
            price: "180.000đ",
            category: "outerwear",
            gender: "female",
            image: "https://down-vn.img.susercontent.com/file/sg-11134201-7repu-m2dj6smn8pq342.webp",
            layerImage: "../img/clothing-layers/denim-jacket.png"
        },
        {
            id: 5,
            name: "Áo croptop nữ tay ngắn HHVINTAGE kiểu lệch vai gấp nhún thun tăm mịn co dãn  ",
            price: "140.000đ",
            category: "tops",
            gender: "female",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lpxcwg2pnvma53@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/white-tshirt.png"
        },
        {
            id: 6,
            name: "Quần legging đùi ECOCHIC nữ cạp cao gen bụng tập gym yoga chất liệu su đúc định hình nâng mông",
            price: "220.000đ",
            category: "bottoms",
            gender: "female",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-ltoo4ycnuivh23@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/beige-pants.png"
        },
        {
            id: 7,
            name: "Áo sơ mi kẻ sọc unisex ZGO vải không nhăn mềm mát",
            price: "280.000đ",
            category: "tops",
            gender: "male",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m9quk7bej9y21e@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/striped-shirt.png"
        },
        {
            id: 8,
            name: "Quần JEAN xuông NAD chất liệu cotton co giãn mặc thoải mái,quần bò nam nữ phom Ống Rộng Dáng Suông",
            price: "180.000đ",
            category: "bottoms",
            gender: "male",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lykdaghq5zkd37.webp",
            layerImage: "../img/clothing-layers/black-jeans.png"
        },
        {
            id: 9,
            name: "Áo bomber jackets nam nữ form rộng thêu 199x varsity vip chất dù 2 lớp mềm mát Sobasic",
            price: "350.000đ",
            category: "outerwear",
            gender: "male",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-lgzzp2u6hquqdf@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/bomber-jacket.png"
        },
        {
            id: 10,
            name: "Áo thun Polo nam KENB CLOSET cổ bẻ tay ngắn phối vai tinh tế chất vải cotton cá sấu co giãn 4 chi",
            price: "320.000đ",
            category: "tops",
            gender: "male",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lyrna0hugi19ce@resize_w450_nl.webp",
            layerImage: "../img/clothing-layers/navy-polo.png"
        }
    ];

    // Sample outfit recommendations
    const outfitRecommendations = [
        {
            id: 1,
            name: "Quần sọc jeans nữ Chollima tua lai trơn QS006 phong cách trẻ trung hàn quốc",
            price: "130,000đ",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-lfsfc7dpom470a.webp",
            items: [1, 2, 4]
        },
        {
            id: 2,
            name: "Quần short TODOBLACK thể thao dáng ôm co giãn tốt thời trang mùa hè cho nữ",
            price: "100.000đ",
            image: "https://down-vn.img.susercontent.com/file/a716298d5f511bdd16381ae6c635a79b@resize_w450_nl.webp",
            items: [5, 6]
        },
        {
            id: 3,
            name: "Kii Wii Váy kẻ sọc kẻ sọc cạp cao mùa hè dành cho nữ",
            price: "90.000đ",
            image: "https://down-vn.img.susercontent.com/file/sg-11134201-7rd4y-m77ox08f0aggdb@resize_w450_nl.webp",
            items: [3, 4]
        },
        {
            id: 4,
            name: "Áo sơ mi nam POLYS Fullbox Vải Linen Vân Đôi Co Giãn 2 Chiều Thấm Hút Tốt ",
            price: "209.000đ",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m6qa2990ec138b@resize_w450_nl.webp",
            items: [7, 8]
        },
        {
            id: 5,
            name: "Áo sơ mi cổ bẻ dài tay PEALO nam chất liệu Linen cao cấp",
            price: "100.000đ",
            image: "https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lza2qvlmk2ap57@resize_w450_nl.webp",
            items: [10, 8, 9]
        }
    ];

    // Current state
    let currentGender = 'female';
    let currentCategory = 'all';
    let selectedItems = [];
    let avatarScale = 1;
    let avatarRotation = 0;

    // DOM elements
    const clothingItemsContainer = document.querySelector('.clothing-items');
    const avatarOptions = document.querySelectorAll('.avatar-option');
    const categoryButtons = document.querySelectorAll('.category-btn');
    const selectedItemsContainer = document.getElementById('selected-items');
    const avatarImage = document.getElementById('avatar-image');
    const heightSlider = document.getElementById('height');
    const weightSlider = document.getElementById('weight');
    const heightValue = document.getElementById('height-value');
    const weightValue = document.getElementById('weight-value');
    const skinToneOptions = document.querySelectorAll('.skin-tone');
    const saveOutfitBtn = document.getElementById('save-outfit');
    const shareOutfitBtn = document.getElementById('share-outfit');
    const addToCartBtn = document.getElementById('add-to-cart');
    const savedOutfitModal = document.getElementById('saved-outfit-modal');
    const modalClose = document.querySelector('.modal-close');
    const rotateLeftBtn = document.getElementById('rotate-left');
    const rotateRightBtn = document.getElementById('rotate-right');
    const zoomInBtn = document.getElementById('zoom-in');
    const zoomOutBtn = document.getElementById('zoom-out');
    const resetViewBtn = document.getElementById('reset-view');
    const recommendationSlider = document.querySelector('.recommendation-slider');

    // Initialize
    function init() {
        loadClothingItems();
        loadRecommendations();
        setupEventListeners();
    }

    // Load clothing items based on current gender and category
    function loadClothingItems() {
        clothingItemsContainer.innerHTML = '';
        
        const filteredItems = clothingItems.filter(item => {
            if (currentCategory === 'all') {
                return item.gender === currentGender;
            } else {
                return item.gender === currentGender && item.category === currentCategory;
            }
        });
        
        if (filteredItems.length === 0) {
            clothingItemsContainer.innerHTML = '<p class="no-items">Không có sản phẩm nào trong danh mục này</p>';
            return;
        }
        
        filteredItems.forEach(item => {
            const isSelected = selectedItems.some(selectedItem => selectedItem.id === item.id);
            
            const itemElement = document.createElement('div');
            itemElement.className = `clothing-item${isSelected ? ' selected' : ''}`;
            itemElement.dataset.id = item.id;
            
            itemElement.innerHTML = `
                <div class="clothing-item-image">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="clothing-item-info">
                    <div class="clothing-item-name">${item.name}</div>
                    <div class="clothing-item-price">${item.price}</div>
                </div>
            `;
            
            clothingItemsContainer.appendChild(itemElement);
            
            // Add click event
            itemElement.addEventListener('click', function() {
                toggleClothingItem(item);
            });
        });
    }

    // Load outfit recommendations
    function loadRecommendations() {
        recommendationSlider.innerHTML = '';
        
        const filteredRecommendations = outfitRecommendations.filter(outfit => {
            // Check if at least one item in the outfit matches the current gender
            const outfitItems = outfit.items.map(id => clothingItems.find(item => item.id === id));
            return outfitItems.some(item => item && item.gender === currentGender);
        });
        
        filteredRecommendations.forEach(outfit => {
            const outfitElement = document.createElement('div');
            outfitElement.className = 'recommendation-item';
            
            outfitElement.innerHTML = `
                <div class="recommendation-image">
                    <img src="${outfit.image}" alt="${outfit.name}">
                </div>
                <div class="recommendation-info">
                    <div class="recommendation-name">${outfit.name}</div>
                    <div class="recommendation-price">${outfit.price}</div>
                </div>
                <button class="try-btn" data-outfit="${outfit.id}">Thử ngay</button>
            `;
            
            recommendationSlider.appendChild(outfitElement);
        });
        
        // Add click events to try buttons
        const tryButtons = document.querySelectorAll('.try-btn');
        tryButtons.forEach(button => {
            button.addEventListener('click', function() {
                const outfitId = parseInt(this.dataset.outfit);
                tryOutfit(outfitId);
            });
        });
    }

    // Try an outfit
    function tryOutfit(outfitId) {
        const outfit = outfitRecommendations.find(outfit => outfit.id === outfitId);
        if (!outfit) return;
        
        // Clear current selection
        selectedItems = [];
        updateSelectedItems();
        
        // Add outfit items
        outfit.items.forEach(itemId => {
            const item = clothingItems.find(item => item.id === itemId);
            if (item && item.gender === currentGender) {
                selectedItems.push(item);
            }
        });
        
        // Update UI
        updateSelectedItems();
        loadClothingItems();
        updateAvatarLayers();
        
        // Show notification
        showNotification(`Đã thử bộ đồ "${outfit.name}"`, 'success');
    }

    // Toggle clothing item selection
    function toggleClothingItem(item) {
        const index = selectedItems.findIndex(selectedItem => selectedItem.id === item.id);
        
        if (index === -1) {
            // Add item
            selectedItems.push(item);
        } else {
            // Remove item
            selectedItems.splice(index, 1);
        }
        
        updateSelectedItems();
        loadClothingItems();
        updateAvatarLayers();
    }

    // Update selected items display
    function updateSelectedItems() {
        if (selectedItems.length === 0) {
            selectedItemsContainer.innerHTML = '<p class="no-items">Chưa có trang phục nào được chọn</p>';
            return;
        }
        
        selectedItemsContainer.innerHTML = '';
        
        selectedItems.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'selected-item';
            
            itemElement.innerHTML = `
                <div class="selected-item-image">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="selected-item-info">
                    <div class="selected-item-name">${item.name}</div>
                    <div class="selected-item-price">${item.price}</div>
                </div>
                <button class="remove-item" data-id="${item.id}"><i class="fas fa-times"></i></button>
            `;
            
            selectedItemsContainer.appendChild(itemElement);
        });
        
        // Add click events to remove buttons
        const removeButtons = document.querySelectorAll('.remove-item');
        removeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const itemId = parseInt(this.dataset.id);
                removeItem(itemId);
            });
        });
    }

    // Remove item from selection
    function removeItem(itemId) {
        const index = selectedItems.findIndex(item => item.id === itemId);
        if (index !== -1) {
            selectedItems.splice(index, 1);
            updateSelectedItems();
            loadClothingItems();
            updateAvatarLayers();
        }
    }

    // Update avatar layers based on selected items
    function updateAvatarLayers() {
        // Remove existing layers
        const existingLayers = document.querySelectorAll('.clothing-layer');
        existingLayers.forEach(layer => layer.remove());
        
        // Add new layers
        selectedItems.forEach(item => {
            if (item.layerImage) {
                const layerElement = document.createElement('img');
                layerElement.className = 'clothing-layer';
                layerElement.src = item.layerImage;
                layerElement.alt = item.name;
                layerElement.style.position = 'absolute';
                layerElement.style.top = '0';
                layerElement.style.left = '0';
                layerElement.style.width = '100%';
                layerElement.style.height = '100%';
                layerElement.style.objectFit = 'contain';
                
                document.querySelector('.avatar-container').appendChild(layerElement);
            }
        });
    }

    // Setup event listeners
    function setupEventListeners() {
        // Avatar gender selection
        avatarOptions.forEach(option => {
            option.addEventListener('click', function() {
                const gender = this.dataset.gender;
                
                // Update active state
                avatarOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
                
                // Update current gender
                currentGender = gender;
                
                // Update avatar image
                avatarImage.src = `../img/avatar-${gender}-full.png`;
                
                // Clear selected items
                selectedItems = [];
                updateSelectedItems();
                
                // Reload clothing items
                loadClothingItems();
                
                // Reload recommendations
                loadRecommendations();
            });
        });
        
        // Category buttons
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                const category = this.dataset.category;
                
                // Update active state
                categoryButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Update current category
                currentCategory = category;
                
                // Reload clothing items
                loadClothingItems();
            });
        });
        
        // Height slider
        if (heightSlider && heightValue) {
            heightSlider.addEventListener('input', function() {
                heightValue.textContent = this.value;
                
                // In a real app, this would update the avatar's height
            });
        }
        
        // Weight slider
        if (weightSlider && weightValue) {
            weightSlider.addEventListener('input', function() {
                weightValue.textContent = this.value;
                
                // In a real app, this would update the avatar's body shape
            });
        }
        
        // Skin tone options
        skinToneOptions.forEach(option => {
            option.addEventListener('click', function() {
                const tone = this.dataset.tone;
                
                // Update active state
                skinToneOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
                
                // In a real app, this would update the avatar's skin tone
            });
        });
        
        // Save outfit button
        if (saveOutfitBtn) {
            saveOutfitBtn.addEventListener('click', function() {
                if (selectedItems.length === 0) {
                    showNotification('Vui lòng chọn ít nhất một trang phục', 'warning');
                    return;
                }
                
                // Show modal
                if (savedOutfitModal) {
                    savedOutfitModal.classList.add('show');
                }
                
                // In a real app, this would save the outfit to the user's account
                showNotification('Đã lưu trang phục thành công!', 'success');
            });
        }
        
        // Share outfit button
        if (shareOutfitBtn) {
            shareOutfitBtn.addEventListener('click', function() {
                if (selectedItems.length === 0) {
                    showNotification('Vui lòng chọn ít nhất một trang phục', 'warning');
                    return;
                }
                
                // In a real app, this would open a share dialog
                showNotification('Đã sao chép đường dẫn chia sẻ!', 'success');
            });
        }
        
        // Add to cart button
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function() {
                if (selectedItems.length === 0) {
                    showNotification('Vui lòng chọn ít nhất một trang phục', 'warning');
                    return;
                }
                
                // In a real app, this would add the items to the cart
                showNotification('Đã thêm trang phục vào giỏ hàng!', 'success');
                
                // Update cart count
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    const currentCount = parseInt(cartCount.textContent) || 0;
                    cartCount.textContent = currentCount + selectedItems.length;
                }
            });
        }
        
        // Modal close button
        if (modalClose) {
            modalClose.addEventListener('click', function() {
                if (savedOutfitModal) {
                    savedOutfitModal.classList.remove('show');
                }
            });
        }
        
        // Preview controls
        if (rotateLeftBtn) {
            rotateLeftBtn.addEventListener('click', function() {
                avatarRotation -= 15;
                updateAvatarTransform();
            });
        }
        
        if (rotateRightBtn) {
            rotateRightBtn.addEventListener('click', function() {
                avatarRotation += 15;
                updateAvatarTransform();
            });
        }
        
        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', function() {
                avatarScale += 0.1;
                if (avatarScale > 1.5) avatarScale = 1.5;
                updateAvatarTransform();
            });
        }
        
        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', function() {
                avatarScale -= 0.1;
                if (avatarScale < 0.5) avatarScale = 0.5;
                updateAvatarTransform();
            });
        }
        
        if (resetViewBtn) {
            resetViewBtn.addEventListener('click', function() {
                avatarScale = 1;
                avatarRotation = 0;
                updateAvatarTransform();
            });
        }
    }

    // Update avatar transform
    function updateAvatarTransform() {
        const avatarContainer = document.querySelector('.avatar-container');
        if (avatarContainer) {
            avatarContainer.style.transform = `scale(${avatarScale}) rotate(${avatarRotation}deg)`;
        }
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        // Icon based on notification type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle notification-icon"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
                break;
            default:
                icon = '<i class="fas fa-info-circle notification-icon"></i>';
        }
        
        // Set notification content
        notification.innerHTML = `
            ${icon}
            <div class="notification-message">${message}</div>
        `;
        
        // Add notification to container
        container.appendChild(notification);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            
            // Remove from DOM after animation completes
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Initialize the app
    init();
});
