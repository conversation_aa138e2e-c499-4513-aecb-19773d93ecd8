/* Product Reviews Styles */
.reviews-section {
    margin-top: 40px;
    padding: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.reviews-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.reviews-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.write-review-btn {
    background-color: #ff6b6b;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.write-review-btn:hover {
    background-color: #ff5252;
}

.write-review-btn i {
    font-size: 14px;
}

.reviews-summary {
    display: flex;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.reviews-average {
    flex: 1;
    text-align: center;
    padding-right: 30px;
    border-right: 1px solid #eee;
}

.average-rating {
    font-size: 48px;
    font-weight: 700;
    color: #333;
    line-height: 1;
    margin-bottom: 10px;
}

.rating-stars {
    color: #FFD700;
    font-size: 24px;
    margin-bottom: 10px;
}

.rating-count {
    color: #666;
    font-size: 14px;
}

.reviews-breakdown {
    flex: 2;
    padding-left: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.rating-bar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.rating-label {
    width: 30px;
    font-size: 14px;
    color: #666;
}

.rating-progress {
    flex: 1;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    margin: 0 10px;
    overflow: hidden;
}

.rating-progress-fill {
    height: 100%;
    background-color: #FFD700;
}

.rating-percent {
    width: 40px;
    font-size: 14px;
    color: #666;
    text-align: right;
}

.reviews-list {
    margin-bottom: 30px;
}

.review-item {
    padding: 20px 0;
    border-bottom: 1px solid #eee;
}

.review-item:last-child {
    border-bottom: none;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.reviewer-info {
    display: flex;
    align-items: center;
}

.reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: #666;
    font-weight: 600;
}

.reviewer-name {
    font-weight: 600;
    color: #333;
}

.review-date {
    color: #999;
    font-size: 12px;
    margin-top: 2px;
}

.review-rating {
    color: #FFD700;
    font-size: 16px;
}

.review-content {
    margin-bottom: 10px;
    color: #333;
    line-height: 1.5;
}

.review-photos {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.review-photo {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
}

.review-photo:hover {
    transform: scale(1.05);
}

.review-actions {
    display: flex;
    gap: 15px;
}

.review-action {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
}

.review-action:hover {
    color: #ff6b6b;
}

.review-action i {
    font-size: 16px;
}

.review-form {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    display: none;
}

.review-form.active {
    display: block;
}

.form-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.rating-input {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.rating-input i {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
}

.rating-input i.active {
    color: #FFD700;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    border-color: #ff6b6b;
    outline: none;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn-cancel {
    background-color: #f0f0f0;
    color: #333;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-cancel:hover {
    background-color: #e0e0e0;
}

.btn-submit {
    background-color: #ff6b6b;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-submit:hover {
    background-color: #ff5252;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .reviews-summary {
        flex-direction: column;
    }
    
    .reviews-average {
        padding-right: 0;
        border-right: none;
        padding-bottom: 20px;
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .reviews-breakdown {
        padding-left: 0;
    }
}
