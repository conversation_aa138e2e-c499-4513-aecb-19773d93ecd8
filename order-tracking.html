<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="./styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/search.css">
    <title>Theo dõi đơn hàng | Fashion Store</title>

    <style>
        /* Order Tracking Styles */
        .tracking-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .tracking-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .tracking-header h1 {
            font-size: 28px;
            color: #333;
            margin-bottom: 15px;
        }

        .tracking-header p {
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .tracking-form {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            border-color: #ff6b6b;
            outline: none;
        }

        .btn-track {
            background-color: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }

        .btn-track:hover {
            background-color: #ff5252;
        }

        .tracking-result {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            display: none;
        }

        .tracking-result.active {
            display: block;
        }

        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .order-detail {
            flex: 1;
        }

        .order-detail h3 {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }

        .order-detail p {
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }

        .tracking-status {
            margin-bottom: 30px;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-title {
            font-size: 18px;
            color: #333;
            margin: 0;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .status-badge.delivered {
            background-color: #4CAF50;
            color: white;
        }

        .status-badge.in-transit {
            background-color: #2196F3;
            color: white;
        }

        .status-badge.processing {
            background-color: #FF9800;
            color: white;
        }

        .status-badge.pending {
            background-color: #9E9E9E;
            color: white;
        }

        .tracking-timeline {
            position: relative;
            padding-left: 30px;
        }

        .tracking-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 7px;
            width: 2px;
            background-color: #ddd;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 25px;
        }

        .timeline-item:last-child {
            padding-bottom: 0;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -30px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #ddd;
        }

        .timeline-item.active::before {
            background-color: #ff6b6b;
            border-color: #ff6b6b;
        }

        .timeline-date {
            font-size: 14px;
            color: #999;
            margin-bottom: 5px;
        }

        .timeline-content {
            font-size: 16px;
            color: #333;
        }

        .timeline-location {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .order-items {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .order-items-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }

        .order-item {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .order-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 15px;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .item-variant {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .item-price {
            font-size: 16px;
            color: #ff6b6b;
            font-weight: 600;
        }

        .tracking-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn-action {
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #ff6b6b;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background-color: #ff5252;
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
            border: none;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .tracking-map {
            height: 300px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 30px;
            position: relative;
            overflow: hidden;
        }

        .tracking-map img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        @media (max-width: 768px) {
            .order-info {
                flex-direction: column;
                gap: 20px;
            }

            .tracking-actions {
                flex-direction: column;
                gap: 15px;
            }

            .btn-action {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>


    <!-- Order Tracking Content -->
    <div class="tracking-container">
        <div class="tracking-header">
            <h1>Theo dõi đơn hàng</h1>
            <p>Nhập mã đơn hàng của bạn để theo dõi trạng thái và vị trí hiện tại của đơn hàng.</p>
        </div>

        <div class="tracking-form">
            <div class="form-group">
                <label for="order-id" class="form-label">Mã đơn hàng</label>
                <input type="text" id="order-id" class="form-control" placeholder="Nhập mã đơn hàng (VD: FS123456789)">
            </div>
            <div class="form-group">
                <label for="email" class="form-label">Email</label>
                <input type="email" id="email" class="form-control" placeholder="Nhập email đặt hàng">
            </div>
            <button type="button" id="track-btn" class="btn-track">Theo dõi đơn hàng</button>
        </div>

        <div class="tracking-result" id="tracking-result">
            <div class="order-info">
                <div class="order-detail">
                    <h3>Mã đơn hàng</h3>
                    <p id="result-order-id">FS123456789</p>
                </div>
                <div class="order-detail">
                    <h3>Ngày đặt hàng</h3>
                    <p id="result-order-date">15/06/2023</p>
                </div>
                <div class="order-detail">
                    <h3>Dự kiến giao hàng</h3>
                    <p id="result-delivery-date">20/06/2023</p>
                </div>
            </div>

            <div class="tracking-status">
                <div class="status-header">
                    <h2 class="status-title">Trạng thái đơn hàng</h2>
                    <span class="status-badge in-transit" id="status-badge">Đang vận chuyển</span>
                </div>

                <div class="tracking-timeline">
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-1">18/06/2023 - 10:30</div>
                        <div class="timeline-content">Đơn hàng đang được vận chuyển</div>
                        <div class="timeline-location">Trung tâm phân phối TP.HCM</div>
                    </div>
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-2">17/06/2023 - 15:45</div>
                        <div class="timeline-content">Đơn hàng đã được đóng gói</div>
                        <div class="timeline-location">Kho hàng Fashion Store</div>
                    </div>
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-3">16/06/2023 - 09:20</div>
                        <div class="timeline-content">Đơn hàng đã được xác nhận</div>
                        <div class="timeline-location">Hệ thống Fashion Store</div>
                    </div>
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-4">15/06/2023 - 14:10</div>
                        <div class="timeline-content">Đơn hàng đã được tạo</div>
                        <div class="timeline-location">Hệ thống Fashion Store</div>
                    </div>
                </div>
            </div>

            <div class="tracking-map">
                <img src="https://maps.googleapis.com/maps/api/staticmap?center=10.7769,106.7009&zoom=12&size=800x300&markers=color:red%7Clabel:A%7C10.7769,106.7009&key=YOUR_API_KEY" alt="Tracking Map">
            </div>

            <div class="order-items">
                <h3 class="order-items-title">Sản phẩm trong đơn hàng</h3>
                <div class="order-item">
                    <img src="./img/men-1.jpg" alt="Áo sơ mi nam trắng" class="item-image">
                    <div class="item-details">
                        <div class="item-name">Áo sơ mi nam trắng</div>
                        <div class="item-variant">Size: L | Màu: Trắng</div>
                        <div class="item-price">350.000₫ x 1</div>
                    </div>
                </div>
                <div class="order-item">
                    <img src="./img/men-3.jpg" alt="Quần jeans nam xanh" class="item-image">
                    <div class="item-details">
                        <div class="item-name">Quần jeans nam xanh</div>
                        <div class="item-variant">Size: 32 | Màu: Xanh đậm</div>
                        <div class="item-price">450.000₫ x 1</div>
                    </div>
                </div>
            </div>

            <div class="tracking-actions">
                <button class="btn-action btn-secondary" id="contact-support">Liên hệ hỗ trợ</button>
                <button class="btn-action btn-primary" id="view-order-details">Xem chi tiết đơn hàng</button>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>
    <script src="./script/user-display.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Order tracking functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Track button click event
            const trackBtn = document.getElementById('track-btn');
            const trackingResult = document.getElementById('tracking-result');
            const orderIdInput = document.getElementById('order-id');
            const emailInput = document.getElementById('email');

            if (trackBtn) {
                trackBtn.addEventListener('click', function() {
                    const orderId = orderIdInput.value.trim();
                    const email = emailInput.value.trim();

                    if (!orderId) {
                        alert('Vui lòng nhập mã đơn hàng');
                        return;
                    }

                    if (!email) {
                        alert('Vui lòng nhập email');
                        return;
                    }

                    // Update result order ID
                    document.getElementById('result-order-id').textContent = orderId;

                    // Show tracking result
                    trackingResult.classList.add('active');

                    // Scroll to tracking result
                    trackingResult.scrollIntoView({ behavior: 'smooth' });
                });
            }

            // Contact support button click event
            const contactSupportBtn = document.getElementById('contact-support');
            if (contactSupportBtn) {
                contactSupportBtn.addEventListener('click', function() {
                    alert('Bạn sẽ được chuyển đến trang hỗ trợ khách hàng');
                });
            }

            // View order details button click event
            const viewOrderDetailsBtn = document.getElementById('view-order-details');
            if (viewOrderDetailsBtn) {
                viewOrderDetailsBtn.addEventListener('click', function() {
                    alert('Bạn sẽ được chuyển đến trang chi tiết đơn hàng');
                });
            }
        });
    </script>
</body>
</html>
