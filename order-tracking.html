<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="./styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/search.css">
    <title>Theo dõi đơn hàng | Fashion Store</title>

    <style>
        /* Order Tracking Styles */
        .tracking-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .tracking-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .tracking-header h1 {
            font-size: 28px;
            color: #333;
            margin-bottom: 15px;
        }

        .tracking-header p {
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .tracking-form {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            border-color: #ff6b6b;
            outline: none;
        }

        .btn-track {
            background-color: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }

        .btn-track:hover {
            background-color: #ff5252;
        }

        .tracking-result {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            display: none;
        }

        .tracking-result.active {
            display: block;
        }

        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .order-detail {
            flex: 1;
        }

        .order-detail h3 {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }

        .order-detail p {
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }

        .tracking-status {
            margin-bottom: 30px;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-title {
            font-size: 18px;
            color: #333;
            margin: 0;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .status-badge.delivered {
            background-color: #4CAF50;
            color: white;
        }

        .status-badge.in-transit {
            background-color: #2196F3;
            color: white;
        }

        .status-badge.processing {
            background-color: #FF9800;
            color: white;
        }

        .status-badge.pending {
            background-color: #9E9E9E;
            color: white;
        }

        .tracking-timeline {
            position: relative;
            padding-left: 30px;
        }

        .tracking-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 7px;
            width: 2px;
            background-color: #ddd;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 25px;
        }

        .timeline-item:last-child {
            padding-bottom: 0;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -30px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #ddd;
        }

        .timeline-item.active::before {
            background-color: #ff6b6b;
            border-color: #ff6b6b;
        }

        .timeline-date {
            font-size: 14px;
            color: #999;
            margin-bottom: 5px;
        }

        .timeline-content {
            font-size: 16px;
            color: #333;
        }

        .timeline-location {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .order-items {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .order-items-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }

        .order-item {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .order-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 15px;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .item-variant {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .item-price {
            font-size: 16px;
            color: #ff6b6b;
            font-weight: 600;
        }

        .tracking-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn-action {
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #ff6b6b;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background-color: #ff5252;
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
            border: none;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .tracking-map {
            height: 300px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 30px;
            position: relative;
            overflow: hidden;
        }

        .tracking-map img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        @media (max-width: 768px) {
            .order-info {
                flex-direction: column;
                gap: 20px;
            }

            .tracking-actions {
                flex-direction: column;
                gap: 15px;
            }

            .btn-action {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
        </div>
    </div>

    <!-- Header Section -->
    <header id="header">
        <!-- Top Announcement Bar -->
        <div class="announcement-bar">
            <p><i class="fas fa-truck"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ</p>
        </div>

        <!-- Main Navigation -->
        <nav class="main-nav">
            <div class="container">
                <div class="nav-wrapper">
                    <!-- Mobile Menu Toggle -->
                    <div class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </div>

                    <!-- Logo -->
                    <div class="logo">
                        <a href="index.html">
                            <img src="./img logo/logo 1.jpg" alt="Fashion Store Logo">
                        </a>
                    </div>

                    <!-- Navigation Menu -->
                    <ul class="nav-menu">
                        <li class="nav-item has-dropdown">
                            <a href="./women.html">Nữ</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="./womenproducts.html">Áo sơ mi</a></li>
                                        <li><a href="./womenproducts.html">Áo thun</a></li>
                                        <li><a href="./womenproducts.html">Quần jean</a></li>
                                        <li><a href="./womenproducts.html">Váy & Đầm</a></li>
                                        <li><a href="./womenproducts.html">Áo khoác</a></li>
                                        <li><a href="./womenproducts.html">Đồ thể thao</a></li>
                                        <li><a href="./womenproducts.html">Đồ ngủ</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="./womenproducts.html">Mùa hè 2024</a></li>
                                        <li><a href="./womenproducts.html">Công sở thanh lịch</a></li>
                                        <li><a href="./womenproducts.html">Dạo phố năng động</a></li>
                                        <li><a href="./womenproducts.html">Dự tiệc sang trọng</a></li>
                                        <li><a href="./womenproducts.html">Đồ thể thao</a></li>
                                        <li><a href="./womenproducts.html">Hàng mới về</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="./img womens/women 15.webp" alt="Women's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="./womenproducts.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="./men.html">Nam</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="./menproducts.html">Áo sơ mi</a></li>
                                        <li><a href="./menproducts.html">Áo thun</a></li>
                                        <li><a href="./menproducts.html">Quần jean</a></li>
                                        <li><a href="./menproducts.html">Quần kaki</a></li>
                                        <li><a href="./menproducts.html">Áo khoác</a></li>
                                        <li><a href="./menproducts.html">Đồ thể thao</a></li>
                                        <li><a href="./menproducts.html">Đồ công sở</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="./menproducts.html">Mùa hè 2024</a></li>
                                        <li><a href="./menproducts.html">Công sở lịch lãm</a></li>
                                        <li><a href="./menproducts.html">Thể thao năng động</a></li>
                                        <li><a href="./menproducts.html">Dạo phố cá tính</a></li>
                                        <li><a href="./menproducts.html">Hàng mới về</a></li>
                                        <li><a href="./menproducts.html">Bán chạy nhất</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="./img mens/men 10.jpg" alt="Men's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="./menproducts.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="./pages/kids.html">Trẻ em</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Bé gái</h3>
                                    <ul>
                                        <li><a href="./pages/kids.html">Áo</a></li>
                                        <li><a href="./pages/kids.html">Quần</a></li>
                                        <li><a href="./pages/kids.html">Váy đầm</a></li>
                                        <li><a href="./pages/kids.html">Đồ ngủ</a></li>
                                        <li><a href="./pages/kids.html">Đồ thể thao</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bé trai</h3>
                                    <ul>
                                        <li><a href="./pages/kids.html">Áo</a></li>
                                        <li><a href="./pages/kids.html">Quần</a></li>
                                        <li><a href="./pages/kids.html">Đồ ngủ</a></li>
                                        <li><a href="./pages/kids.html">Đồ thể thao</a></li>
                                        <li><a href="./pages/kids.html">Bộ quần áo</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="./img womens/women 12.webp" alt="Kids Collection">
                                    <h4>Bộ sưu tập mới cho bé</h4>
                                    <a href="./pages/kids.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="./pages/sale.html">Khuyến mãi</a>
                        </li>
                        <li class="nav-item">
                            <a href="./pages/about.html">Giới thiệu</a>
                        </li>
                    </ul>

                    <!-- Right Navigation -->
                    <div class="nav-right">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="Tìm kiếm...">
                            <button id="search-btn"><i class="fas fa-search"></i></button>
                        </div>
                        <div class="nav-icons">
                            <a href="./pages/account.html" class="nav-icon" title="Tài khoản">
                                <i class="fas fa-user"></i>
                            </a>
                            <a href="./pages/wishlist.html" class="nav-icon" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </a>
                            <a href="./AddCart.html" class="nav-icon cart-icon" title="Giỏ hàng">
                                <i class="fas fa-shopping-bag"></i>
                                <span class="cart-count">0</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Order Tracking Content -->
    <div class="tracking-container">
        <div class="tracking-header">
            <h1>Theo dõi đơn hàng</h1>
            <p>Nhập mã đơn hàng của bạn để theo dõi trạng thái và vị trí hiện tại của đơn hàng.</p>
        </div>

        <div class="tracking-form">
            <div class="form-group">
                <label for="order-id" class="form-label">Mã đơn hàng</label>
                <input type="text" id="order-id" class="form-control" placeholder="Nhập mã đơn hàng (VD: FS123456789)">
            </div>
            <div class="form-group">
                <label for="email" class="form-label">Email</label>
                <input type="email" id="email" class="form-control" placeholder="Nhập email đặt hàng">
            </div>
            <button type="button" id="track-btn" class="btn-track">Theo dõi đơn hàng</button>
        </div>

        <div class="tracking-result" id="tracking-result">
            <div class="order-info">
                <div class="order-detail">
                    <h3>Mã đơn hàng</h3>
                    <p id="result-order-id">FS123456789</p>
                </div>
                <div class="order-detail">
                    <h3>Ngày đặt hàng</h3>
                    <p id="result-order-date">15/06/2023</p>
                </div>
                <div class="order-detail">
                    <h3>Dự kiến giao hàng</h3>
                    <p id="result-delivery-date">20/06/2023</p>
                </div>
            </div>

            <div class="tracking-status">
                <div class="status-header">
                    <h2 class="status-title">Trạng thái đơn hàng</h2>
                    <span class="status-badge in-transit" id="status-badge">Đang vận chuyển</span>
                </div>

                <div class="tracking-timeline">
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-1">18/06/2023 - 10:30</div>
                        <div class="timeline-content">Đơn hàng đang được vận chuyển</div>
                        <div class="timeline-location">Trung tâm phân phối TP.HCM</div>
                    </div>
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-2">17/06/2023 - 15:45</div>
                        <div class="timeline-content">Đơn hàng đã được đóng gói</div>
                        <div class="timeline-location">Kho hàng Fashion Store</div>
                    </div>
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-3">16/06/2023 - 09:20</div>
                        <div class="timeline-content">Đơn hàng đã được xác nhận</div>
                        <div class="timeline-location">Hệ thống Fashion Store</div>
                    </div>
                    <div class="timeline-item active">
                        <div class="timeline-date" id="timeline-date-4">15/06/2023 - 14:10</div>
                        <div class="timeline-content">Đơn hàng đã được tạo</div>
                        <div class="timeline-location">Hệ thống Fashion Store</div>
                    </div>
                </div>
            </div>

            <div class="tracking-map">
                <img src="https://maps.googleapis.com/maps/api/staticmap?center=10.7769,106.7009&zoom=12&size=800x300&markers=color:red%7Clabel:A%7C10.7769,106.7009&key=YOUR_API_KEY" alt="Tracking Map">
            </div>

            <div class="order-items">
                <h3 class="order-items-title">Sản phẩm trong đơn hàng</h3>
                <div class="order-item">
                    <img src="./img/men-1.jpg" alt="Áo sơ mi nam trắng" class="item-image">
                    <div class="item-details">
                        <div class="item-name">Áo sơ mi nam trắng</div>
                        <div class="item-variant">Size: L | Màu: Trắng</div>
                        <div class="item-price">350.000₫ x 1</div>
                    </div>
                </div>
                <div class="order-item">
                    <img src="./img/men-3.jpg" alt="Quần jeans nam xanh" class="item-image">
                    <div class="item-details">
                        <div class="item-name">Quần jeans nam xanh</div>
                        <div class="item-variant">Size: 32 | Màu: Xanh đậm</div>
                        <div class="item-price">450.000₫ x 1</div>
                    </div>
                </div>
            </div>

            <div class="tracking-actions">
                <button class="btn-action btn-secondary" id="contact-support">Liên hệ hỗ trợ</button>
                <button class="btn-action btn-primary" id="view-order-details">Xem chi tiết đơn hàng</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Thông tin</h3>
                    <ul>
                        <li><a href="./pages/about.html">Về chúng tôi</a></li>
                        <li><a href="./pages/contact.html">Liên hệ</a></li>
                        <li><a href="./blog.html">Tin tức & Xu hướng</a></li>
                        <li><a href="./pages/faq.html">Câu hỏi thường gặp</a></li>
                        <li><a href="./pages/stores.html">Hệ thống cửa hàng</a></li>
                        <li><a href="./pages/careers.html">Tuyển dụng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Dịch vụ khách hàng</h3>
                    <ul>
                        <li><a href="./pages/account.html">Tài khoản của tôi</a></li>
                        <li><a href="./pages/orders.html">Theo dõi đơn hàng</a></li>
                        <li><a href="./pages/shipping.html">Chính sách vận chuyển</a></li>
                        <li><a href="./pages/returns.html">Chính sách đổi trả</a></li>
                        <li><a href="./pages/size-guide.html">Hướng dẫn chọn size</a></li>
                        <li><a href="./pages/gift-cards.html">Thẻ quà tặng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Liên hệ với chúng tôi</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                123 Đường Nguyễn Trãi, Quận 1, TP. Hồ Chí Minh
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-text">
                                +84 (0) 123 456 789
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <EMAIL>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                Thứ Hai - Chủ Nhật: 9:00 - 21:00
                            </div>
                        </div>
                    </div>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <div class="footer-newsletter">
                        <h4>Đăng ký nhận tin</h4>
                        <p>Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
                        <form class="footer-newsletter-form">
                            <input type="email" placeholder="Email của bạn" required>
                            <button type="submit"><i class="fas fa-paper-plane"></i></button>
                        </form>
                    </div>
                    <div class="app-download">
                        <h4>Tải ứng dụng</h4>
                        <div class="app-buttons">
                            <a href="#"><img src="../img logo/images.png" alt="App Store"></a>
                            <a href="#"><img src="../img logo/img 2.png" alt="Google Play"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Fashion Store. Tất cả các quyền được bảo lưu. | Thiết kế bởi <a href="#">MindX JSA08</a></p>
            </div>
        </div>
    </footer>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/lucky-wheel.js"></script>
    <script src="./script/chatbot.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/search.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Track button click event
            const trackBtn = document.getElementById('track-btn');
            const trackingResult = document.getElementById('tracking-result');
            const orderIdInput = document.getElementById('order-id');
            const emailInput = document.getElementById('email');

            if (trackBtn) {
                trackBtn.addEventListener('click', function() {
                    const orderId = orderIdInput.value.trim();
                    const email = emailInput.value.trim();

                    if (!orderId) {
                        alert('Vui lòng nhập mã đơn hàng');
                        return;
                    }

                    if (!email) {
                        alert('Vui lòng nhập email');
                        return;
                    }

                    // Update result order ID
                    document.getElementById('result-order-id').textContent = orderId;

                    // Show tracking result
                    trackingResult.classList.add('active');

                    // Scroll to tracking result
                    trackingResult.scrollIntoView({ behavior: 'smooth' });
                });
            }

            // Contact support button click event
            const contactSupportBtn = document.getElementById('contact-support');
            if (contactSupportBtn) {
                contactSupportBtn.addEventListener('click', function() {
                    alert('Bạn sẽ được chuyển đến trang hỗ trợ khách hàng');
                });
            }

            // View order details button click event
            const viewOrderDetailsBtn = document.getElementById('view-order-details');
            if (viewOrderDetailsBtn) {
                viewOrderDetailsBtn.addEventListener('click', function() {
                    alert('Bạn sẽ được chuyển đến trang chi tiết đơn hàng');
                });
            }

            // Hiển thị tên người dùng đã đăng nhập
            function displayUsername() {
                const user = JSON.parse(localStorage.getItem('user'));
                if (user && user.isLoggedIn) {
                    const userIcon = document.querySelector('.nav-icons .nav-icon[title="Tài khoản"]');
                    if (userIcon) {
                        // Thay đổi icon thành tên người dùng
                        const userName = user.name || user.email.split('@')[0];
                        userIcon.innerHTML = `<span class="user-name">${userName}</span>`;
                        userIcon.title = `Xin chào, ${userName}`;

                        // Thêm sự kiện click để hiển thị menu tài khoản
                        userIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showAccountMenu(this);
                        });
                    }
                }
            }

            // Hiển thị menu tài khoản
            function showAccountMenu(element) {
                // Xóa menu cũ nếu có
                const existingMenu = document.querySelector('.account-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }

                // Tạo menu tài khoản
                const accountMenu = document.createElement('div');
                accountMenu.className = 'account-menu';

                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user'));
                const userName = user.name || user.email.split('@')[0];

                // Thiết lập nội dung menu
                accountMenu.innerHTML = `
                    <div class="account-menu-header">
                        <div class="account-menu-user">
                            <div class="account-menu-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="account-menu-info">
                                <div class="account-menu-name">${userName}</div>
                                <div class="account-menu-email">${user.email}</div>
                            </div>
                        </div>
                    </div>
                    <div class="account-menu-body">
                        <a href="./pages/account.html" class="account-menu-item">
                            <i class="fas fa-user"></i> Tài khoản của tôi
                        </a>
                        <a href="./pages/orders.html" class="account-menu-item">
                            <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                        </a>
                        <a href="./pages/wishlist.html" class="account-menu-item">
                            <i class="fas fa-heart"></i> Danh sách yêu thích
                        </a>
                        <a href="./pages/settings.html" class="account-menu-item">
                            <i class="fas fa-cog"></i> Cài đặt
                        </a>
                        <div class="account-menu-divider"></div>
                        <a href="#" class="account-menu-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Đăng xuất
                        </a>
                    </div>
                `;

                // Định vị menu
                const rect = element.getBoundingClientRect();
                accountMenu.style.top = `${rect.bottom + window.scrollY}px`;
                accountMenu.style.right = `${window.innerWidth - rect.right}px`;

                // Thêm vào body
                document.body.appendChild(accountMenu);

                // Hiển thị với hiệu ứng
                setTimeout(() => {
                    accountMenu.classList.add('show');
                }, 10);

                // Thêm sự kiện cho nút đăng xuất
                const logoutBtn = accountMenu.querySelector('.logout-btn');
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });

                // Đóng menu khi click bên ngoài
                document.addEventListener('click', function closeMenu(e) {
                    if (!accountMenu.contains(e.target) && e.target !== element) {
                        accountMenu.classList.remove('show');
                        setTimeout(() => {
                            accountMenu.remove();
                        }, 300);
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }

            // Hàm đăng xuất
            function logout() {
                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user')) || {};

                // Cập nhật trạng thái đăng nhập
                user.isLoggedIn = false;

                // Lưu vào localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Hiển thị thông báo
                alert('Đăng xuất thành công');

                // Khôi phục icon người dùng
                const userIcon = document.querySelector('.nav-icons .nav-icon[title="Xin chào, ' + (user.name || user.email.split('@')[0]) + '"]');
                if (userIcon) {
                    userIcon.innerHTML = '<i class="fas fa-user"></i>';
                    userIcon.title = 'Tài khoản';
                }

                // Chuyển hướng về trang chủ sau một khoảng thời gian ngắn
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            }

            // Gọi hàm hiển thị tên người dùng
            displayUsername();
        });
    </script>
</body>
</html>
