// Checkout Page Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    }
    
    // Load cart items
    loadCartItems();
    
    // Handle shipping method selection
    const shippingOptions = document.querySelectorAll('input[name="shipping"]');
    shippingOptions.forEach(option => {
        option.addEventListener('change', updateOrderSummary);
    });
    
    // Handle payment method selection
    const paymentOptions = document.querySelectorAll('input[name="payment"]');
    paymentOptions.forEach(option => {
        option.addEventListener('change', function() {
            // Hide all payment details
            const paymentDetails = document.querySelectorAll('.payment-details');
            paymentDetails.forEach(detail => {
                detail.style.display = 'none';
            });
            
            // Show selected payment details
            const selectedPaymentId = this.value;
            const selectedPaymentDetails = document.getElementById(`${selectedPaymentId}-details`);
            if (selectedPaymentDetails) {
                selectedPaymentDetails.style.display = 'block';
            }
        });
    });
    
    // Handle promo code application
    const applyPromoBtn = document.getElementById('apply-promo');
    if (applyPromoBtn) {
        applyPromoBtn.addEventListener('click', function() {
            const promoInput = document.getElementById('promo');
            const promoCode = promoInput.value.trim();
            
            if (promoCode) {
                // In a real application, this would validate the promo code with the server
                // For this demo, we'll just apply a 10% discount for any code
                applyDiscount(10);
                
                // Show success message
                showNotification('Mã giảm giá đã được áp dụng!', 'success');
                
                // Disable the input and button
                promoInput.disabled = true;
                applyPromoBtn.disabled = true;
            } else {
                showNotification('Vui lòng nhập mã giảm giá', 'error');
            }
        });
    }
    
    // Handle place order button
    const placeOrderBtn = document.getElementById('place-order');
    if (placeOrderBtn) {
        placeOrderBtn.addEventListener('click', function() {
            // Validate form
            if (validateForm()) {
                // In a real application, this would submit the order to the server
                // For this demo, we'll just redirect to the order confirmation page
                
                // Save order details to localStorage for the confirmation page
                saveOrderDetails();
                
                // Redirect to confirmation page
                window.location.href = 'end.html';
            }
        });
    }
    
    // Format card inputs
    const cardNumberInput = document.getElementById('card-number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            this.value = formatCardNumber(this.value);
        });
    }
    
    const cardExpiryInput = document.getElementById('card-expiry');
    if (cardExpiryInput) {
        cardExpiryInput.addEventListener('input', function() {
            this.value = formatCardExpiry(this.value);
        });
    }
    
    const cardCvcInput = document.getElementById('card-cvc');
    if (cardCvcInput) {
        cardCvcInput.addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '').slice(0, 4);
        });
    }
});

// Load cart items from localStorage
function loadCartItems() {
    const orderItemsContainer = document.getElementById('order-items');
    if (!orderItemsContainer) return;
    
    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Clear container
    orderItemsContainer.innerHTML = '';
    
    if (cart.length === 0) {
        // Show empty cart message
        orderItemsContainer.innerHTML = `
            <div class="empty-cart">
                <p>Giỏ hàng của bạn đang trống</p>
                <a href="index.html" class="btn btn-primary">Tiếp tục mua sắm</a>
            </div>
        `;
        
        // Disable place order button
        const placeOrderBtn = document.getElementById('place-order');
        if (placeOrderBtn) {
            placeOrderBtn.disabled = true;
        }
        
        return;
    }
    
    // Add items to container
    let subtotal = 0;
    
    cart.forEach(item => {
        // Calculate item total
        const price = parseInt(item.price.replace(/\D/g, ''));
        const quantity = item.quantity || 1;
        const itemTotal = price * quantity;
        
        // Add to subtotal
        subtotal += itemTotal;
        
        // Create item element
        const itemElement = document.createElement('div');
        itemElement.className = 'order-item';
        
        itemElement.innerHTML = `
            <div class="order-item-image">
                <img src="${item.image}" alt="${item.name}">
            </div>
            <div class="order-item-details">
                <div class="order-item-name">${item.name}</div>
                <div class="order-item-variant">
                    <span>Màu: ${item.color || 'N/A'}</span> | 
                    <span>Size: ${item.size || 'N/A'}</span> | 
                    <span>SL: ${quantity}</span>
                </div>
                <div class="order-item-price">
                    <span>${formatCurrency(price)}đ</span>
                    <span>${formatCurrency(itemTotal)}đ</span>
                </div>
            </div>
        `;
        
        orderItemsContainer.appendChild(itemElement);
    });
    
    // Update order summary
    updateOrderSummary(subtotal);
}

// Update order summary
function updateOrderSummary(subtotal) {
    // If subtotal is not provided, calculate from cart
    if (typeof subtotal !== 'number') {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        subtotal = 0;
        
        cart.forEach(item => {
            const price = parseInt(item.price.replace(/\D/g, ''));
            const quantity = item.quantity || 1;
            subtotal += price * quantity;
        });
    }
    
    // Get shipping cost
    let shippingCost = 0;
    const expressShipping = document.getElementById('express-shipping');
    if (expressShipping && expressShipping.checked) {
        shippingCost = 50000; // 50.000đ for express shipping
    }
    
    // Get discount
    const discount = parseInt(localStorage.getItem('checkout_discount') || 0);
    
    // Calculate total
    const total = subtotal + shippingCost - discount;
    
    // Update DOM
    const subtotalElement = document.getElementById('subtotal');
    const shippingCostElement = document.getElementById('shipping-cost');
    const discountElement = document.getElementById('discount');
    const totalElement = document.getElementById('total');
    
    if (subtotalElement) subtotalElement.textContent = `${formatCurrency(subtotal)}đ`;
    if (shippingCostElement) shippingCostElement.textContent = shippingCost > 0 ? `${formatCurrency(shippingCost)}đ` : 'Miễn phí';
    if (discountElement) discountElement.textContent = discount > 0 ? `-${formatCurrency(discount)}đ` : '0đ';
    if (totalElement) totalElement.textContent = `${formatCurrency(total)}đ`;
    
    // Save to localStorage for the confirmation page
    localStorage.setItem('checkout_subtotal', subtotal);
    localStorage.setItem('checkout_shipping', shippingCost);
    localStorage.setItem('checkout_discount', discount);
    localStorage.setItem('checkout_total', total);
}

// Apply discount
function applyDiscount(percentage) {
    const subtotal = parseInt(localStorage.getItem('checkout_subtotal') || 0);
    const discount = Math.round(subtotal * (percentage / 100));
    
    localStorage.setItem('checkout_discount', discount);
    updateOrderSummary();
}

// Validate form
function validateForm() {
    // Get required fields
    const requiredFields = document.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    // Check each field
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
            field.classList.add('error');
            
            // Add error message if it doesn't exist
            const errorMessage = field.parentElement.querySelector('.error-message');
            if (!errorMessage) {
                const message = document.createElement('div');
                message.className = 'error-message';
                message.textContent = 'Vui lòng điền thông tin này';
                field.parentElement.appendChild(message);
            }
        } else {
            field.classList.remove('error');
            
            // Remove error message if it exists
            const errorMessage = field.parentElement.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
        }
    });
    
    // Check payment method
    const selectedPayment = document.querySelector('input[name="payment"]:checked');
    if (selectedPayment && selectedPayment.value === 'credit-card') {
        // Validate card number
        const cardNumber = document.getElementById('card-number');
        if (cardNumber && cardNumber.value.replace(/\s/g, '').length < 16) {
            isValid = false;
            cardNumber.classList.add('error');
            showNotification('Số thẻ không hợp lệ', 'error');
        }
        
        // Validate expiry date
        const cardExpiry = document.getElementById('card-expiry');
        if (cardExpiry && !isValidExpiry(cardExpiry.value)) {
            isValid = false;
            cardExpiry.classList.add('error');
            showNotification('Ngày hết hạn không hợp lệ', 'error');
        }
        
        // Validate CVC
        const cardCvc = document.getElementById('card-cvc');
        if (cardCvc && cardCvc.value.length < 3) {
            isValid = false;
            cardCvc.classList.add('error');
            showNotification('Mã bảo mật không hợp lệ', 'error');
        }
    }
    
    if (!isValid) {
        showNotification('Vui lòng điền đầy đủ thông tin', 'error');
    }
    
    return isValid;
}

// Save order details to localStorage
function saveOrderDetails() {
    // Get customer information
    const firstName = document.getElementById('first-name').value;
    const lastName = document.getElementById('last-name').value;
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    
    // Get shipping address
    const country = document.getElementById('country').value;
    const address = document.getElementById('address').value;
    const address2 = document.getElementById('address2').value;
    const city = document.getElementById('city').value;
    const zip = document.getElementById('zip').value;
    
    // Get shipping method
    const shippingMethod = document.querySelector('input[name="shipping"]:checked').value;
    
    // Get payment method
    const paymentMethod = document.querySelector('input[name="payment"]:checked').value;
    
    // Create order object
    const order = {
        customer: {
            firstName,
            lastName,
            email,
            phone
        },
        shipping: {
            country,
            address,
            address2,
            city,
            zip,
            method: shippingMethod
        },
        payment: {
            method: paymentMethod
        },
        items: JSON.parse(localStorage.getItem('cart')) || [],
        subtotal: parseInt(localStorage.getItem('checkout_subtotal') || 0),
        shipping: parseInt(localStorage.getItem('checkout_shipping') || 0),
        discount: parseInt(localStorage.getItem('checkout_discount') || 0),
        total: parseInt(localStorage.getItem('checkout_total') || 0),
        date: new Date().toISOString(),
        orderNumber: generateOrderNumber()
    };
    
    // Save to localStorage
    localStorage.setItem('current_order', JSON.stringify(order));
    
    // Clear cart
    // localStorage.removeItem('cart');
}

// Generate random order number
function generateOrderNumber() {
    const prefix = 'FS';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
}

// Format card number (add spaces every 4 digits)
function formatCardNumber(value) {
    const v = value.replace(/\s+/g, '').replace(/\D/g, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
        parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
        return parts.join(' ');
    } else {
        return value;
    }
}

// Format card expiry (MM/YY)
function formatCardExpiry(value) {
    const v = value.replace(/\s+/g, '').replace(/\D/g, '');
    
    if (v.length >= 3) {
        return `${v.slice(0, 2)}/${v.slice(2, 4)}`;
    } else {
        return v;
    }
}

// Check if expiry date is valid
function isValidExpiry(value) {
    const parts = value.split('/');
    if (parts.length !== 2) return false;
    
    const month = parseInt(parts[0]);
    const year = parseInt(`20${parts[1]}`);
    
    if (isNaN(month) || isNaN(year)) return false;
    if (month < 1 || month > 12) return false;
    
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    if (year < currentYear) return false;
    if (year === currentYear && month < currentMonth) return false;
    
    return true;
}

// Format currency
function formatCurrency(value) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }
    
    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;
    
    // Add notification to container
    container.appendChild(notification);
    
    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
