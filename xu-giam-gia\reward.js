// Lấy xu từ LocalStorage
let coins = localStorage.getItem("coins") ? parseInt(localStorage.getItem("coins")) : 0;
document.getElementById("coin-count").textContent = coins;

// Quay số nhận xu ngẫu nhiên
function spinWheel() {
    let reward = Math.floor(Math.random() * 50) + 10; // Nhận từ 10 đến 50 xu
    coins += reward;
    localStorage.setItem("coins", coins);
    document.getElementById("coin-count").textContent = coins;
    alert(`🎉 Bạn nhận được ${reward} xu!`);

    // Cập nhật xu vào cart.html
    localStorage.setItem("cartCoins", coins);
}

// Đổi xu lấy mã giảm giá & lưu sang cart.html
function redeemCoupon(amount) {
    if (coins >= amount) {
        coins -= amount;
        localStorage.setItem("coins", coins);
        document.getElementById("coin-count").textContent = coins;

        let discount = amount === 50 ? 0.05 : 0.1; // 5% hoặc 10%
        localStorage.setItem("discountRate", discount);
        alert(`✅ Bạn đã đổi ${amount} xu để giảm giá ${discount * 100}% trong giỏ hàng!`);
        window.location.href = "../AddCart.html"; // Chuyển sang giỏ hàng sau khi đổi xu
    } else {
        alert("❌ Bạn không đủ xu để đổi!");
    }
}

