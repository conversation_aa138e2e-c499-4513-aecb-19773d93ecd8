// Enhanced Authentication Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Login Tabs
    const loginTabs = document.querySelectorAll('.login-tab');
    const tabContents = document.querySelectorAll('.login-tab-content');

    if (loginTabs.length > 0) {
        loginTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                loginTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Show selected tab content
                const tabId = this.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
    }

    // Toggle Password Visibility
    const togglePasswordBtns = document.querySelectorAll('.toggle-password');

    if (togglePasswordBtns.length > 0) {
        togglePasswordBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const passwordInput = this.parentElement.querySelector('input');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    this.innerHTML = '<i class="far fa-eye-slash"></i>';
                } else {
                    passwordInput.type = 'password';
                    this.innerHTML = '<i class="far fa-eye"></i>';
                }
            });
        });
    }

    // OTP Input Handling
    const otpInputs = document.querySelectorAll('.otp-input');

    if (otpInputs.length > 0) {
        otpInputs.forEach((input, index) => {
            // Auto focus next input when a digit is entered
            input.addEventListener('input', function() {
                if (this.value.length === 1) {
                    // Move to next input
                    if (index < otpInputs.length - 1) {
                        otpInputs[index + 1].focus();
                    }
                }
            });

            // Handle backspace
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Backspace' && this.value.length === 0) {
                    // Move to previous input
                    if (index > 0) {
                        otpInputs[index - 1].focus();
                    }
                }
            });

            // Handle paste
            input.addEventListener('paste', function(e) {
                e.preventDefault();

                // Get pasted data
                const pastedData = (e.clipboardData || window.clipboardData).getData('text');

                // Fill inputs with pasted data
                if (/^\d+$/.test(pastedData)) {
                    for (let i = 0; i < Math.min(pastedData.length, otpInputs.length); i++) {
                        otpInputs[i].value = pastedData[i];
                    }

                    // Focus last filled input
                    const lastIndex = Math.min(pastedData.length, otpInputs.length) - 1;
                    if (lastIndex >= 0) {
                        otpInputs[lastIndex].focus();
                    }
                }
            });
        });
    }

    // Send OTP Button
    const sendOtpBtn = document.getElementById('send-otp-btn');
    const otpTimer = document.getElementById('otp-timer');
    const timerCount = document.getElementById('timer-count');

    if (sendOtpBtn && otpTimer && timerCount) {
        sendOtpBtn.addEventListener('click', function() {
            // Get phone number
            const countryCode = document.getElementById('country-code').value;
            const phoneNumber = document.getElementById('phone-number').value;

            if (!phoneNumber) {
                showNotification('Vui lòng nhập số điện thoại', 'error');
                return;
            }

            // Show notification
            showNotification(`Đã gửi mã OTP đến ${countryCode} ${phoneNumber}`, 'success');

            // Disable button
            this.disabled = true;

            // Show timer
            otpTimer.style.display = 'block';

            // Start countdown
            let count = 60;
            timerCount.textContent = count;

            const countdown = setInterval(() => {
                count--;
                timerCount.textContent = count;

                if (count <= 0) {
                    clearInterval(countdown);
                    sendOtpBtn.disabled = false;
                    otpTimer.style.display = 'none';
                }
            }, 1000);
        });
    }

    // QR Code Timer
    const qrTimer = document.getElementById('qr-timer');

    if (qrTimer) {
        let minutes = 2;
        let seconds = 0;

        const updateQrTimer = () => {
            qrTimer.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        };

        updateQrTimer();

        const qrCountdown = setInterval(() => {
            if (seconds === 0) {
                if (minutes === 0) {
                    clearInterval(qrCountdown);
                    qrTimer.textContent = 'Hết hạn';
                    qrTimer.style.color = 'red';
                    return;
                }
                minutes--;
                seconds = 59;
            } else {
                seconds--;
            }

            updateQrTimer();
        }, 1000);
    }

    // Refresh QR Code Button
    const refreshQrBtn = document.getElementById('refresh-qr-btn');

    if (refreshQrBtn) {
        refreshQrBtn.addEventListener('click', function() {
            showNotification('Đã làm mới mã QR', 'success');

            // Reset timer (in a real app, you would also refresh the QR code image)
            if (qrTimer) {
                qrTimer.textContent = '2:00';
                qrTimer.style.color = '';
            }
        });
    }

    // Forgot Password Modal
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    const forgotPasswordModal = document.getElementById('forgot-password-modal');
    const modalClose = document.querySelector('.modal-close');
    const modalCancel = document.querySelector('.modal-cancel');

    if (forgotPasswordLink && forgotPasswordModal) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            forgotPasswordModal.classList.add('show');
        });

        if (modalClose) {
            modalClose.addEventListener('click', function() {
                forgotPasswordModal.classList.remove('show');
            });
        }

        if (modalCancel) {
            modalCancel.addEventListener('click', function() {
                forgotPasswordModal.classList.remove('show');
            });
        }
    }

    // Forgot Password Form
    const forgotPasswordForm = document.getElementById('forgot-password-form');

    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('recovery-email').value;

            if (!email) {
                showNotification('Vui lòng nhập email', 'error');
                return;
            }

            // Show notification
            showNotification(`Đã gửi hướng dẫn đặt lại mật khẩu đến ${email}`, 'success');

            // Close modal
            if (forgotPasswordModal) {
                forgotPasswordModal.classList.remove('show');
            }
        });
    }

    // Login Form
    const loginForm = document.getElementById('login-form');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            if (!email || !password) {
                showNotification('Vui lòng nhập đầy đủ thông tin', 'error');
                return;
            }

            // Simulate login (in a real app, you would send a request to the server)
            setTimeout(() => {
                // Create user object
                const user = {
                    email: email,
                    name: email.split('@')[0],
                    isLoggedIn: true,
                    loginTime: new Date().toISOString()
                };

                // Save to localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Show notification
                showNotification('Đăng nhập thành công!', 'success');

                // Redirect to home page
                setTimeout(() => {
                    // Lưu thông tin đăng nhập vào localStorage
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', user.name);

                    window.location.href = '../index.html'; // Đường dẫn chính xác đến trang chủ
                }, 1000);
            }, 1000);
        });
    }

    // Phone Login Form
    const phoneLoginForm = document.getElementById('phone-login-form');

    if (phoneLoginForm) {
        phoneLoginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const countryCode = document.getElementById('country-code').value;
            const phoneNumber = document.getElementById('phone-number').value;

            // Get OTP values
            let otp = '';
            otpInputs.forEach(input => {
                otp += input.value;
            });

            if (!phoneNumber || otp.length !== 6) {
                showNotification('Vui lòng nhập đầy đủ thông tin', 'error');
                return;
            }

            // Simulate login (in a real app, you would send a request to the server)
            setTimeout(() => {
                // Create user object
                const user = {
                    phone: `${countryCode} ${phoneNumber}`,
                    name: `Người dùng ${phoneNumber.substring(phoneNumber.length - 4)}`,
                    isLoggedIn: true,
                    loginTime: new Date().toISOString()
                };

                // Save to localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Show notification
                showNotification('Đăng nhập thành công!', 'success');

                // Redirect to home page
                setTimeout(() => {
                    // Lưu thông tin đăng nhập vào localStorage
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', user.name);

                    window.location.href = '../index.html'; // Đường dẫn chính xác đến trang chủ
                }, 1000);
            }, 1000);
        });
    }

    // Social Login Buttons
    const facebookLoginBtn = document.getElementById('facebook-login');
    const googleLoginBtn = document.getElementById('google-login');
    const appleLoginBtn = document.getElementById('apple-login');

    if (facebookLoginBtn) {
        facebookLoginBtn.addEventListener('click', function() {
            showNotification('Đang chuyển hướng đến đăng nhập Facebook...', 'info');
        });
    }

    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', function() {
            showNotification('Đang chuyển hướng đến đăng nhập Google...', 'info');
        });
    }

    if (appleLoginBtn) {
        appleLoginBtn.addEventListener('click', function() {
            showNotification('Đang chuyển hướng đến đăng nhập Apple...', 'info');
        });
    }
});

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
