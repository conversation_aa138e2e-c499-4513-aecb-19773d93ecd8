<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/sections.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/account.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-enhanced.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <link rel="stylesheet" type="text/css" href="../styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="../styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="../styles/neon-effects.css">

    <title>Đăng nhập | Fashion Store</title>

    <style>
        /* Đảm bảo màu sắc giống với index.html */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #f5f5f5;
            --accent-color: #e74c3c;
            --accent-hover: #c0392b;
            --text-color: #333;
            --light-text: #fff;
            --dark-text: #222;
            --border-color: #ddd;
            --hover-color: #f9f9f9;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --warning-color: #f39c12;
            --warning-bg: #fef5e7;
            --success-color: #27ae60;
            --success-bg: #e9f7ef;
            --error-color: #e74c3c;
            --error-bg: #fdedec;
        }

        /* Thông báo lỗi và hướng dẫn */
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-size: 14px;
            line-height: 1.5;
        }

        .alert-warning {
            background-color: var(--warning-bg);
            border-left: 4px solid var(--warning-color);
            color: #7d6608;
        }

        .alert-success {
            background-color: var(--success-bg);
            border-left: 4px solid var(--success-color);
            color: #196f3d;
        }

        .alert ol {
            margin: 10px 0 5px 20px;
            padding: 0;
        }

        .alert li {
            margin-bottom: 5px;
        }

        .alert code {
            background-color: rgba(0,0,0,0.05);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }

        .otp-hint {
            margin-top: 5px;
            color: var(--primary-color);
            background-color: rgba(44, 62, 80, 0.05);
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
        }

        .otp-hint strong {
            color: var(--accent-color);
            letter-spacing: 1px;
        }

        /* Navbar styles */
        .announcement-bar {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .main-nav {
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-item > a {
            color: var(--primary-color);
        }

        .nav-item > a:hover {
            color: var(--accent-color);
        }

        .nav-icon {
            color: var(--primary-color);
        }

        .nav-icon:hover {
            color: var(--accent-color);
        }

        /* Footer styles */
        .footer {
            background-color: var(--primary-color);
            color: var(--light-text);
        }

        .footer::before {
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
        }

        .footer-column h3 {
            color: var(--light-text);
        }

        .footer-column h3::after {
            background-color: var(--accent-color);
        }

        .footer-column ul li a {
            color: rgba(255, 255, 255, 0.7);
        }

        .footer-column ul li a::before {
            color: var(--accent-color);
        }

        .footer-column ul li a:hover {
            color: var(--light-text);
        }

        .social-icon {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--light-text);
        }

        .social-icon:hover {
            background-color: var(--accent-color);
            color: var(--light-text);
        }

        .footer-newsletter-form button {
            background-color: var(--accent-color);
        }

        .footer-newsletter-form button:hover {
            background-color: var(--accent-hover);
        }

        .contact-icon {
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
        </div>
    </div>

    <!-- Login Page Content -->
    <div class="auth-container">
        <div class="container">
            <div class="auth-wrapper">
                <div class="auth-form-container neon-form-container electric-border">
                    <div class="led-strip"></div>
                    <div class="led-strip led-bottom"></div>
                    <div class="wave-container"></div>
                    <div class="glowing-bg"></div>

                    <div class="auth-header">
                        <h1 class="neon-title">Đăng nhập</h1>
                        <p>Đăng nhập để truy cập tài khoản của bạn</p>
                    </div>

                    <!-- Login Methods Tabs -->
                    <div class="login-methods">
                        <div class="login-tabs">
                            <button class="login-tab active" data-tab="email">
                                <i class="fas fa-envelope"></i> Email
                            </button>
                            <button class="login-tab" data-tab="phone">
                                <i class="fas fa-phone"></i> Số điện thoại
                            </button>
                            <button class="login-tab" data-tab="qr">
                                <i class="fas fa-qrcode"></i> Mã QR
                            </button>
                        </div>

                        <div class="login-tab-content active" id="email-tab">
                            <form id="login-form" class="auth-form">
                                <div class="floating-label neon-input-container lightning-effect">
                                    <input type="email" id="email" name="email" placeholder=" " class="neon-input" required>
                                    <label for="email">Email <span class="required">*</span></label>
                                    <div class="saved-emails" id="saved-emails">
                                        <!-- Saved emails will be displayed here -->
                                    </div>
                                </div>

                                <div class="form-group neon-input-container lightning-effect">
                                    <div class="password-input">
                                        <input type="password" id="password" name="password" class="neon-input" required>
                                        <span class="toggle-password"><i class="far fa-eye"></i></span>
                                    </div>
                                    <label for="password">Mật khẩu <span class="required">*</span></label>
                                </div>

                                <div class="form-options">
                                    <div class="remember-me">
                                        <input type="checkbox" id="remember" name="remember">
                                        <label for="remember">Ghi nhớ đăng nhập</label>
                                    </div>
                                    <a href="#" class="forgot-password" id="forgot-password-link">Quên mật khẩu?</a>
                                </div>

                                <button type="submit" class="btn btn-primary btn-block neon-button">Đăng nhập</button>
                            </form>
                        </div>

                        <div class="login-tab-content" id="phone-tab">
                            <form id="phone-login-form" class="auth-form">
                                <div class="form-group">
                                    <label for="phone-number">Số điện thoại <span class="required">*</span></label>
                                    <div class="phone-input">
                                        <select id="country-code">
                                            <option value="+84">+84 (Việt Nam)</option>
                                            <option value="+1">+1 (Mỹ/Canada)</option>
                                            <option value="+65">+65 (Singapore)</option>
                                            <option value="+82">+82 (Hàn Quốc)</option>
                                            <option value="+81">+81 (Nhật Bản)</option>
                                        </select>
                                        <input type="tel" id="phone-number" name="phone-number" placeholder="Nhập số điện thoại" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="otp">Mã xác thực OTP <span class="required">*</span></label>
                                    <div class="otp-input-container">
                                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                                    </div>
                                    <button type="button" id="send-otp-btn" class="btn btn-outline">Gửi mã OTP</button>
                                    <div class="otp-timer" id="otp-timer">
                                        Gửi lại mã sau <span id="timer-count">60</span>s
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-block neon-button">Đăng nhập</button>
                            </form>
                        </div>

                        <div class="login-tab-content" id="qr-tab">
                            <div class="qr-login-container">
                                <div class="qr-code">
                                    <img src="../img/qr-code.png" alt="QR Code">
                                </div>
                                <div class="qr-instructions">
                                    <h3>Quét mã QR để đăng nhập</h3>
                                    <ol>
                                        <li>Mở ứng dụng <strong>Fashion Store</strong> trên điện thoại</li>
                                        <li>Chọn <strong>Quét mã QR</strong> trong menu</li>
                                        <li>Quét mã QR này để đăng nhập tự động</li>
                                    </ol>
                                    <p class="qr-note">Mã QR sẽ hết hạn sau <span id="qr-timer">2:00</span></p>
                                    <button type="button" id="refresh-qr-btn" class="btn btn-outline neon-button">
                                        <i class="fas fa-sync-alt"></i> Làm mới mã QR
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="social-login">
                        <p>Hoặc đăng nhập với</p>
                        <div class="social-buttons">
                            <button class="social-btn facebook neon-button" id="facebook-login"><i class="fab fa-facebook-f"></i> Facebook</button>
                            <button class="social-btn google neon-button" id="google-login"><i class="fab fa-google"></i> Google</button>
                            <button class="social-btn apple neon-button" id="apple-login"><i class="fab fa-apple"></i> Apple</button>
                        </div>
                    </div>

                    <div class="recent-activity">
                        <h4>Hoạt động gần đây</h4>
                        <div class="activity-list" id="activity-list">
                            <p class="no-activity">Đăng nhập để xem hoạt động gần đây của bạn</p>
                        </div>
                    </div>

                    <div class="auth-footer">
                        <p>Bạn chưa có tài khoản? <a href="register.html">Đăng ký ngay</a></p>
                    </div>
                </div>

                <div class="auth-image">
                    <img src="../img womens/women 11.webp" alt="Login Image">
                    <div class="auth-image-overlay">
                        <h2>Chào mừng trở lại</h2>
                        <p>Đăng nhập để trải nghiệm mua sắm tốt nhất và nhận nhiều ưu đãi hấp dẫn</p>
                        <div class="auth-benefits">
                            <div class="benefit-item">
                                <i class="fas fa-gift"></i>
                                <span>Ưu đãi độc quyền</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-truck"></i>
                                <span>Miễn phí vận chuyển</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-undo"></i>
                                <span>Đổi trả dễ dàng</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal" id="forgot-password-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Khôi phục mật khẩu</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Vui lòng nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu.</p>
                <form id="forgot-password-form">
                    <div class="form-group">
                        <label for="recovery-email">Email <span class="required">*</span></label>
                        <input type="email" id="recovery-email" name="recovery-email"  required>
                    </div>
                    <div class="form-actions">
                         <button type="submit" class="btn btn-primary">Gửi yêu cầu</button>
                         <!-- btn btn-primary -->
                        <button type="button" class="btn btn-outline modal-cancel">Hủy</button>
                        <!-- btn btn-outline modal-cancel -->
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Thông tin</h3>
                    <ul>
                        <li><a href="../about.html">Về chúng tôi</a></li>
                        <li><a href="../contact.html">Liên hệ</a></li>
                        <li><a href="../blog.html">Tin tức & Xu hướng</a></li>
                        <li><a href="../faq.html">Câu hỏi thường gặp</a></li>
                        <li><a href="../stores.html">Hệ thống cửa hàng</a></li>
                        <li><a href="../careers.html">Tuyển dụng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Dịch vụ khách hàng</h3>
                    <ul>
                        <li><a href="../account.html">Tài khoản của tôi</a></li>
                        <li><a href="../order-tracking.html">Theo dõi đơn hàng</a></li>
                        <li><a href="../shipping.html">Chính sách vận chuyển</a></li>
                        <li><a href="../returns.html">Chính sách đổi trả</a></li>
                        <li><a href="../size-guide.html">Hướng dẫn chọn size</a></li>
                        <li><a href="../wishlist.html">Danh sách yêu thích</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Liên hệ với chúng tôi</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                123 Đường Nguyễn Trãi, Quận 1, TP. Hồ Chí Minh
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-text">
                                +84 (0) 9034709345
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <EMAIL>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                Thứ Hai - Chủ Nhật: 9:00 - 21:00
                            </div>
                        </div>
                    </div>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <div class="footer-newsletter">
                        <h4>Đăng ký nhận tin</h4>
                        <p>Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
                        <form class="footer-newsletter-form">
                            <input type="email" placeholder="Email của bạn" required>
                            <button type="submit"><i class="fas fa-paper-plane"></i></button>
                        </form>
                    </div>
                    <div class="app-download">
                        <h4>Tải ứng dụng</h4>
                        <div class="app-buttons">
                            <a href="#"><img src="../img logo/images.png" alt="App Store"></a>
                            <a href="#"><img src="../img logo/img 2.png" alt="Google Play"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Fashion Store. Tất cả các quyền được bảo lưu. | Thiết kế bởi <a href="#">Kelsey Tsai</a></p>
            </div>
        </div>
    <div id="footerbox"></div>

    <iframe
        id="youtube-player"
        width="0" height="0"
        src="https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=1"
        frameborder="0"
        allow="autoplay; encrypted-media">
    </iframe>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/auth-enhanced.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/chatbot.js"></script>
    <script src="../script/neon-effects.js"></script>
    <script src="../script/api-service.js"></script>
    <script src="../script/user-display.js"></script>
    <script src="../script/marquee.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>


    <script>
        // Đảm bảo đăng nhập hoạt động đúng
        document.addEventListener('DOMContentLoaded', function() {




// Xử lý gửi mã OTP qua số điện thoại
const sendOtpBtn = document.getElementById("send-otp-btn");
if (sendOtpBtn) {
    sendOtpBtn.addEventListener("click", async function () {
        const phone = document.getElementById("phone-number").value.trim();
        const code = document.getElementById("country-code").value;

        if (!phone) {
            showNotification("Vui lòng nhập số điện thoại", "error");
            return;
        }

        // Kiểm tra định dạng số điện thoại
        if (!/^\d{9,12}$/.test(phone)) {
            showNotification("Số điện thoại không hợp lệ. Vui lòng nhập 9-12 chữ số", "error");
            return;
        }

        const fullPhone = code + phone;

        // Vô hiệu hóa nút gửi OTP để tránh spam
        sendOtpBtn.disabled = true;
        sendOtpBtn.textContent = "Đang gửi...";

        try {
            showNotification("Đang gửi mã OTP...", "info");

            const response = await fetch("http://localhost:3000/api/send-otp", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ phone: fullPhone })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                showNotification("✅ Mã OTP đã được gửi đến điện thoại của bạn!", "success");
                startOTPTimer();

                // Nếu đang ở môi trường phát triển và có mã OTP giả lập
                if (result.simulatedOtp) {
                    console.log("Mã OTP giả lập:", result.simulatedOtp);

                    // Hiển thị thông báo nhỏ về mã OTP giả lập
                    const otpHint = document.createElement('div');
                    otpHint.className = 'otp-hint';
                    otpHint.innerHTML = `<small>Mã OTP giả lập: <strong>${result.simulatedOtp}</strong> (chỉ hiển thị trong môi trường phát triển)</small>`;

                    // Thêm thông báo vào form
                    const otpForm = document.querySelector('.otp-input-container');
                    if (otpForm && !document.querySelector('.otp-hint')) {
                        otpForm.parentNode.insertBefore(otpHint, otpForm.nextSibling);
                    }
                }

                // Focus vào ô input OTP đầu tiên
                const firstOtpInput = document.querySelector('.otp-input');
                if (firstOtpInput) {
                    firstOtpInput.focus();
                }
            } else {
                throw new Error(result.message || "Không gửi được OTP");
            }
        } catch (err) {
            console.error("Lỗi khi gửi OTP:", err);

            // Kiểm tra lỗi kết nối
            if (err.name === 'TypeError' && err.message.includes('Failed to fetch')) {
                showNotification(`❌ Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng và đảm bảo server đang chạy.`, "error", 10000);

                // Hiển thị hướng dẫn khởi động server
                const serverHint = document.createElement('div');
                serverHint.className = 'server-hint';
                serverHint.innerHTML = `
                    <div class="alert alert-warning">
                        <strong>Hướng dẫn:</strong>
                        <ol>
                            <li>Mở file <code>start-server.bat</code> để khởi động server</li>
                            <li>Đợi server khởi động hoàn tất (thông báo "Server đang chạy tại http://localhost:3000")</li>
                            <li>Thử lại việc gửi mã OTP</li>
                        </ol>
                    </div>
                `;

                // Thêm hướng dẫn vào form
                const phoneForm = document.getElementById('phone-login-form');
                if (phoneForm && !document.querySelector('.server-hint')) {
                    phoneForm.insertBefore(serverHint, phoneForm.querySelector('button[type="submit"]'));
                }
            } else {
                showNotification(`❌ ${err.message || "Đã xảy ra lỗi khi gửi mã OTP"}`, "error");
            }
        } finally {
            // Khôi phục nút gửi OTP
            sendOtpBtn.textContent = "Gửi mã OTP";
            // Không cần bật lại nút vì startOTPTimer sẽ quản lý việc này
        }
    });
}

// Xử lý xác thực OTP
const phoneLoginForm = document.getElementById("phone-login-form");
if (phoneLoginForm) {
    phoneLoginForm.addEventListener("submit", async function(e) {
        e.preventDefault();

        const phone = document.getElementById("phone-number").value.trim();
        const code = document.getElementById("country-code").value;
        const fullPhone = code + phone;

        // Lấy mã OTP từ các ô input
        const otpInputs = document.querySelectorAll('.otp-input');
        let otp = '';

        otpInputs.forEach(input => {
            otp += input.value;
        });

        if (!phone) {
            showNotification("Vui lòng nhập số điện thoại", "error");
            return;
        }

        if (otp.length !== 6) {
            showNotification("Vui lòng nhập đủ 6 chữ số OTP", "error");
            return;
        }

        // Vô hiệu hóa nút đăng nhập để tránh gửi nhiều lần
        const submitButton = phoneLoginForm.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xác thực...';
        }

        try {
            showNotification("Đang xác thực OTP...", "info");

            const response = await fetch("http://localhost:3000/api/verify-otp", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    phone: fullPhone,
                    otp: otp
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                showNotification("✅ Xác thực OTP thành công!", "success");

                // Hiệu ứng thành công
                otpInputs.forEach(input => {
                    input.style.borderColor = 'var(--success-color)';
                    input.style.backgroundColor = 'var(--success-bg)';
                });

                // Lưu thông tin người dùng vào localStorage
                localStorage.setItem('user', JSON.stringify({
                    ...result.data,
                    token: result.token,
                    isLoggedIn: true
                }));
                localStorage.setItem('isLoggedIn', 'true');

                // Hiển thị thông báo chuyển hướng
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success';
                successMessage.innerHTML = `
                    <i class="fas fa-check-circle"></i> Đăng nhập thành công! Đang chuyển hướng...
                `;

                // Thêm thông báo vào form
                if (!document.querySelector('.alert-success')) {
                    phoneLoginForm.appendChild(successMessage);
                }

                // Chuyển hướng về trang chủ hoặc trang trước đó
                const redirectUrl = localStorage.getItem('redirectAfterLogin') || '../index.html';
                localStorage.removeItem('redirectAfterLogin'); // Xóa URL chuyển hướng

                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1500);
            } else {
                // Hiệu ứng lỗi
                otpInputs.forEach(input => {
                    input.style.borderColor = 'var(--error-color)';
                    input.style.backgroundColor = 'var(--error-bg)';
                    input.value = '';
                });

                // Focus vào ô đầu tiên
                otpInputs[0].focus();

                throw new Error(result.message || "Mã OTP không chính xác hoặc đã hết hạn");
            }
        } catch (err) {
            console.error("Lỗi khi xác thực OTP:", err);

            // Kiểm tra lỗi kết nối
            if (err.name === 'TypeError' && err.message.includes('Failed to fetch')) {
                showNotification(`❌ Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng và đảm bảo server đang chạy.`, "error", 10000);
            } else {
                showNotification(`❌ ${err.message || "Đã xảy ra lỗi khi xác thực OTP"}`, "error");
            }
        } finally {
            // Khôi phục nút đăng nhập
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.innerHTML = 'Đăng nhập';
            }
        }
    });
}

// Xử lý chuyển tiếp giữa các ô input OTP
const otpInputs = document.querySelectorAll('.otp-input');
otpInputs.forEach((input, index) => {
    input.addEventListener('keyup', (e) => {
        // Nếu nhập một chữ số, chuyển sang ô tiếp theo
        if (e.key >= '0' && e.key <= '9') {
            input.value = e.key;
            if (index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }
        }
        // Nếu nhấn Backspace, xóa giá trị hiện tại và chuyển về ô trước đó
        else if (e.key === 'Backspace') {
            input.value = '';
            if (index > 0) {
                otpInputs[index - 1].focus();
            }
        }
    });

    // Xử lý khi paste OTP
    input.addEventListener('paste', (e) => {
        e.preventDefault();
        const pasteData = e.clipboardData.getData('text');
        const digits = pasteData.match(/\d/g);

        if (digits && digits.length > 0) {
            otpInputs.forEach((input, i) => {
                if (i < digits.length) {
                    input.value = digits[i];
                }
            });

            // Focus vào ô cuối cùng hoặc ô tiếp theo sau khi paste
            if (digits.length < otpInputs.length) {
                otpInputs[digits.length].focus();
            } else {
                otpInputs[otpInputs.length - 1].focus();
            }
        }
    });
});

// Hàm đếm ngược thời gian gửi lại OTP
function startOTPTimer() {
    let count = 60;
    const timerEl = document.getElementById("timer-count");
    const otpTimer = document.getElementById("otp-timer");
    const sendOtpBtn = document.getElementById("send-otp-btn");

    otpTimer.style.display = "block";
    timerEl.textContent = count;
    sendOtpBtn.disabled = true;

    const interval = setInterval(() => {
        count--;
        timerEl.textContent = count;

        if (count <= 0) {
            clearInterval(interval);
            otpTimer.textContent = "Bạn có thể gửi lại mã.";
            sendOtpBtn.disabled = false;
        }
    }, 1000);
}

            // Xóa các event listener cũ để tránh xung đột
            const loginForm = document.getElementById('login-form');
            const oldLoginForm = loginForm.cloneNode(true);
            loginForm.parentNode.replaceChild(oldLoginForm, loginForm);

            // Thêm event listener mới
            oldLoginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const remember = document.getElementById('remember').checked;

                if (!email || !password) {
                    showNotification('Vui lòng nhập đầy đủ thông tin', 'error');
                    return;
                }

                // Hiển thị thông báo đang đăng nhập
                showNotification('Đang đăng nhập...', 'info');

                try {
                    // Gọi API đăng nhập
                    const credentials = {
                        username: email.split('@')[0], // Sử dụng phần trước @ làm username
                        password: password
                    };

                    // Gọi API đăng nhập
                    const response = await ApiService.users.login(credentials);

                    // Hiển thị thông báo thành công
                    showNotification('Đăng nhập thành công!', 'success');

                    // Chuyển hướng về trang chủ hoặc trang trước đó
                    const redirectUrl = localStorage.getItem('redirectAfterLogin') || '../index.html';
                    localStorage.removeItem('redirectAfterLogin'); // Xóa URL chuyển hướng

                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                } catch (error) {
                    // Hiển thị thông báo lỗi
                    showNotification(error || 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin!', 'error');

                    // Giả lập đăng nhập thành công (chỉ để demo)
                    setTimeout(() => {
                        // Lưu thông tin người dùng vào localStorage
                        const username = email.split('@')[0];

                        localStorage.setItem('isLoggedIn', 'true');
                        localStorage.setItem('username', username);
                        localStorage.setItem('user', JSON.stringify({
                            email: email,
                            username: username,
                            fullName: username,
                            isLoggedIn: true,
                            loginTime: new Date().toISOString()
                        }));

                        // Hiển thị thông báo thành công
                        showNotification('Đăng nhập thành công (demo)!', 'success');

                        // Chuyển hướng về trang chủ
                        setTimeout(() => {
                            window.location.href = '../index.html';
                        }, 1000);
                    }, 1500);
                }
            });

            // Xử lý đăng nhập bằng Facebook
            const facebookLoginBtn = document.getElementById('facebook-login');
            if (facebookLoginBtn) {
                facebookLoginBtn.addEventListener('click', function() {
                    socialLogin('Facebook');
                });
            }

            // Xử lý đăng nhập bằng Google
            const googleLoginBtn = document.getElementById('google-login');
            if (googleLoginBtn) {
                googleLoginBtn.addEventListener('click', function() {
                    socialLogin('Google');
                });
            }

            // Xử lý đăng nhập bằng Apple
            const appleLoginBtn = document.getElementById('apple-login');
            if (appleLoginBtn) {
                appleLoginBtn.addEventListener('click', function() {
                    socialLogin('Apple');
                });
            }

            // Hàm xử lý đăng nhập bằng mạng xã hội
            function socialLogin(provider) {
                showNotification(`Đang đăng nhập bằng ${provider}...`, 'info');

                setTimeout(() => {
                    const username = `User${provider}`;

                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', username);
                    localStorage.setItem('user', JSON.stringify({
                        provider: provider,
                        username: username,
                        fullName: `${provider} User`,
                        isLoggedIn: true,
                        loginTime: new Date().toISOString()
                    }));

                    showNotification(`Đăng nhập thành công bằng ${provider}!`, 'success');

                    // Chuyển hướng về trang chủ hoặc trang trước đó
                    const redirectUrl = localStorage.getItem('redirectAfterLogin') || '../index.html';
                    localStorage.removeItem('redirectAfterLogin'); // Xóa URL chuyển hướng

                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                }, 1500);
            }
        });
    // </script>




</body>
</html>
