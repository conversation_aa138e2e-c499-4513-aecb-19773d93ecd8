/* Size Guide Styles */

.size-guide-container {
    padding: 60px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 50px;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #343a40;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.page-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #ff6b6b;
}

.page-header p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

.size-guide-content {
    max-width: 1000px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 40px;
}

/* Size Guide Navigation */
.size-guide-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 20px;
}

.size-nav-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    font-size: 1rem;
    color: #6c757d;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.size-nav-btn::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: transparent;
    transition: all 0.3s ease;
}

.size-nav-btn.active {
    color: #ff6b6b;
    font-weight: 500;
}

.size-nav-btn.active::after {
    background-color: #ff6b6b;
}

.size-nav-btn:hover {
    color: #ff6b6b;
}

/* How to Measure */
.how-to-measure {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.how-to-measure h2 {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 20px;
    text-align: center;
}

.how-to-measure p {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 20px;
    text-align: center;
}

.measure-guide {
    display: flex;
    gap: 30px;
    align-items: center;
}

.measure-image {
    flex: 1;
    max-width: 300px;
}

.measure-image img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.measure-steps {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.measure-step {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.step-number {
    width: 30px;
    height: 30px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.1rem;
    color: #343a40;
    margin-bottom: 5px;
}

.step-content p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    text-align: left;
}

/* Size Tables */
.size-category {
    display: none;
}

.size-category.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.size-category h2 {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 20px;
    text-align: center;
}

.size-table-section {
    margin-bottom: 30px;
}

.size-table-section h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
    padding-left: 15px;
    border-left: 3px solid #ff6b6b;
}

.size-table-wrapper {
    overflow-x: auto;
}

.size-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 700px;
}

.size-table th, .size-table td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.size-table th {
    background-color: #f8f9fa;
    color: #343a40;
    font-weight: 600;
    white-space: nowrap;
}

.size-table tr:last-child td {
    border-bottom: none;
}

.size-table tr:hover td {
    background-color: #f8f9fa;
}

.size-table td:first-child {
    font-weight: 600;
    color: #343a40;
}

/* Size Guide Tips */
.size-guide-tips {
    margin-top: 40px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ff6b6b;
}

.size-guide-tips h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
}

.size-guide-tips ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.size-guide-tips li {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 10px;
    line-height: 1.5;
}

.size-guide-tips p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.size-guide-tips a {
    color: #ff6b6b;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.size-guide-tips a:hover {
    text-decoration: underline;
}

/* Responsive */
@media (max-width: 992px) {
    .measure-guide {
        flex-direction: column;
    }
    
    .measure-image {
        max-width: 250px;
        margin: 0 auto 20px;
    }
}

@media (max-width: 768px) {
    .size-guide-content {
        padding: 30px 20px;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .size-nav-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .size-nav-btn {
        padding: 8px 10px;
        font-size: 0.8rem;
    }
    
    .measure-step {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .step-number {
        margin-bottom: 5px;
    }
}
