const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const products = require('../data/products');

// Secret key for JWT
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify token
const verifyToken = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      status: 'fail',
      message: 'No token provided'
    });
  }
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.userId = decoded.id;
    next();
  } catch (error) {
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid token'
    });
  }
};

// In-memory storage for wishlists (in a real app, this would be a database)
const wishlists = {};

// Get wishlist
router.get('/', verifyToken, (req, res) => {
  const userId = req.userId;
  const wishlist = wishlists[userId] || [];
  
  res.json({
    status: 'success',
    results: wishlist.length,
    data: wishlist
  });
});

// Add item to wishlist
router.post('/add', verifyToken, (req, res) => {
  const userId = req.userId;
  const { productId } = req.body;
  
  // Find product
  const product = products.find(p => p.id === productId);
  
  if (!product) {
    return res.status(404).json({
      status: 'fail',
      message: 'Product not found'
    });
  }
  
  // Initialize wishlist if it doesn't exist
  if (!wishlists[userId]) {
    wishlists[userId] = [];
  }
  
  // Check if product already exists in wishlist
  const existingItemIndex = wishlists[userId].findIndex(item => 
    item.product.id === productId
  );
  
  if (existingItemIndex !== -1) {
    return res.status(400).json({
      status: 'fail',
      message: 'Product already in wishlist'
    });
  }
  
  // Add new item
  wishlists[userId].push({
    id: Date.now().toString(),
    product: {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image
    },
    addedAt: new Date().toISOString()
  });
  
  res.json({
    status: 'success',
    results: wishlists[userId].length,
    data: wishlists[userId]
  });
});

// Toggle item in wishlist (add if not exists, remove if exists)
router.post('/toggle', verifyToken, (req, res) => {
  const userId = req.userId;
  const { productId } = req.body;
  
  // Find product
  const product = products.find(p => p.id === productId);
  
  if (!product) {
    return res.status(404).json({
      status: 'fail',
      message: 'Product not found'
    });
  }
  
  // Initialize wishlist if it doesn't exist
  if (!wishlists[userId]) {
    wishlists[userId] = [];
  }
  
  // Check if product already exists in wishlist
  const existingItemIndex = wishlists[userId].findIndex(item => 
    item.product.id === productId
  );
  
  let action = '';
  
  if (existingItemIndex !== -1) {
    // Remove item
    wishlists[userId] = wishlists[userId].filter(item => item.product.id !== productId);
    action = 'removed';
  } else {
    // Add new item
    wishlists[userId].push({
      id: Date.now().toString(),
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image
      },
      addedAt: new Date().toISOString()
    });
    action = 'added';
  }
  
  res.json({
    status: 'success',
    action,
    results: wishlists[userId].length,
    data: wishlists[userId]
  });
});

// Remove item from wishlist
router.delete('/remove/:itemId', verifyToken, (req, res) => {
  const userId = req.userId;
  const { itemId } = req.params;
  
  // Check if wishlist exists
  if (!wishlists[userId]) {
    return res.status(404).json({
      status: 'fail',
      message: 'Wishlist not found'
    });
  }
  
  // Remove item
  wishlists[userId] = wishlists[userId].filter(item => item.id !== itemId);
  
  res.json({
    status: 'success',
    results: wishlists[userId].length,
    data: wishlists[userId]
  });
});

// Clear wishlist
router.delete('/clear', verifyToken, (req, res) => {
  const userId = req.userId;
  
  // Clear wishlist
  wishlists[userId] = [];
  
  res.json({
    status: 'success',
    message: 'Wishlist cleared',
    data: []
  });
});

module.exports = router;
