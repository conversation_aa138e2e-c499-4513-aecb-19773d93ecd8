<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thẻ Thành Viên | Fashion Store</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="stylesheet" href="./styles/header.css">
    <link rel="stylesheet" href="./styles/footer.css">
    <link rel="stylesheet" href="./styles/membership.css">
    <link rel="stylesheet" href="./styles/marquee.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Styles cho page header */
        .page-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('./img/pattern-bg.png');
            opacity: 0.1;
            z-index: 0;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .page-header .breadcrumb {
            background: transparent;
            margin-bottom: 0;
            padding: 0;
            position: relative;
            z-index: 1;
            justify-content: center;
        }

        .page-header .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s;
        }

        .page-header .breadcrumb-item a:hover {
            color: white;
        }

        .page-header .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.6);
        }

        .page-header .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.5);
        }

        /* Styles cho trang thẻ thành viên */
        .membership-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .membership-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .membership-header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .membership-header p {
            font-size: 1.1rem;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
        }

        .membership-card {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
            border-radius: 15px;
            padding: 30px;
            color: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .membership-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('./img/card-pattern.png');
            opacity: 0.1;
            z-index: 0;
        }

        .card-content {
            position: relative;
            z-index: 1;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
        }

        .card-logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .card-level {
            font-size: 1.2rem;
            padding: 5px 15px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }

        .card-details {
            margin-bottom: 20px;
        }

        .card-name {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .card-number {
            font-size: 1.2rem;
            letter-spacing: 2px;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-points {
            font-size: 1.2rem;
        }

        .card-since {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .membership-levels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }

        .level-card {
            flex: 1;
            min-width: 200px;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 0 10px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .level-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .level-card.active {
            border: 2px solid #b21f1f;
        }

        .level-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .level-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #b21f1f;
        }

        .level-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .level-points {
            font-size: 0.9rem;
            color: #666;
        }

        .level-benefits {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .level-benefits li {
            padding: 5px 0;
            font-size: 0.9rem;
            color: #333;
        }

        .level-benefits li i {
            color: #4CAF50;
            margin-right: 5px;
        }

        .membership-info {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 50px;
        }

        .info-card {
            flex: 1;
            min-width: 300px;
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            margin: 0 10px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .info-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .info-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: #b21f1f;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(178, 31, 31, 0.1);
            border-radius: 50%;
        }

        .info-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin: 0;
        }

        .info-content {
            color: #666;
        }

        .info-content ul {
            padding-left: 20px;
        }

        .info-content ul li {
            margin-bottom: 10px;
        }

        .redeem-section {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 50px;
        }

        .redeem-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .redeem-header h2 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            color: #333;
        }

        .redeem-header p {
            color: #666;
        }

        .redeem-options {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }

        .redeem-option {
            width: 200px;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .redeem-option:hover {
            transform: translateY(-5px);
        }

        .redeem-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #b21f1f;
        }

        .redeem-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .redeem-points {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
        }

        .redeem-btn {
            background-color: #b21f1f;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .redeem-btn:hover {
            background-color: #8c1919;
        }

        .redeem-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .membership-levels {
                flex-direction: column;
            }

            .level-card {
                margin: 0 0 20px;
            }

            .membership-info {
                flex-direction: column;
            }

            .info-card {
                margin: 0 0 20px;
            }
        }

        /* Styles cho các cấp độ thẻ */
        .standard-card {
            background: linear-gradient(135deg, #757F9A, #D7DDE8);
        }

        .silver-card {
            background: linear-gradient(135deg, #BDC3C7, #2C3E50);
        }

        .gold-card {
            background: linear-gradient(135deg, #FDEB71, #F8D800);
            color: #333;
        }

        .platinum-card {
            background: linear-gradient(135deg, #C9D6FF, #E2E2E2);
            color: #333;
        }

        .diamond-card {
            background: linear-gradient(135deg, #43CEA2, #185A9D);
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
        </div>
    </div>

    <section class="page-header">
        <div class="container">
            <h1>Thẻ Thành Viên</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a href="pages/account.html">Tài khoản của tôi</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Thẻ thành viên</li>
                </ol>
            </nav>
        </div>
    </section>

    <div class="membership-container">
        <div class="membership-header" data-aos="fade-up">
            <h1>Thẻ Thành Viên</h1>
            <p>Tận hưởng các đặc quyền độc quyền và ưu đãi hấp dẫn với thẻ thành viên của Fashion Store. Tích điểm với mỗi đơn hàng và nâng cấp thẻ thành viên của bạn để nhận thêm nhiều quyền lợi.</p>
        </div>

        <div class="membership-card gold-card" data-aos="fade-up">
            <div class="card-content">
                <div class="card-header">
                    <div class="card-logo">FASHION STORE</div>
                    <div class="card-level" id="member-level">Thành viên Vàng</div>
                </div>
                <div class="card-details">
                    <div class="card-name" id="member-name">Nguyễn Văn A</div>
                    <div class="card-number" id="member-id">ID: FS-2023-12345</div>
                </div>
                <div class="card-footer">
                    <div class="card-points"><span>Điểm</span>: <span id="member-points">1,250</span></div>
                    <div class="card-since"><span>Thành viên từ</span>: <span id="member-since">01/01/2023</span></div>
                </div>
            </div>
        </div>

        <h2 class="section-title" data-aos="fade-up">Cấp độ thành viên</h2>
        <div class="membership-levels">
            <div class="level-card" data-aos="fade-up" data-aos-delay="100">
                <div class="level-header">
                    <div class="level-icon"><i class="fas fa-user"></i></div>
                    <div class="level-name" data-lang="standard_member">Thành viên Tiêu chuẩn</div>
                    <div class="level-points">0 - 999 <span data-lang="points">điểm</span></div>
                </div>
                <ul class="level-benefits">
                    <li><i class="fas fa-check"></i> <span data-lang="birthday_gift">Quà tặng sinh nhật</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="exclusive_offers">Ưu đãi độc quyền</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="earn_points">Tích điểm mỗi đơn hàng</span></li>
                </ul>
            </div>

            <div class="level-card" data-aos="fade-up" data-aos-delay="200">
                <div class="level-header">
                    <div class="level-icon"><i class="fas fa-award"></i></div>
                    <div class="level-name" data-lang="silver_member">Thành viên Bạc</div>
                    <div class="level-points">1,000 - 2,999 <span data-lang="points">điểm</span></div>
                </div>
                <ul class="level-benefits">
                    <li><i class="fas fa-check"></i> <span data-lang="all_standard_benefits">Tất cả quyền lợi Tiêu chuẩn</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="free_shipping">Miễn phí vận chuyển</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="priority_support">Hỗ trợ ưu tiên</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="early_access">Tiếp cận sớm BST mới</span></li>
                </ul>
            </div>

            <div class="level-card active" data-aos="fade-up" data-aos-delay="300">
                <div class="level-header">
                    <div class="level-icon"><i class="fas fa-crown"></i></div>
                    <div class="level-name" data-lang="gold_member">Thành viên Vàng</div>
                    <div class="level-points">3,000 - 4,999 <span data-lang="points">điểm</span></div>
                </div>
                <ul class="level-benefits">
                    <li><i class="fas fa-check"></i> <span data-lang="all_silver_benefits">Tất cả quyền lợi Bạc</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="exclusive_events">Sự kiện độc quyền</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="personal_shopper">Dịch vụ mua sắm cá nhân</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="birthday_discount">Giảm 15% ngày sinh nhật</span></li>
                </ul>
            </div>

            <div class="level-card" data-aos="fade-up" data-aos-delay="400">
                <div class="level-header">
                    <div class="level-icon"><i class="fas fa-gem"></i></div>
                    <div class="level-name" data-lang="platinum_member">Thành viên Bạch Kim</div>
                    <div class="level-points">5,000 - 9,999 <span data-lang="points">điểm</span></div>
                </div>
                <ul class="level-benefits">
                    <li><i class="fas fa-check"></i> <span data-lang="all_gold_benefits">Tất cả quyền lợi Vàng</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="vip_customer_service">Dịch vụ khách hàng VIP</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="free_alterations">Miễn phí sửa đồ</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="extended_returns">Đổi trả kéo dài 60 ngày</span></li>
                </ul>
            </div>

            <div class="level-card" data-aos="fade-up" data-aos-delay="500">
                <div class="level-header">
                    <div class="level-icon"><i class="fas fa-star"></i></div>
                    <div class="level-name" data-lang="diamond_member">Thành viên Kim Cương</div>
                    <div class="level-points">10,000+ <span data-lang="points">điểm</span></div>
                </div>
                <ul class="level-benefits">
                    <li><i class="fas fa-check"></i> <span data-lang="all_platinum_benefits">Tất cả quyền lợi Bạch Kim</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="private_shopping">Mua sắm riêng tư</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="unlimited_free_shipping">Miễn phí vận chuyển không giới hạn</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="birthday_gift_card">Thẻ quà tặng sinh nhật</span></li>
                    <li><i class="fas fa-check"></i> <span data-lang="exclusive_collaborations">Bộ sưu tập hợp tác độc quyền</span></li>
                </ul>
            </div>
        </div>

        <div class="membership-info">
            <div class="info-card" data-aos="fade-up">
                <div class="info-header">
                    <div class="info-icon"><i class="fas fa-coins"></i></div>
                    <h3 class="info-title" data-lang="how_to_earn">Cách tích điểm</h3>
                </div>
                <div class="info-content">
                    <ul>
                        <li><span data-lang="earn_per_purchase">Tích điểm với mỗi đơn hàng</span>: 1,000đ = 1 <span data-lang="point">điểm</span></li>
                        <li><span data-lang="earn_on_birthday">Tích điểm đôi vào ngày sinh nhật</span></li>
                        <li><span data-lang="earn_for_reviews">Tích điểm khi đánh giá sản phẩm</span>: 10 <span data-lang="points">điểm</span>/đánh giá</li>
                        <li><span data-lang="earn_for_referrals">Tích điểm khi giới thiệu bạn bè</span>: 100 <span data-lang="points">điểm</span>/người</li>
                        <li><span data-lang="earn_for_social_shares">Tích điểm khi chia sẻ trên mạng xã hội</span>: 5 <span data-lang="points">điểm</span>/chia sẻ</li>
                    </ul>
                </div>
            </div>

            <div class="info-card" data-aos="fade-up" data-aos-delay="100">
                <div class="info-header">
                    <div class="info-icon"><i class="fas fa-gift"></i></div>
                    <h3 class="info-title" data-lang="redeem_points">Đổi điểm</h3>
                </div>
                <div class="info-content">
                    <ul>
                        <li><span data-lang="redeem_for_discounts">Đổi điểm lấy mã giảm giá</span>: 100 <span data-lang="points">điểm</span> = 10% <span data-lang="discount">giảm giá</span></li>
                        <li><span data-lang="redeem_for_free_shipping">Đổi điểm lấy miễn phí vận chuyển</span>: 50 <span data-lang="points">điểm</span></li>
                        <li><span data-lang="redeem_for_gift_cards">Đổi điểm lấy thẻ quà tặng</span>: 500 <span data-lang="points">điểm</span> = 100,000đ</li>
                        <li><span data-lang="redeem_for_exclusive_items">Đổi điểm lấy sản phẩm độc quyền</span></li>
                        <li><span data-lang="redeem_for_experiences">Đổi điểm lấy trải nghiệm</span> (workshop, sự kiện)</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="redeem-section" data-aos="fade-up">
            <div class="redeem-header">
                <h2 data-lang="redeem_your_points">Đổi điểm của bạn</h2>
                <p><span data-lang="available_points">Điểm hiện có</span>: <span id="available-points">1,250</span></p>
            </div>
            <div class="redeem-options">
                <div class="redeem-option" data-aos="fade-up" data-aos-delay="100">
                    <div class="redeem-icon"><i class="fas fa-tag"></i></div>
                    <div class="redeem-name" data-lang="discount_10">Giảm giá 10%</div>
                    <div class="redeem-points">100 <span data-lang="points">điểm</span></div>
                    <button class="redeem-btn" data-lang="redeem">Đổi điểm</button>
                </div>

                <div class="redeem-option" data-aos="fade-up" data-aos-delay="200">
                    <div class="redeem-icon"><i class="fas fa-truck"></i></div>
                    <div class="redeem-name" data-lang="free_shipping">Miễn phí vận chuyển</div>
                    <div class="redeem-points">50 <span data-lang="points">điểm</span></div>
                    <button class="redeem-btn" data-lang="redeem">Đổi điểm</button>
                </div>

                <div class="redeem-option" data-aos="fade-up" data-aos-delay="300">
                    <div class="redeem-icon"><i class="fas fa-gift"></i></div>
                    <div class="redeem-name" data-lang="gift_card">Thẻ quà tặng 100K</div>
                    <div class="redeem-points">500 <span data-lang="points">điểm</span></div>
                    <button class="redeem-btn" data-lang="redeem">Đổi điểm</button>
                </div>

                <div class="redeem-option" data-aos="fade-up" data-aos-delay="400">
                    <div class="redeem-icon"><i class="fas fa-tshirt"></i></div>
                    <div class="redeem-name" data-lang="exclusive_item">Sản phẩm độc quyền</div>
                    <div class="redeem-points">1,000 <span data-lang="points">điểm</span></div>
                    <button class="redeem-btn" data-lang="redeem">Đổi điểm</button>
                </div>
            </div>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/membership.js"></script>
    <script src="./script/marquee.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>
</body>
</html>
