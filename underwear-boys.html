<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/product.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="./styles/underwear.css">
    <link rel="stylesheet" type="text/css" href="./styles/underwear-nav.css">

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />

    <title>Nội y bé trai | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="product-hero" style="background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('./img/underwear-boys-hero.jpg');">
        <div class="product-hero-content">
            <h1 data-aos="fade-up">Nội y bé trai</h1>
            <p data-aos="fade-up" data-aos-delay="200">Thoải mái, năng động và an toàn cho bé</p>
        </div>
    </section>

    <!-- Underwear Navigation -->
    <nav class="underwear-nav">
        <div class="container">
            <div class="underwear-nav-container">
                <a href="./underwear-women.html" class="underwear-nav-item">Nội y nữ</a>
                <a href="./underwear-men.html" class="underwear-nav-item">Nội y nam</a>
                <a href="./underwear-girls.html" class="underwear-nav-item">Nội y bé gái</a>
                <a href="./underwear-boys.html" class="underwear-nav-item active">Nội y bé trai</a>
                <a href="./size-guide.html" class="underwear-nav-item">Hướng dẫn chọn size</a>
            </div>
        </div>
    </nav>

    <!-- Product Filters -->
    <section class="product-filters">
        <div class="container">
            <div class="filter-wrapper">
                <div class="filter-group">
                    <button class="filter-btn active" data-filter="all">Tất cả</button>
                    <button class="filter-btn" data-filter="underwear">Quần lót</button>
                    <button class="filter-btn" data-filter="undershirt">Áo lót</button>
                    <button class="filter-btn" data-filter="set">Bộ đồ lót</button>
                    <button class="filter-btn" data-filter="sleepwear">Đồ ngủ</button>
                    <button class="filter-btn" data-filter="socks">Tất & Vớ</button>
                </div>
                <div class="filter-group">
                    <div class="sort-by">
                        <label for="sort-select">Sắp xếp:</label>
                        <select id="sort-select">
                            <option value="popular">Phổ biến nhất</option>
                            <option value="newest">Mới nhất</option>
                            <option value="price-asc">Giá: Thấp đến cao</option>
                            <option value="price-desc">Giá: Cao đến thấp</option>
                            <option value="discount">Giảm giá nhiều nhất</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <div class="products-grid" id="products-container">
                <!-- Products will be loaded here via JavaScript -->
            </div>
            <div class="load-more">
                <button id="load-more-btn" class="btn btn-outline">Xem thêm</button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="features-grid">
                <div class="feature-card" data-aos="fade-up">
                    <div class="feature-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Chất liệu an toàn</h3>
                        <p>Sử dụng cotton hữu cơ 100%, mềm mại và an toàn cho làn da nhạy cảm của bé</p>
                    </div>
                </div>
                <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Thiết kế thoải mái</h3>
                        <p>Được thiết kế đặc biệt để mang lại sự thoải mái tối đa cho bé trong mọi hoạt động</p>
                    </div>
                </div>
                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Họa tiết thú vị</h3>
                        <p>Nhiều họa tiết và màu sắc thú vị, phù hợp với sở thích của bé trai</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Parent Guide Section -->
    <section class="parent-guide-section">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Hướng dẫn cho phụ huynh</h2>
            <div class="guide-content" data-aos="fade-up" data-aos-delay="100">
                <div class="guide-item">
                    <div class="guide-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="guide-text">
                        <h3>Chọn đúng kích cỡ</h3>
                        <p>Đo kích thước của bé và tham khảo bảng size của chúng tôi để chọn size phù hợp. Nội y quá chật hoặc quá rộng đều không tốt cho bé.</p>
                    </div>
                </div>
                <div class="guide-item">
                    <div class="guide-icon">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div class="guide-text">
                        <h3>Chất liệu tự nhiên</h3>
                        <p>Ưu tiên chọn nội y làm từ chất liệu tự nhiên như cotton hữu cơ, giúp da bé thoáng khí và tránh kích ứng.</p>
                    </div>
                </div>
                <div class="guide-item">
                    <div class="guide-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="guide-text">
                        <h3>Thay đổi thường xuyên</h3>
                        <p>Nên thay nội y cho bé hàng ngày và giặt sạch bằng xà phòng dịu nhẹ để đảm bảo vệ sinh.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>
    <script src="./script/underwear-boys.js"></script>
    <script src="./script/placeholder-images.js"></script>
    <script src="./script/marquee.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>
</body>
</html>
