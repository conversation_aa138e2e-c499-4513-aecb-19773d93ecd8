// Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Toggle Sidebar
    const toggleSidebarBtn = document.getElementById('toggle-sidebar');
    const sidebar = document.querySelector('.admin-sidebar');
    const content = document.querySelector('.admin-content');
    
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
        });
    }
    
    // Navigation
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.admin-section');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section');
            
            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding section
            sections.forEach(section => {
                if (section.id === `${sectionId}-section`) {
                    section.classList.add('active');
                } else {
                    section.classList.remove('active');
                }
            });
        });
    });
    
    // Logout functionality
    const logoutBtn = document.getElementById('logout-btn');
    const logoutDropdownBtn = document.getElementById('logout-dropdown-btn');
    
    const handleLogout = function() {
        // Show confirmation dialog
        if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
            // Clear user session/localStorage
            localStorage.removeItem('adminUser');
            
            // Redirect to login page
            window.location.href = '../index.html';
        }
    };
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    if (logoutDropdownBtn) {
        logoutDropdownBtn.addEventListener('click', handleLogout);
    }
    
    // Notification handling
    const notificationItems = document.querySelectorAll('.notification-item');
    const notificationBadge = document.querySelector('.notification-badge');
    const markAllReadBtn = document.querySelector('.btn-mark-all-read');
    
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function() {
            notificationItems.forEach(item => {
                item.classList.remove('unread');
            });
            
            // Update notification count
            notificationBadge.textContent = '0';
            notificationBadge.style.display = 'none';
        });
    }
    
    // Table select all functionality
    const selectAllCheckbox = document.getElementById('select-all-users');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Modal functionality
    const modals = document.querySelectorAll('.modal');
    const modalTriggers = document.querySelectorAll('[data-modal]');
    const closeModalBtns = document.querySelectorAll('.btn-close-modal, .btn-close-modal-footer');
    
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            
            if (modal) {
                modal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    });
    
    // Close modal when clicking outside
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    });
    
    // View user details
    const viewUserBtns = document.querySelectorAll('.btn-action.view');
    const userDetailModal = document.getElementById('user-detail-modal');
    
    if (viewUserBtns.length > 0 && userDetailModal) {
        viewUserBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                userDetailModal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            });
        });
    }
    
    // Filter dropdown
    const filterBtn = document.querySelector('.btn-filter');
    const filterMenu = document.querySelector('.filter-menu');
    
    if (filterBtn && filterMenu) {
        filterBtn.addEventListener('click', function() {
            filterMenu.style.display = filterMenu.style.display === 'block' ? 'none' : 'block';
        });
        
        // Close filter menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.filter-dropdown') && filterMenu.style.display === 'block') {
                filterMenu.style.display = 'none';
            }
        });
    }
    
    // Apply filter
    const applyFilterBtn = document.querySelector('.btn-apply-filter');
    
    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', function() {
            // Get filter values
            const roleFilters = document.querySelectorAll('.filter-group:nth-child(1) input:checked');
            const statusFilters = document.querySelectorAll('.filter-group:nth-child(2) input:checked');
            const dateFrom = document.querySelector('.date-range input:nth-child(1)').value;
            const dateTo = document.querySelector('.date-range input:nth-child(2)').value;
            
            // Apply filters (this would typically call an API or filter data client-side)
            console.log('Applying filters:', {
                roles: Array.from(roleFilters).map(el => el.value),
                statuses: Array.from(statusFilters).map(el => el.value),
                dateFrom,
                dateTo
            });
            
            // Close filter menu
            filterMenu.style.display = 'none';
            
            // Show notification
            showNotification('Đã áp dụng bộ lọc', 'success');
        });
    }
    
    // Reset filter
    const resetFilterBtn = document.querySelector('.btn-reset-filter');
    
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', function() {
            // Reset checkboxes
            document.querySelectorAll('.filter-group input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = checkbox.value === 'all';
            });
            
            // Reset date inputs
            document.querySelectorAll('.date-range input').forEach(input => {
                input.value = '';
            });
            
            // Close filter menu
            filterMenu.style.display = 'none';
            
            // Show notification
            showNotification('Đã đặt lại bộ lọc', 'info');
        });
    }
    
    // Add new user
    const addUserBtn = document.querySelector('.btn-add-new');
    
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            // Show add user form/modal
            showNotification('Chức năng thêm người dùng đang được phát triển', 'info');
        });
    }
    
    // Edit user
    const editUserBtns = document.querySelectorAll('.btn-action.edit');
    
    if (editUserBtns.length > 0) {
        editUserBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Show edit user form/modal
                showNotification('Chức năng chỉnh sửa người dùng đang được phát triển', 'info');
            });
        });
    }
    
    // Delete user
    const deleteUserBtns = document.querySelectorAll('.btn-action.delete');
    
    if (deleteUserBtns.length > 0) {
        deleteUserBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
                    // Delete user logic
                    showNotification('Đã xóa người dùng thành công', 'success');
                }
            });
        });
    }
    
    // Check if user is logged in as admin
    function checkAdminAuth() {
        const adminUser = localStorage.getItem('adminUser');
        
        if (!adminUser) {
            // Redirect to login page
            // window.location.href = '../login.html';
            console.log('Admin not logged in');
        }
    }
    
    // Call auth check
    // checkAdminAuth();
});

// Show notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icon = document.createElement('i');
    switch (type) {
        case 'success':
            icon.className = 'fas fa-check-circle';
            break;
        case 'error':
            icon.className = 'fas fa-times-circle';
            break;
        case 'warning':
            icon.className = 'fas fa-exclamation-circle';
            break;
        default:
            icon.className = 'fas fa-info-circle';
    }
    
    notification.appendChild(icon);
    
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);
    
    const container = document.querySelector('.notification-container');
    container.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('hide');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
