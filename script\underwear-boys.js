// Underwear Boys JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sample product data
    const products = [
        {
            id: 'b1',
            name: 'Combo 4 Quần lót bé trai dáng đùi hoạ tiết hoạt hình - Set 4 Quần sịp trẻ em cotton mịn mát',
            price: 199000,
            originalPrice: 249000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m6z21mcbkqwoff@resize_w450_nl.webp',
            category: 'underwear',
            colors: ['Xanh dương', 'Xám', 'Xanh lá'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-11Y'],
            rating: 4.8,
            reviews: 132,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'b2',
            name: 'Set 3 quần đùi chíp cao cấp cho bé trai, chất liệu sợi tre',
            price: 149000,
            originalPrice: 179000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-llzo5ernmn6nf5@resize_w450_nl.webp',
            category: 'undershirt',
            colors: ['Trắng', 'Xanh dương', 'Xám'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y'],
            rating: 4.7,
            reviews: 98,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'b3',
            name: 'Phong Cách Mới 4 Mảnh Quần Đùi Cho Trẻ Em Thoải Mái Cotton Nguyên Chất Thoáng Khí Hoạt Hình Quần Lót Cho Bé Trai 1-11 ',
            price: 249000,
            originalPrice: 299000,
            image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lyua0jxhi942d3@resize_w450_nl.webp',
            category: 'set',
            colors: ['Xanh dương', 'Xám', 'Xanh lá'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y'],
            rating: 4.9,
            reviews: 87,
            isNew: true,
            isBestSeller: true
        },
        {
            id: 'b4',
            name: '80-160cm Xuân Hè Phong Cách Bé Trai Bé Gái Cotton Nguyên Chất Gạc Bộ Đồ Ngủ Trung Lớn Trẻ Em Phiên Bản Hàn Quốc Tất',
            price: 349000,
            originalPrice: 429000,
            image: 'https://down-vn.img.susercontent.com/file/sg-11134201-7rep8-m8bj3wger0rtf8@resize_w450_nl.webp',
            category: 'sleepwear',
            colors: ['Xanh dương', 'Đỏ', 'Xám'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-11Y'],
            rating: 4.8,
            reviews: 76,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'b5',
            name: 'Set 5 đôi tất cotton MICCYBC dễ thương cho bé 1-12 tuổi',
            price: 99000,
            originalPrice: 129000,
            image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lnhy9l30akkba2.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lnhy9l30akkba2.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lnhy9l30akkba2.webp',
            category: 'socks',
            colors: ['Xanh dương', 'Đỏ', 'Đen'],
            sizes: ['1-3Y', '4-5Y', '6-7Y', '8-9Y', '10-12Y'],
            rating: 4.6,
            reviews: 112,
            isNew: false,
            isBestSeller: false
        },
        {
            id: 'b6',
            name: 'Bộ thun tăm lạnh dài tay chất thun tăm lạnh mềm mịn thoáng mát cho bé trai ',
            price: 399000,
            originalPrice: 499000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ras8-m337evxiwhyo1a@resize_w450_nl.webp',
            category: 'sleepwear',
            colors: ['Xanh dương', 'Xám', 'Xanh lá'],
            sizes: ['1-3Y', '4-5Y', '6-7Y', '8-9Y', '10-12Y'],
            rating: 4.8,
            reviews: 94,
            isNew: true,
            isBestSeller: false
        },
        {
            id: 'b7',
            name: 'Combo 4 Quần lót đùi bé trai cotton êm ái thoáng khí, hoạ tiết khủng long đáng yêu',
            price: 229000,
            originalPrice: 279000,
            image: 'https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m6xosnlmaa3lb2@resize_w450_nl.webp',
            category: 'underwear',
            colors: ['Xanh dương', 'Xám', 'Xanh lá'],
            sizes: ['4-5Y', '6-7Y', '8-9Y', '10-11Y'],
            rating: 4.9,
            reviews: 156,
            isNew: false,
            isBestSeller: true
        },
        {
            id: 'b8',
            name: 'Mới 4 Cotton Kid Boy Quần Lót Mềm Tập Đi ',
            price: 179000,
            originalPrice: 229000,
            image: 'https://down-vn.img.susercontent.com/file/cn-11134207-7ras8-m6jevjweqcuj0f@resize_w450_nl.webp',
            category: 'undershirt',
            colors: ['Trắng', 'Xanh dương', 'Xám'],
            sizes: ['2-3Y', '4-5Y', '6-7Y', '8-9Y', '10-11Y'],
            rating: 4.7,
            reviews: 87,
            isNew: false,
            isBestSeller: false
        }
    ];

    // Function to render products
    function renderProducts(productsToRender) {
        const productsContainer = document.getElementById('products-container');
        if (!productsContainer) return;

        productsContainer.innerHTML = '';

        productsToRender.forEach(product => {
            const discount = Math.round((1 - product.price / product.originalPrice) * 100);

            const productCard = document.createElement('div');
            productCard.className = 'product-card';
            productCard.setAttribute('data-category', product.category);

            productCard.innerHTML = `
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}">
                    ${product.isNew ? '<span class="product-tag new-tag">Mới</span>' : ''}
                    ${product.isBestSeller ? '<span class="product-tag bestseller-tag">Bán chạy</span>' : ''}
                    ${discount > 0 ? `<span class="product-tag sale-tag">-${discount}%</span>` : ''}
                    <div class="product-overlay">
                        <button class="product-overlay-btn quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="product-overlay-btn add-to-wishlist-btn" data-product-id="${product.id}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="product-overlay-btn add-to-cart-btn" data-product-id="${product.id}">
                            <i class="fas fa-shopping-bag"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <div class="product-price">
                        ${discount > 0 ? `<span class="original-price">${product.originalPrice.toLocaleString()}đ</span>` : ''}
                        <span class="current-price">${product.price.toLocaleString()}đ</span>
                    </div>
                    <div class="product-meta">
                        <div class="product-rating">
                            <i class="fas fa-star"></i>
                            <span>${product.rating} (${product.reviews})</span>
                        </div>
                        <div class="product-sizes">
                            ${product.sizes.slice(0, 3).map(size => `<span class="product-size">${size}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;

            productsContainer.appendChild(productCard);
        });

        // Add event listeners to buttons
        document.querySelectorAll('.quick-view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                quickViewProduct(productId);
            });
        });

        document.querySelectorAll('.add-to-wishlist-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToWishlist(productId);
            });
        });

        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToCart(productId);
            });
        });
    }

    // Function to filter products
    function filterProducts() {
        const activeFilter = document.querySelector('.filter-btn.active');
        if (!activeFilter) return products;

        const filterValue = activeFilter.getAttribute('data-filter');
        if (filterValue === 'all') return products;

        return products.filter(product => product.category === filterValue);
    }

    // Function to sort products
    function sortProducts(productsToSort) {
        const sortSelect = document.getElementById('sort-select');
        if (!sortSelect) return productsToSort;

        const sortValue = sortSelect.value;

        switch (sortValue) {
            case 'price-asc':
                return [...productsToSort].sort((a, b) => a.price - b.price);
            case 'price-desc':
                return [...productsToSort].sort((a, b) => b.price - a.price);
            case 'newest':
                return [...productsToSort].sort((a, b) => b.isNew - a.isNew);
            case 'discount':
                return [...productsToSort].sort((a, b) => {
                    const discountA = (a.originalPrice - a.price) / a.originalPrice;
                    const discountB = (b.originalPrice - b.price) / b.originalPrice;
                    return discountB - discountA;
                });
            case 'popular':
            default:
                return [...productsToSort].sort((a, b) => {
                    if (a.isBestSeller && !b.isBestSeller) return -1;
                    if (!a.isBestSeller && b.isBestSeller) return 1;
                    return b.reviews - a.reviews;
                });
        }
    }

    // Function to update products based on filters and sorting
    function updateProducts() {
        const filteredProducts = filterProducts();
        const sortedProducts = sortProducts(filteredProducts);
        renderProducts(sortedProducts);
    }

    // Initialize products
    updateProducts();

    // Add event listeners to filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            updateProducts();
        });
    });

    // Add event listener to sort select
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', updateProducts);
    }

    // Quick view product function
    function quickViewProduct(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        showNotification(`Xem nhanh: ${product.name}`, 'info');
        // In a real implementation, this would open a modal with product details
    }

    // Add to wishlist function
    function addToWishlist(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        showNotification(`Đã thêm ${product.name} vào danh sách yêu thích`, 'success');
        // In a real implementation, this would add the product to the wishlist
    }

    // Add to cart function
    function addToCart(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        // Check if user is logged in
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.isLoggedIn) {
            // Show login prompt
            showLoginPrompt();
            return;
        }

        showNotification(`Đã thêm ${product.name} vào giỏ hàng`, 'success');

        // Get current cart from localStorage
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Add product to cart
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price.toLocaleString() + 'đ', // Chuyển đổi giá thành chuỗi có định dạng
            image: product.image,
            image1: product.image, // Thêm image1 để tương thích với AddCart.html
            quantity: 1
        });

        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));

        // Update cart count
        updateCartCount();
    }

    // Show login prompt function
    function showLoginPrompt() {
        // Create login prompt container
        const loginPrompt = document.createElement('div');
        loginPrompt.className = 'login-prompt-overlay';
        loginPrompt.style.position = 'fixed';
        loginPrompt.style.top = '0';
        loginPrompt.style.left = '0';
        loginPrompt.style.width = '100%';
        loginPrompt.style.height = '100%';
        loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        loginPrompt.style.display = 'flex';
        loginPrompt.style.alignItems = 'center';
        loginPrompt.style.justifyContent = 'center';
        loginPrompt.style.zIndex = '9999';
        loginPrompt.style.opacity = '0';
        loginPrompt.style.transition = 'opacity 0.3s ease';

        loginPrompt.innerHTML = `
            <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                    <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                    <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                </div>
                <div class="login-prompt-body" style="padding: 20px;">
                    <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                    <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                        <a href="login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                        <a href="register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                    </div>
                </div>
            </div>
        `;

        // Add to body
        document.body.appendChild(loginPrompt);

        // Show with animation
        setTimeout(() => {
            loginPrompt.style.opacity = '1';
        }, 10);

        // Close button event
        const closeButton = loginPrompt.querySelector('.login-prompt-close');
        closeButton.addEventListener('click', function() {
            loginPrompt.style.opacity = '0';
            setTimeout(() => {
                loginPrompt.remove();
            }, 300);
        });

        // Save current URL to redirect back after login
        localStorage.setItem('redirectAfterLogin', window.location.href);
    }

    // Update cart count function
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = cart.length;
        }
    }

    // Initialize cart count
    updateCartCount();

    // Load more button functionality
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            showNotification('Đang tải thêm sản phẩm...', 'info');
            // In a real implementation, this would load more products
            setTimeout(() => {
                showNotification('Đã tải tất cả sản phẩm', 'success');
                this.disabled = true;
                this.textContent = 'Đã tải tất cả';
            }, 1000);
        });
    }
});
