/* Kids Page Styles */
@import url('style.css');

/* Hero Section */
.product-hero {
    height: 500px;
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../img/kids-hero.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    margin-bottom: 2rem;
    position: relative;
}

.product-hero-content {
    max-width: 800px;
    padding: 0 2rem;
    z-index: 2;
}

.product-hero h1 {
    font-family: var(--heading-font);
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.product-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.btn {
    padding: 12px 25px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background-color: #ff6b6b;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #fa5252;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
    flex-wrap: wrap;
    position: absolute;
    bottom: -25px;
    left: 0;
    width: 100%;
    padding: 0 20px;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.hero-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hero-feature i {
    font-size: 1.5rem;
    color: #ff6b6b;
}

.hero-feature span {
    font-size: 0.9rem;
    font-weight: 500;
    color: #343a40;
}

/* Category Tabs */
.category-tabs {
    margin-bottom: 2rem;
}

.tabs-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem 0;
}

.tab-btn {
    padding: 0.8rem 1.5rem;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tab-btn:hover {
    background-color: var(--secondary-color);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

/* Products Section */
.products-section {
    padding: 2rem 0;
}

.product-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
    background-color: #fff;
    padding: 1rem;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--primary-color);
}

.filter-group select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: #fff;
    font-size: 0.9rem;
    min-width: 150px;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.new-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background-color: var(--accent-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 2;
}

.product-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    display: flex;
    justify-content: space-between;
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.btn-quick-view,
.btn-add-to-cart {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-quick-view {
    background-color: #fff;
    color: var(--primary-color);
}

.btn-quick-view:hover {
    background-color: var(--secondary-color);
}

.btn-add-to-cart {
    background-color: var(--accent-color);
    color: #fff;
}

.btn-add-to-cart:hover {
    background-color: var(--accent-hover);
}

.product-info {
    padding: 1.2rem;
}

.product-name {
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
    height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    font-weight: 600;
    color: var(--accent-color);
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
}

.product-colors {
    display: flex;
    gap: 0.5rem;
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.color-option:hover {
    transform: scale(1.2);
}

.color-option.active {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Color Definitions */
.color-option.black { background-color: #000; }
.color-option.white { background-color: #fff; border: 1px solid #ddd; }
.color-option.red { background-color: #e74c3c; }
.color-option.blue { background-color: #3498db; }
.color-option.green { background-color: #2ecc71; }
.color-option.yellow { background-color: #f1c40f; }
.color-option.purple { background-color: #9b59b6; }
.color-option.orange { background-color: #e67e22; }
.color-option.pink { background-color: #e84393; }
.color-option.brown { background-color: #795548; }
.color-option.gray { background-color: #95a5a6; }

/* Load More Button */
.load-more-container {
    text-align: center;
}

.btn-secondary {
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-secondary:hover {
    background-color: var(--primary-hover);
}

/* Featured Collection */
.featured-collection {
    padding: 4rem 0;
    background-color: var(--secondary-color);
}

.section-title {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 2rem;
}

.collection-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.collection-card {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 400px;
}

.collection-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.collection-card:hover img {
    transform: scale(1.05);
}

.collection-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: #fff;
    text-align: center;
}

.collection-content h3 {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.collection-content p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    opacity: 0.9;
}

.btn-shop {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    background-color: var(--accent-color);
    color: #fff;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    font-weight: 500;
    transition: background-color var(--transition-fast);
}

.btn-shop:hover {
    background-color: var(--accent-hover);
}

/* Size Guide */
.size-guide {
    padding: 4rem 0;
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../img womens/women 12.webp');
    background-size: cover;
    background-position: center;
    color: #fff;
    text-align: center;
}

.size-guide-content {
    max-width: 800px;
    margin: 0 auto;
}

.size-guide h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.size-guide p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.size-guide-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.size-guide-feature {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 30px 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.size-guide-feature:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.2);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 20px;
}

.feature-content h3 {
    font-size: 1.2rem;
    color: white;
    margin-bottom: 10px;
}

.feature-content p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.btn-primary {
    background-color: var(--accent-color);
    color: #fff;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
}

/* Size Guide Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
    padding: 30px 0;
}

.modal-content {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    max-width: 800px;
    width: 90%;
    margin: 30px auto;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: #ff6b6b;
    background-color: rgba(0, 0, 0, 0.05);
}

.modal-content h2 {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.modal-content h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #ff6b6b;
}

.size-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 30px;
}

.size-table h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-align: center;
    position: relative;
    display: inline-block;
    padding-bottom: 8px;
}

.size-table h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #ff6b6b;
}

.size-table table {
    width: 100%;
    border-collapse: collapse;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
}

.size-table th,
.size-table td {
    padding: 0.8rem;
    text-align: center;
    border: 1px solid var(--border-color);
}

.size-table th {
    background-color: #ff6b6b;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.size-table tr:nth-child(even) {
    background-color: var(--secondary-color);
}

.size-table tr:hover td {
    background-color: rgba(255, 107, 107, 0.05);
}

.size-guide-tips {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ff6b6b;
}

.size-guide-tips h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
}

.size-guide-tips ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.size-guide-tips li {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 10px;
    line-height: 1.5;
}

.size-guide-tips p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.size-guide-tips a {
    color: #ff6b6b;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.size-guide-tips a:hover {
    text-decoration: underline;
}

/* Customer Reviews */
.customer-reviews {
    padding: 60px 0;
    background-color: #f8f9fa;
}

.reviews-slider {
    display: flex;
    gap: 30px;
    overflow-x: auto;
    padding: 20px 0;
    scroll-snap-type: x mandatory;
}

.reviews-slider::-webkit-scrollbar {
    height: 8px;
}

.reviews-slider::-webkit-scrollbar-track {
    background: #e9ecef;
    border-radius: 10px;
}

.reviews-slider::-webkit-scrollbar-thumb {
    background: #adb5bd;
    border-radius: 10px;
}

.reviews-slider::-webkit-scrollbar-thumb:hover {
    background: #6c757d;
}

.review-card {
    min-width: 300px;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    scroll-snap-align: start;
    transition: all 0.3s ease;
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.review-rating {
    margin-bottom: 15px;
}

.review-rating i {
    color: #ffc107;
    font-size: 1rem;
}

.review-text {
    font-size: 0.95rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reviewer-name {
    font-size: 0.9rem;
    color: #343a40;
    font-weight: 500;
}

/* Responsive */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .collection-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .kids-hero h1 {
        font-size: 2.5rem;
    }

    .kids-hero p {
        font-size: 1rem;
    }

    .hero-features {
        gap: 15px;
    }

    .size-tables {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .product-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        justify-content: space-between;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .kids-hero {
        padding: 80px 0;
    }

    .kids-hero h1 {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .btn {
        width: 100%;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .size-guide-content p {
        font-size: 1rem;
    }

    .review-card {
        min-width: 250px;
    }
}

@media (max-width: 576px) {
    .kids-hero {
        padding: 60px 0;
    }

    .kids-hero h1 {
        font-size: 1.8rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 10px;
    }

    .hero-feature {
        width: 100%;
        flex-direction: row;
        justify-content: flex-start;
        padding: 10px 15px;
    }

    .tab-buttons {
        flex-wrap: wrap;
    }

    .tab-btn {
        font-size: 0.8rem;
        padding: 8px 15px;
    }

    .product-card {
        max-width: 300px;
        margin: 0 auto;
    }

    .modal-content {
        padding: 20px 15px;
    }
}

    .collection-grid {
        grid-template-columns: 1fr;
    }

    .product-hero h1 {
        font-size: 2rem;
    }

    .tabs-container {
        flex-direction: column;
        align-items: stretch;
    }
}
