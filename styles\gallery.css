/* Gallery Page Styles */
@import url('style.css');

/* Gallery Header */
.gallery-header {
    padding: 4rem 0;
    text-align: center;
    background-color: var(--secondary-color);
    margin-bottom: 2rem;
}

.gallery-header h1 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.gallery-header p {
    color: var(--text-color);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Gallery Filter */
.gallery-filter {
    padding: 1rem 0 2rem;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover {
    background-color: var(--secondary-color);
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

/* Gallery Grid */
.gallery-container {
    padding: 2rem 0 4rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

.gallery-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-medium);
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-img {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.gallery-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.gallery-item:hover .gallery-img img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-info {
    text-align: center;
    color: #fff;
    padding: 1rem;
}

.gallery-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.gallery-info p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Lookbook Section */
.lookbook-section {
    padding: 4rem 0;
    background-color: var(--secondary-color);
    text-align: center;
}

.lookbook-section h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.lookbook-section > .container > p {
    color: var(--text-color);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.lookbook-slider {
    max-width: 900px;
    margin: 0 auto;
}

.lookbook-slide {
    position: relative;
    height: 500px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.lookbook-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.lookbook-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: #fff;
    text-align: left;
}

.lookbook-caption h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.lookbook-caption p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Lightbox Customization */
.lb-data .lb-caption {
    font-family: var(--heading-font);
    font-size: 1.2rem;
}

.lb-data .lb-details {
    width: 100%;
    text-align: center;
}

/* Responsive */
@media (max-width: 1200px) {
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .lookbook-slide {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .gallery-header h1 {
        font-size: 2rem;
    }
    
    .lookbook-section h2 {
        font-size: 2rem;
    }
    
    .lookbook-slide {
        height: 350px;
    }
    
    .lookbook-caption {
        padding: 1.5rem;
    }
    
    .lookbook-caption h3 {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .filter-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .lookbook-slide {
        height: 300px;
    }
}
