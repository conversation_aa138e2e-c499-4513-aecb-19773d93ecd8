<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/sections.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/product.css">
    <link rel="stylesheet" type="text/css" href="../styles/kids.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <title>Thời trang trẻ em | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="product-hero kids-hero">
        <div class="product-hero-content">
            <h1 data-aos="fade-up">Thời trang trẻ em</h1>
            <p data-aos="fade-up" data-aos-delay="200">Thoải mái, năng động và đáng yêu cho bé yêu của bạn</p>
            <div class="hero-buttons" data-aos="fade-up" data-aos-delay="300">
                <a href="#products-container" class="btn btn-primary">Mua sắm ngay</a>
                <a href="#size-guide-section" class="btn btn-secondary">Hướng dẫn chọn size</a>
            </div>
        </div>
        <div class="hero-features" data-aos="fade-up" data-aos-delay="400">
            <div class="hero-feature">
                <i class="fas fa-tshirt"></i>
                <span>Chất liệu an toàn</span>
            </div>
            <div class="hero-feature">
                <i class="fas fa-medal"></i>
                <span>Chất lượng cao</span>
            </div>
            <div class="hero-feature">
                <i class="fas fa-shipping-fast"></i>
                <span>Giao hàng nhanh</span>
            </div>
            <div class="hero-feature">
                <i class="fas fa-exchange-alt"></i>
                <span>Đổi trả dễ dàng</span>
            </div>
        </div>
    </section>

    <!-- Category Tabs -->
    <section class="category-tabs">
        <div class="container">
            <div class="tabs-container" data-aos="fade-up">
                <button class="tab-btn active" data-category="all">Tất cả</button>
                <button class="tab-btn" data-category="girls">Bé gái</button>
                <button class="tab-btn" data-category="boys">Bé trai</button>
                <button class="tab-btn" data-category="baby">Trẻ sơ sinh</button>
                <button class="tab-btn" data-category="accessories">Phụ kiện</button>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <!-- Filter and Sort -->
            <div class="product-filters" data-aos="fade-up">
                <div class="filter-group">
                    <label>Lọc theo:</label>
                    <select id="age-filter">
                        <option value="all">Độ tuổi</option>
                        <option value="0-2">0-2 tuổi</option>
                        <option value="3-5">3-5 tuổi</option>
                        <option value="6-8">6-8 tuổi</option>
                        <option value="9-12">9-12 tuổi</option>
                    </select>
                </div>
                <div class="filter-group">
                    <select id="price-filter">
                        <option value="all">Giá</option>
                        <option value="0-200000">Dưới 200.000đ</option>
                        <option value="200000-500000">200.000đ - 500.000đ</option>
                        <option value="500000+">Trên 500.000đ</option>
                    </select>
                </div>
                <div class="filter-group">
                    <select id="color-filter">
                        <option value="all">Màu sắc</option>
                        <option value="red">Đỏ</option>
                        <option value="blue">Xanh dương</option>
                        <option value="pink">Hồng</option>
                        <option value="yellow">Vàng</option>
                        <option value="green">Xanh lá</option>
                        <option value="black">Đen</option>
                        <option value="white">Trắng</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Sắp xếp:</label>
                    <select id="sort-filter">
                        <option value="newest">Mới nhất</option>
                        <option value="price-asc">Giá: Thấp đến cao</option>
                        <option value="price-desc">Giá: Cao đến thấp</option>
                        <option value="name-asc">Tên: A-Z</option>
                        <option value="name-desc">Tên: Z-A</option>
                    </select>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="products-container" data-aos="fade-up">
                <!-- Products will be added dynamically with JavaScript -->
            </div>

            <!-- Load More Button -->
            <div class="load-more-container" data-aos="fade-up">
                <button id="load-more" class="btn btn-secondary">Xem thêm</button>
            </div>
        </div>
    </section>

    <!-- Featured Collection -->
    <section class="featured-collection bg-light">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Bộ sưu tập nổi bật</h2>
            <div class="collection-grid">
                <div class="collection-card" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvghkmizv0y59f@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvghkmizv0y59f@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvghkmizv0y59f@resize_w450_nl.webp" alt="Summer Collection">
                    <div class="collection-content">
                        <h3>Bộ sưu tập mùa hè</h3>
                        <p>Thoải mái và năng động cho những ngày nắng</p>
                        <a href="#" class="btn-shop">Khám phá</a>
                    </div>
                </div>
                <div class="collection-card" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7qukw-liu33jib6i1e38@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/vn-11134207-7qukw-liu33jib6i1e38@resize_w450_nl.webp" alt="School Collection">
                    <div class="collection-content">
                        <h3>Đồng phục học sinh</h3>
                        <p>Trang phục đi học lịch sự và thoải mái</p>
                        <a href="#" class="btn-shop">Khám phá</a>
                    </div>
                </div>
                <div class="collection-card" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7rbmj-lone94c44n4ab4.webp" alt="Party Collection">
                    <div class="collection-content">
                        <h3>Trang phục dự tiệc</h3>
                        <p>Đáng yêu và lộng lẫy cho những dịp đặc biệt</p>
                        <a href="#" class="btn-shop">Khám phá</a>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <iframe
    id="youtube-player"
    width="0" height="0"
    src="https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=1"
    frameborder="0"
    allow="autoplay; encrypted-media">
</iframe>

    <!-- Size Guide -->
    <section class="size-guide" id="size-guide-section">
        <div class="container">
            <div class="size-guide-content" data-aos="fade-up">
                <h2 class="section-title">Hướng dẫn chọn size</h2>
                <p>Chọn đúng size cho bé yêu của bạn với bảng hướng dẫn chi tiết của chúng tôi</p>
                <div class="size-guide-features">
                    <div class="size-guide-feature" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-icon">
                            <i class="fas fa-ruler"></i>
                        </div>
                        <div class="feature-content">
                            <h3>Đo lường chính xác</h3>
                            <p>Hướng dẫn cách đo kích thước chính xác cho bé</p>
                        </div>
                    </div>
                    <div class="size-guide-feature" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-icon">
                            <i class="fas fa-child"></i>
                        </div>
                        <div class="feature-content">
                            <h3>Theo độ tuổi</h3>
                            <p>Bảng size theo độ tuổi cho bé trai và bé gái</p>
                        </div>
                    </div>
                    <div class="size-guide-feature" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="feature-content">
                            <h3>Mẹo chọn size</h3>
                            <p>Những lời khuyên hữu ích khi chọn size cho bé</p>
                        </div>
                    </div>
                </div>
                <a href="#" class="btn btn-primary" id="size-guide-btn" data-aos="fade-up" data-aos-delay="400">Xem bảng size chi tiết</a>
                <a href="../size-guide.html" class="btn btn-secondary" data-aos="fade-up" data-aos-delay="400">Hướng dẫn đầy đủ</a>
            </div>
        </div>
    </section>

    <!-- Customer Reviews -->
    <section class="customer-reviews bg-light">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Khách hàng nói gì về chúng tôi</h2>
            <div class="reviews-slider" data-aos="fade-up" data-aos-delay="200">
                <div class="review-card">
                    <div class="review-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="review-text">"Quần áo chất lượng tuyệt vời, bé nhà mình rất thích. Vải mềm, thoáng khí và dễ giặt. Sẽ tiếp tục ủng hộ shop!"</p>
                    <div class="reviewer-info">
                        <div class="reviewer-avatar">
                            <img src="../img/avatar-1.jpg" alt="Reviewer">
                        </div>
                        <div class="reviewer-name">Chị Hương Giang</div>
                    </div>
                </div>
                <div class="review-card">
                    <div class="review-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="review-text">"Đặt đồ cho bé trai 5 tuổi, size vừa vặn, màu sắc đẹp như hình. Giao hàng nhanh và đóng gói cẩn thận. Rất hài lòng!"</p>
                    <div class="reviewer-info">
                        <div class="reviewer-avatar">
                            <img src="../img/avatar-2.jpg" alt="Reviewer">
                        </div>
                        <div class="reviewer-name">Anh Minh Tuấn</div>
                    </div>
                </div>
                <div class="review-card">
                    <div class="review-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <p class="review-text">"Váy đầm cho bé gái rất xinh xắn, bé mặc đi tiệc sinh nhật được khen nhiều. Giá cả hợp lý cho chất lượng như vậy."</p>
                    <div class="reviewer-info">
                        <div class="reviewer-avatar">
                            <img src="../img/avatar-3.jpg" alt="Reviewer">
                        </div>
                        <div class="reviewer-name">Chị Thu Hà</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Size Guide Modal -->
    <div class="modal" id="size-guide-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Bảng hướng dẫn chọn size</h2>
            <div class="size-tables">
                <div class="size-table">
                    <h3>Bé gái (cm)</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Size</th>
                                <th>Tuổi</th>
                                <th>Chiều cao</th>
                                <th>Cân nặng</th>
                                <th>Ngực</th>
                                <th>Eo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2</td>
                                <td>2 tuổi</td>
                                <td>80-90</td>
                                <td>10-14kg</td>
                                <td>52-54</td>
                                <td>50-52</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>4 tuổi</td>
                                <td>95-105</td>
                                <td>14-18kg</td>
                                <td>56-58</td>
                                <td>52-54</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>6 tuổi</td>
                                <td>110-120</td>
                                <td>18-22kg</td>
                                <td>60-62</td>
                                <td>54-56</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>8 tuổi</td>
                                <td>125-135</td>
                                <td>22-28kg</td>
                                <td>64-66</td>
                                <td>56-58</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>10 tuổi</td>
                                <td>140-150</td>
                                <td>28-36kg</td>
                                <td>68-70</td>
                                <td>58-60</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="size-table">
                    <h3>Bé trai (cm)</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Size</th>
                                <th>Tuổi</th>
                                <th>Chiều cao</th>
                                <th>Cân nặng</th>
                                <th>Ngực</th>
                                <th>Eo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2</td>
                                <td>2 tuổi</td>
                                <td>80-90</td>
                                <td>10-14kg</td>
                                <td>53-55</td>
                                <td>51-53</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>4 tuổi</td>
                                <td>95-105</td>
                                <td>14-18kg</td>
                                <td>57-59</td>
                                <td>53-55</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>6 tuổi</td>
                                <td>110-120</td>
                                <td>18-22kg</td>
                                <td>61-63</td>
                                <td>55-57</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>8 tuổi</td>
                                <td>125-135</td>
                                <td>22-28kg</td>
                                <td>65-67</td>
                                <td>57-59</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>10 tuổi</td>
                                <td>140-150</td>
                                <td>28-36kg</td>
                                <td>69-71</td>
                                <td>59-61</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="size-guide-tips">
                <h3>Mẹo chọn size</h3>
                <ul>
                    <li>Nếu bé đang ở giữa hai size, hãy chọn size lớn hơn để thoải mái hơn và mặc được lâu hơn.</li>
                    <li>Đối với quần áo mùa đông, nên chọn size lớn hơn một chút để có thể mặc thêm áo bên trong.</li>
                    <li>Đối với trẻ đang trong giai đoạn phát triển nhanh, nên chọn size lớn hơn một chút.</li>
                </ul>
                <p>Để biết thêm chi tiết, vui lòng xem <a href="../size-guide.html">Hướng dẫn chọn size đầy đủ</a> của chúng tôi.</p>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/wishlist-handler.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/voice-search.js"></script>
    <script src="../script/dark-mode.js"></script>
    <script src="../script/social-sharing.js"></script>
    <script src="../script/product-alerts.js"></script>
    <script src="../script/user-display.js"></script>
    <script src="../script/marquee.js"></script>
    <script src="../script/kids.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>

    <script>
        // Khởi tạo AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Xử lý modal size guide
        document.addEventListener('DOMContentLoaded', function() {
            const sizeGuideBtn = document.getElementById('size-guide-btn');
            const sizeGuideModal = document.getElementById('size-guide-modal');
            const closeModal = document.querySelector('.close-modal');

            if (sizeGuideBtn && sizeGuideModal) {
                sizeGuideBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    sizeGuideModal.style.display = 'block';
                });

                closeModal.addEventListener('click', function() {
                    sizeGuideModal.style.display = 'none';
                });

                window.addEventListener('click', function(e) {
                    if (e.target === sizeGuideModal) {
                        sizeGuideModal.style.display = 'none';
                    }
                });
            }

            // Cập nhật số lượng giỏ hàng
            let cart = JSON.parse(localStorage.getItem("cart")) || [];
            let countBag = document.querySelector(".cart-count");
            if (countBag) {
                countBag.textContent = cart.length;
            }
        });







        document.addEventListener("click", function() {
        let youtubePlayer = document.getElementById("youtube-player");
        if (youtubePlayer.src.includes("mute=1")) {
            youtubePlayer.src = "https://www.youtube.com/embed/hlWiI4xVXKY?autoplay=1&loop=1&playlist=hlWiI4xVXKY&mute=0";
            console.log("✅ Đã bật tiếng cho YouTube!");
        }
    }, { once: true }); // Chỉ chạy một lần




    </script>
</body>
</html>
