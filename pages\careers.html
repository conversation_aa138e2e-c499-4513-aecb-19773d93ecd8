<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="../styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <title>Tuyển dụng | Fashion Store</title>

    <style>
        /* Careers Page Styles */
        .careers-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .careers-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .careers-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .careers-header h1 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .careers-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .careers-banner {
            position: relative;
            height: 400px;
            margin-bottom: 60px;
            border-radius: 8px;
            overflow: hidden;
        }

        .careers-banner img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 50px;
        }

        .banner-overlay h2 {
            color: white;
            font-size: 32px;
            margin-bottom: 20px;
            max-width: 600px;
        }

        .banner-overlay p {
            color: white;
            font-size: 18px;
            max-width: 500px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .banner-btn {
            display: inline-block;
            background-color: #ff6b6b;
            color: white;
            padding: 12px 25px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }

        .banner-btn:hover {
            background-color: #ff5252;
        }

        .why-join-us {
            margin-bottom: 60px;
            text-align: center;
        }

        .why-join-us h2 {
            font-size: 28px;
            color: #333;
            margin-bottom: 40px;
            position: relative;
            display: inline-block;
        }

        .why-join-us h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .benefit-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
            text-align: center;
        }

        .benefit-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .benefit-icon {
            width: 70px;
            height: 70px;
            background-color: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 28px;
        }

        .benefit-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 15px;
        }

        .benefit-description {
            color: #666;
            line-height: 1.6;
        }

        .job-openings {
            margin-bottom: 60px;
        }

        .job-openings h2 {
            font-size: 28px;
            color: #333;
            margin-bottom: 40px;
            position: relative;
            display: inline-block;
        }

        .job-openings h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .job-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        .job-filter {
            background-color: #f0f0f0;
            color: #666;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .job-filter:hover, .job-filter.active {
            background-color: #ff6b6b;
            color: white;
        }

        .job-list {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .job-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .job-info {
            flex: 1;
        }

        .job-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
        }

        .job-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .job-meta-item {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 14px;
        }

        .job-meta-item i {
            margin-right: 5px;
            color: #ff6b6b;
        }

        .job-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .job-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .job-tag {
            background-color: #f0f0f0;
            color: #666;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .job-apply {
            margin-left: 20px;
        }

        .apply-btn {
            background-color: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
            display: inline-block;
        }

        .apply-btn:hover {
            background-color: #ff5252;
        }

        .application-process {
            margin-bottom: 60px;
            background-color: #f9f9f9;
            padding: 40px;
            border-radius: 8px;
        }

        .application-process h2 {
            font-size: 28px;
            color: #333;
            margin-bottom: 40px;
            position: relative;
            display: inline-block;
        }

        .application-process h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .process-steps {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 40px;
        }

        .process-step {
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background-color: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .step-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .step-description {
            color: #666;
            line-height: 1.6;
        }

        .process-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -15px;
            width: 30px;
            height: 2px;
            background-color: #ddd;
        }

        .testimonials {
            margin-bottom: 60px;
        }

        .testimonials h2 {
            font-size: 28px;
            color: #333;
            margin-bottom: 40px;
            position: relative;
            display: inline-block;
        }

        .testimonials h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
        }

        .testimonial-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .testimonial-content {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            font-style: italic;
            position: relative;
        }

        .testimonial-content::before {
            content: '"';
            font-size: 60px;
            color: #ff6b6b;
            opacity: 0.2;
            position: absolute;
            top: -20px;
            left: -15px;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
        }

        .author-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .author-info h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .author-info p {
            color: #666;
            font-size: 14px;
        }

        .contact-box {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-top: 40px;
        }

        .contact-box h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .contact-box p {
            color: #666;
            margin-bottom: 20px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .contact-btn {
            display: inline-block;
            background-color: #ff6b6b;
            color: white;
            padding: 12px 25px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .contact-btn:hover {
            background-color: #ff5252;
        }

        @media (max-width: 768px) {
            .banner-overlay {
                padding: 0 30px;
            }

            .banner-overlay h2 {
                font-size: 28px;
            }

            .banner-overlay p {
                font-size: 16px;
            }

            .process-steps {
                flex-direction: column;
            }

            .process-step:not(:last-child)::after {
                display: none;
            }

            .job-card {
                flex-direction: column;
                align-items: flex-start;
            }

            .job-apply {
                margin-left: 0;
                margin-top: 20px;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Careers Content -->
    <div class="careers-container">
        <div class="careers-header">
            <h1>Cơ hội nghề nghiệp tại Fashion Store</h1>
            <p>Khám phá các cơ hội nghề nghiệp hấp dẫn và trở thành một phần của đội ngũ đam mê thời trang và sáng tạo của chúng tôi.</p>
        </div>

        <div class="careers-banner">
            <img src="https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80" alt="Fashion Store Team">
            <div class="banner-overlay">
                <h2>Cùng nhau xây dựng tương lai của thời trang</h2>
                <p>Tại Fashion Store, chúng tôi tin rằng mỗi nhân viên đều có thể tạo ra sự khác biệt. Hãy tham gia cùng chúng tôi và phát triển sự nghiệp của bạn trong một môi trường năng động và sáng tạo.</p>
                <a href="#job-openings" class="banner-btn">Xem vị trí tuyển dụng</a>
            </div>
        </div>

        <div class="why-join-us">
            <h2>Tại sao nên gia nhập Fashion Store?</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="benefit-title">Cơ hội phát triển</h3>
                    <p class="benefit-description">Chúng tôi cung cấp các chương trình đào tạo và phát triển nghề nghiệp để giúp bạn đạt được mục tiêu cá nhân và chuyên môn.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="benefit-title">Văn hóa đội nhóm</h3>
                    <p class="benefit-description">Làm việc trong một môi trường hợp tác, nơi mọi ý tưởng đều được lắng nghe và đánh giá cao.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-medal"></i>
                    </div>
                    <h3 class="benefit-title">Chế độ đãi ngộ hấp dẫn</h3>
                    <p class="benefit-description">Chúng tôi cung cấp mức lương cạnh tranh, bảo hiểm sức khỏe, nghỉ phép có lương và nhiều phúc lợi khác.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <h3 class="benefit-title">Ưu đãi sản phẩm</h3>
                    <p class="benefit-description">Nhân viên được hưởng chiết khấu đặc biệt cho tất cả sản phẩm của Fashion Store.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="benefit-title">Môi trường sáng tạo</h3>
                    <p class="benefit-description">Chúng tôi khuyến khích sự đổi mới và sáng tạo trong mọi khía cạnh công việc.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3 class="benefit-title">Cân bằng công việc - cuộc sống</h3>
                    <p class="benefit-description">Chúng tôi hiểu tầm quan trọng của việc cân bằng giữa công việc và cuộc sống cá nhân.</p>
                </div>
            </div>
        </div>

        <div class="job-openings" id="job-openings">
            <h2>Vị trí tuyển dụng hiện tại</h2>

            <div class="job-filters">
                <button class="job-filter active" data-filter="all">Tất cả</button>
                <button class="job-filter" data-filter="retail">Bán lẻ</button>
                <button class="job-filter" data-filter="marketing">Marketing</button>
                <button class="job-filter" data-filter="design">Thiết kế</button>
                <button class="job-filter" data-filter="it">Công nghệ thông tin</button>
                <button class="job-filter" data-filter="logistics">Kho vận</button>
            </div>

            <div class="job-list">
                <!-- Job 1 -->
                <div class="job-card" data-category="retail">
                    <div class="job-info">
                        <h3 class="job-title">Quản lý cửa hàng</h3>
                        <div class="job-meta">
                            <div class="job-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>TP. Hồ Chí Minh</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-briefcase"></i>
                                <span>Toàn thời gian</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>15-20 triệu/tháng</span>
                            </div>
                        </div>
                        <p class="job-description">Chúng tôi đang tìm kiếm một Quản lý cửa hàng có kinh nghiệm để quản lý hoạt động hàng ngày của cửa hàng, đào tạo nhân viên và đảm bảo dịch vụ khách hàng xuất sắc.</p>
                        <div class="job-tags">
                            <span class="job-tag">Quản lý</span>
                            <span class="job-tag">Bán lẻ</span>
                            <span class="job-tag">Dịch vụ khách hàng</span>
                        </div>
                    </div>
                    <div class="job-apply">
                        <a href="#" class="apply-btn">Ứng tuyển</a>
                    </div>
                </div>

                <!-- Job 2 -->
                <div class="job-card" data-category="marketing">
                    <div class="job-info">
                        <h3 class="job-title">Chuyên viên Marketing số</h3>
                        <div class="job-meta">
                            <div class="job-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Hà Nội</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-briefcase"></i>
                                <span>Toàn thời gian</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>18-25 triệu/tháng</span>
                            </div>
                        </div>
                        <p class="job-description">Chúng tôi đang tìm kiếm một Chuyên viên Marketing số tài năng để phát triển và thực hiện các chiến dịch marketing trên các nền tảng kỹ thuật số nhằm tăng nhận thức về thương hiệu và thúc đẩy doanh số bán hàng.</p>
                        <div class="job-tags">
                            <span class="job-tag">Digital Marketing</span>
                            <span class="job-tag">Social Media</span>
                            <span class="job-tag">SEO/SEM</span>
                        </div>
                    </div>
                    <div class="job-apply">
                        <a href="#" class="apply-btn">Ứng tuyển</a>
                    </div>
                </div>

                <!-- Job 3 -->
                <div class="job-card" data-category="design">
                    <div class="job-info">
                        <h3 class="job-title">Nhà thiết kế thời trang</h3>
                        <div class="job-meta">
                            <div class="job-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>TP. Hồ Chí Minh</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-briefcase"></i>
                                <span>Toàn thời gian</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>20-30 triệu/tháng</span>
                            </div>
                        </div>
                        <p class="job-description">Chúng tôi đang tìm kiếm một Nhà thiết kế thời trang sáng tạo để phát triển các bộ sưu tập mới, theo dõi xu hướng thời trang và đảm bảo các sản phẩm đáp ứng tiêu chuẩn chất lượng cao.</p>
                        <div class="job-tags">
                            <span class="job-tag">Thiết kế thời trang</span>
                            <span class="job-tag">Sáng tạo</span>
                            <span class="job-tag">Xu hướng</span>
                        </div>
                    </div>
                    <div class="job-apply">
                        <a href="#" class="apply-btn">Ứng tuyển</a>
                    </div>
                </div>

                <!-- Job 4 -->
                <div class="job-card" data-category="it">
                    <div class="job-info">
                        <h3 class="job-title">Lập trình viên Frontend</h3>
                        <div class="job-meta">
                            <div class="job-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Hà Nội</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-briefcase"></i>
                                <span>Toàn thời gian</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>25-35 triệu/tháng</span>
                            </div>
                        </div>
                        <p class="job-description">Chúng tôi đang tìm kiếm một Lập trình viên Frontend có kinh nghiệm để phát triển và duy trì website thương mại điện tử của chúng tôi, đảm bảo trải nghiệm người dùng tốt nhất.</p>
                        <div class="job-tags">
                            <span class="job-tag">HTML/CSS</span>
                            <span class="job-tag">JavaScript</span>
                            <span class="job-tag">React</span>
                        </div>
                    </div>
                    <div class="job-apply">
                        <a href="#" class="apply-btn">Ứng tuyển</a>
                    </div>
                </div>

                <!-- Job 5 -->
                <div class="job-card" data-category="logistics">
                    <div class="job-info">
                        <h3 class="job-title">Quản lý kho hàng</h3>
                        <div class="job-meta">
                            <div class="job-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>TP. Hồ Chí Minh</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-briefcase"></i>
                                <span>Toàn thời gian</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>15-20 triệu/tháng</span>
                            </div>
                        </div>
                        <p class="job-description">Chúng tôi đang tìm kiếm một Quản lý kho hàng để giám sát hoạt động kho, quản lý hàng tồn kho và đảm bảo việc giao hàng đúng hẹn.</p>
                        <div class="job-tags">
                            <span class="job-tag">Quản lý kho</span>
                            <span class="job-tag">Logistics</span>
                            <span class="job-tag">Chuỗi cung ứng</span>
                        </div>
                    </div>
                    <div class="job-apply">
                        <a href="#" class="apply-btn">Ứng tuyển</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="application-process">
            <h2>Quy trình ứng tuyển</h2>
            <div class="process-steps">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h3 class="step-title">Nộp hồ sơ</h3>
                    <p class="step-description">Gửi CV và thư xin việc của bạn qua mẫu đơn trực tuyến hoặc email.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">2</div>
                    <h3 class="step-title">Sàng lọc hồ sơ</h3>
                    <p class="step-description">Đội ngũ nhân sự của chúng tôi sẽ xem xét hồ sơ của bạn và liên hệ nếu phù hợp.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">3</div>
                    <h3 class="step-title">Phỏng vấn</h3>
                    <p class="step-description">Phỏng vấn trực tiếp hoặc qua video với quản lý tuyển dụng và đội ngũ liên quan.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">4</div>
                    <h3 class="step-title">Đánh giá</h3>
                    <p class="step-description">Có thể bao gồm bài kiểm tra kỹ năng hoặc bài tập thực hành tùy thuộc vào vị trí.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">5</div>
                    <h3 class="step-title">Đề xuất công việc</h3>
                    <p class="step-description">Nếu thành công, bạn sẽ nhận được đề xuất công việc và thảo luận về các điều khoản.</p>
                </div>
            </div>
        </div>

        <div class="testimonials">
            <h2>Nhân viên nói gì về chúng tôi</h2>
            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "Làm việc tại Fashion Store là một trải nghiệm tuyệt vời. Tôi được khuyến khích phát triển kỹ năng và theo đuổi đam mê của mình. Văn hóa công ty thực sự hỗ trợ và môi trường làm việc rất năng động."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Nguyễn Thị Minh">
                        </div>
                        <div class="author-info">
                            <h4>Nguyễn Thị Minh</h4>
                            <p>Quản lý Marketing | 3 năm</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "Tôi bắt đầu làm việc tại Fashion Store như một nhân viên bán hàng và hiện đang là quản lý cửa hàng. Công ty thực sự đầu tư vào sự phát triển của nhân viên và tạo cơ hội thăng tiến cho những người chăm chỉ."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Trần Văn Hùng">
                        </div>
                        <div class="author-info">
                            <h4>Trần Văn Hùng</h4>
                            <p>Quản lý Cửa hàng | 5 năm</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "Là một nhà thiết kế, tôi đánh giá cao sự tự do sáng tạo mà Fashion Store mang lại. Tôi được khuyến khích thử nghiệm và đổi mới, điều này đã giúp tôi phát triển rất nhiều trong sự nghiệp."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Lê Thị Hương">
                        </div>
                        <div class="author-info">
                            <h4>Lê Thị Hương</h4>
                            <p>Nhà Thiết kế Thời trang | 2 năm</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="contact-box">
            <h3>Không tìm thấy vị trí phù hợp?</h3>
            <p>Chúng tôi luôn tìm kiếm những tài năng xuất sắc để gia nhập đội ngũ. Hãy gửi CV của bạn và chúng tôi sẽ liên hệ khi có vị trí phù hợp.</p>
            <a href="./contact.html" class="contact-btn">Liên hệ với chúng tôi</a>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/voice-search.js"></script>
    <script src="../script/dark-mode.js"></script>
    <script src="../script/user-display.js"></script>
    <script src="../script/marquee.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Careers page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Job Filter Functionality
            const filterButtons = document.querySelectorAll('.job-filter');
            const jobCards = document.querySelectorAll('.job-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get filter value
                    const filterValue = this.getAttribute('data-filter');

                    // Filter jobs
                    jobCards.forEach(card => {
                        if (filterValue === 'all') {
                            card.style.display = 'flex';
                        } else if (card.getAttribute('data-category') === filterValue) {
                            card.style.display = 'flex';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Apply button click event
            const applyButtons = document.querySelectorAll('.apply-btn');

            applyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    const jobTitle = this.closest('.job-card').querySelector('.job-title').textContent;

                    alert(`Bạn đang ứng tuyển vị trí: ${jobTitle}. Vui lòng điền thông tin của bạn trong form ứng tuyển.`);

                    // Redirect to application form (you can uncomment this when you have the form ready)
                    // window.location.href = './application-form.html';
                });
            });
        });
    </script>
</body>
</html>