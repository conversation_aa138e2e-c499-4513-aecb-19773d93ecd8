// Đ<PERSON>nh nghĩa các cấp độ thành viên
const membershipLevels = {
    standard: {
        name: 'standard_member',
        minPoints: 0,
        maxPoints: 999,
        cardClass: 'standard-card',
        benefits: [
            'birthday_gift',
            'exclusive_offers',
            'earn_points'
        ]
    },
    silver: {
        name: 'silver_member',
        minPoints: 1000,
        maxPoints: 2999,
        cardClass: 'silver-card',
        benefits: [
            'all_standard_benefits',
            'free_shipping',
            'priority_support',
            'early_access'
        ]
    },
    gold: {
        name: 'gold_member',
        minPoints: 3000,
        maxPoints: 4999,
        cardClass: 'gold-card',
        benefits: [
            'all_silver_benefits',
            'exclusive_events',
            'personal_shopper',
            'birthday_discount'
        ]
    },
    platinum: {
        name: 'platinum_member',
        minPoints: 5000,
        maxPoints: 9999,
        cardClass: 'platinum-card',
        benefits: [
            'all_gold_benefits',
            'vip_customer_service',
            'free_alterations',
            'extended_returns'
        ]
    },
    diamond: {
        name: 'diamond_member',
        minPoints: 10000,
        maxPoints: Infinity,
        cardClass: 'diamond-card',
        benefits: [
            'all_platinum_benefits',
            'private_shopping',
            'unlimited_free_shipping',
            'birthday_gift_card',
            'exclusive_collaborations'
        ]
    }
};

// Định nghĩa các phần thưởng đổi điểm
const redeemOptions = [
    {
        name: 'discount_10',
        points: 100,
        icon: 'fas fa-tag',
        action: redeemDiscount
    },
    {
        name: 'free_shipping',
        points: 50,
        icon: 'fas fa-truck',
        action: redeemFreeShipping
    },
    {
        name: 'gift_card',
        points: 500,
        icon: 'fas fa-gift',
        action: redeemGiftCard
    },
    {
        name: 'exclusive_item',
        points: 1000,
        icon: 'fas fa-tshirt',
        action: redeemExclusiveItem
    }
];

// Hàm khởi tạo trang thẻ thành viên
document.addEventListener('DOMContentLoaded', function() {
    // Kiểm tra đăng nhập
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || !user.isLoggedIn) {
        // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
        localStorage.setItem('redirectAfterLogin', window.location.href);

        // Chuyển hướng đến trang đăng nhập
        window.location.href = './pages/login.html';
        return;
    }

    // Lấy thông tin thành viên từ localStorage hoặc tạo mới nếu chưa có
    let memberInfo = JSON.parse(localStorage.getItem('memberInfo'));
    if (!memberInfo) {
        // Tạo thông tin thành viên mới
        memberInfo = {
            id: generateMemberId(),
            points: 0,
            level: 'standard',
            joinDate: new Date().toISOString(),
            transactions: []
        };

        // Lưu thông tin thành viên vào localStorage
        localStorage.setItem('memberInfo', JSON.stringify(memberInfo));
    }

    // Cập nhật thông tin thẻ thành viên
    updateMembershipCard(user, memberInfo);

    // Cập nhật hiển thị cấp độ thành viên
    updateMembershipLevels(memberInfo);

    // Cập nhật hiển thị đổi điểm
    updateRedeemOptions(memberInfo);

    // Thêm sự kiện cho các nút đổi điểm
    addRedeemEvents();
});

// Hàm tạo ID thành viên
function generateMemberId() {
    const prefix = 'FS';
    const year = new Date().getFullYear();
    const random = Math.floor(10000 + Math.random() * 90000);
    return `${prefix}-${year}-${random}`;
}

// Hàm cập nhật thông tin thẻ thành viên
function updateMembershipCard(user, memberInfo) {
    // Cập nhật tên thành viên
    const memberName = document.getElementById('member-name');
    if (memberName) {
        memberName.textContent = user.fullName || user.username;
    }

    // Cập nhật ID thành viên
    const memberId = document.getElementById('member-id');
    if (memberId) {
        memberId.textContent = `ID: ${memberInfo.id}`;
    }

    // Cập nhật điểm thành viên
    const memberPoints = document.getElementById('member-points');
    if (memberPoints) {
        memberPoints.textContent = memberInfo.points.toLocaleString();
    }

    // Cập nhật ngày tham gia
    const memberSince = document.getElementById('member-since');
    if (memberSince) {
        const joinDate = new Date(memberInfo.joinDate);
        memberSince.textContent = joinDate.toLocaleDateString();
    }

    // Cập nhật cấp độ thành viên
    const memberLevel = document.getElementById('member-level');
    if (memberLevel) {
        const levelNames = {
            'standard_member': 'Thành viên Tiêu chuẩn',
            'silver_member': 'Thành viên Bạc',
            'gold_member': 'Thành viên Vàng',
            'platinum_member': 'Thành viên Bạch Kim',
            'diamond_member': 'Thành viên Kim Cương'
        };
        memberLevel.textContent = levelNames[membershipLevels[memberInfo.level].name] || membershipLevels[memberInfo.level].name;
    }

    // Cập nhật class cho thẻ thành viên
    const membershipCard = document.querySelector('.membership-card');
    if (membershipCard) {
        // Xóa tất cả các class cấp độ
        Object.values(membershipLevels).forEach(level => {
            membershipCard.classList.remove(level.cardClass);
        });

        // Thêm class cấp độ hiện tại
        membershipCard.classList.add(membershipLevels[memberInfo.level].cardClass);
    }

    // Cập nhật điểm hiện có trong phần đổi điểm
    const availablePoints = document.getElementById('available-points');
    if (availablePoints) {
        availablePoints.textContent = memberInfo.points.toLocaleString();
    }
}

// Hàm cập nhật hiển thị cấp độ thành viên
function updateMembershipLevels(memberInfo) {
    // Lấy tất cả các thẻ cấp độ
    const levelCards = document.querySelectorAll('.level-card');

    // Xóa class active từ tất cả các thẻ
    levelCards.forEach(card => {
        card.classList.remove('active');
    });

    // Thêm class active cho thẻ cấp độ hiện tại
    levelCards.forEach((card, index) => {
        const levelKeys = Object.keys(membershipLevels);
        if (index < levelKeys.length && levelKeys[index] === memberInfo.level) {
            card.classList.add('active');
        }
    });
}

// Hàm cập nhật hiển thị đổi điểm
function updateRedeemOptions(memberInfo) {
    // Lấy tất cả các nút đổi điểm
    const redeemButtons = document.querySelectorAll('.redeem-btn');

    // Cập nhật trạng thái của các nút dựa trên số điểm hiện có
    redeemButtons.forEach((button, index) => {
        if (index < redeemOptions.length) {
            const option = redeemOptions[index];

            // Kiểm tra xem có đủ điểm để đổi không
            if (memberInfo.points < option.points) {
                button.disabled = true;
                button.title = 'Không đủ điểm';
            } else {
                button.disabled = false;
                button.title = '';
            }
        }
    });
}

// Hàm thêm sự kiện cho các nút đổi điểm
function addRedeemEvents() {
    // Lấy tất cả các nút đổi điểm
    const redeemButtons = document.querySelectorAll('.redeem-btn');

    // Thêm sự kiện click cho các nút
    redeemButtons.forEach((button, index) => {
        if (index < redeemOptions.length) {
            button.addEventListener('click', function() {
                // Lấy thông tin thành viên từ localStorage
                const memberInfo = JSON.parse(localStorage.getItem('memberInfo'));

                // Kiểm tra xem có đủ điểm để đổi không
                if (memberInfo.points >= redeemOptions[index].points) {
                    // Gọi hàm xử lý đổi điểm tương ứng
                    redeemOptions[index].action(memberInfo, redeemOptions[index]);
                } else {
                    // Hiển thị thông báo không đủ điểm
                    showNotification('Không đủ điểm', 'error');
                }
            });
        }
    });
}

// Hàm xử lý đổi điểm lấy mã giảm giá
function redeemDiscount(memberInfo, option) {
    // Trừ điểm
    memberInfo.points -= option.points;

    // Tạo mã giảm giá
    const discountCode = generateDiscountCode();

    // Lưu mã giảm giá vào localStorage
    const discountCodes = JSON.parse(localStorage.getItem('discountCodes')) || [];
    discountCodes.push({
        code: discountCode,
        discount: 10,
        used: false,
        expiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // Hết hạn sau 30 ngày
    });
    localStorage.setItem('discountCodes', JSON.stringify(discountCodes));

    // Lưu thông tin thành viên đã cập nhật
    localStorage.setItem('memberInfo', JSON.stringify(memberInfo));

    // Cập nhật hiển thị
    updateMembershipCard({ fullName: document.getElementById('member-name').textContent }, memberInfo);
    updateRedeemOptions(memberInfo);

    // Hiển thị thông báo thành công
    showNotification('Đã tạo mã giảm giá thành công: ' + discountCode, 'success');
}

// Hàm xử lý đổi điểm lấy miễn phí vận chuyển
function redeemFreeShipping(memberInfo, option) {
    // Trừ điểm
    memberInfo.points -= option.points;

    // Lưu miễn phí vận chuyển vào localStorage
    localStorage.setItem('freeShipping', 'true');

    // Lưu thông tin thành viên đã cập nhật
    localStorage.setItem('memberInfo', JSON.stringify(memberInfo));

    // Cập nhật hiển thị
    updateMembershipCard({ fullName: document.getElementById('member-name').textContent }, memberInfo);
    updateRedeemOptions(memberInfo);

    // Hiển thị thông báo thành công
    showNotification('Đã đổi điểm lấy miễn phí vận chuyển thành công', 'success');
}

// Hàm xử lý đổi điểm lấy thẻ quà tặng
function redeemGiftCard(memberInfo, option) {
    // Trừ điểm
    memberInfo.points -= option.points;

    // Tạo mã thẻ quà tặng
    const giftCardCode = generateGiftCardCode();

    // Lưu mã thẻ quà tặng vào localStorage
    const giftCards = JSON.parse(localStorage.getItem('giftCards')) || [];
    giftCards.push({
        code: giftCardCode,
        amount: 100000,
        used: false,
        expiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() // Hết hạn sau 90 ngày
    });
    localStorage.setItem('giftCards', JSON.stringify(giftCards));

    // Lưu thông tin thành viên đã cập nhật
    localStorage.setItem('memberInfo', JSON.stringify(memberInfo));

    // Cập nhật hiển thị
    updateMembershipCard({ fullName: document.getElementById('member-name').textContent }, memberInfo);
    updateRedeemOptions(memberInfo);

    // Hiển thị thông báo thành công
    showNotification('Đã tạo thẻ quà tặng thành công: ' + giftCardCode, 'success');
}

// Hàm xử lý đổi điểm lấy sản phẩm độc quyền
function redeemExclusiveItem(memberInfo, option) {
    // Trừ điểm
    memberInfo.points -= option.points;

    // Lưu thông tin thành viên đã cập nhật
    localStorage.setItem('memberInfo', JSON.stringify(memberInfo));

    // Cập nhật hiển thị
    updateMembershipCard({ fullName: document.getElementById('member-name').textContent }, memberInfo);
    updateRedeemOptions(memberInfo);

    // Hiển thị thông báo thành công
    showNotification('Đã đổi điểm lấy sản phẩm độc quyền thành công', 'success');
}

// Hàm tạo mã giảm giá
function generateDiscountCode() {
    const prefix = 'DISC';
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}-${random}`;
}

// Hàm tạo mã thẻ quà tặng
function generateGiftCardCode() {
    const prefix = 'GIFT';
    const random = Math.random().toString(36).substring(2, 10).toUpperCase();
    return `${prefix}-${random}`;
}
