/* Chatbot Styles */
#chat-bot {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 350px;
    height: 450px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    display: none;
    flex-direction: column;
    overflow: hidden;
    z-index: 999;
    transition: all 0.3s ease;
}

.chat-header {
    background-color: #ff6b6b;
    color: white;
    padding: 15px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#chat-close {
    cursor: pointer;
    font-size: 20px;
    transition: transform 0.2s;
}

#chat-close:hover {
    transform: scale(1.2);
}

.chat-body {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.chat-message {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
    margin-bottom: 5px;
    position: relative;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.chat-message.bot {
    background-color: #f1f1f1;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 5px;
}

.chat-message:not(.bot) {
    background-color: #ff6b6b;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 5px;
}

#chat-input {
    width: calc(100% - 30px);
    padding: 12px 15px;
    border: none;
    border-top: 1px solid #eee;
    outline: none;
    font-size: 14px;
    margin-top: auto;
}

#chat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 99;
    transition: transform 0.3s, background-color 0.3s;
}

#chat-toggle:hover {
    transform: scale(1.1);
    background-color: #ff5252;
}

#chat-toggle i {
    font-size: 24px;
}

.chat-typing {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background-color: #f1f1f1;
    border-radius: 18px;
    max-width: 80px;
    align-self: flex-start;
    margin-bottom: 5px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: #666;
    border-radius: 50%;
    animation: typingAnimation 1s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingAnimation {
    0% { opacity: 0.3; transform: translateY(0); }
    50% { opacity: 1; transform: translateY(-5px); }
    100% { opacity: 0.3; transform: translateY(0); }
}

.chat-options {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.chat-option-btn {
    background-color: #f1f1f1;
    color: #333;
    border: none;
    border-radius: 15px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.chat-option-btn:hover {
    background-color: #e0e0e0;
}

/* Responsive Styles */
@media (max-width: 576px) {
    #chat-bot {
        width: 300px;
        height: 400px;
        bottom: 80px;
        right: 10px;
    }
}
