<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/women-new.css">
    <link rel="stylesheet" type="text/css" href="./styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">
    <link rel="stylesheet" type="text/css" href="./styles/auth-check.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <title>Thời trang Nữ | Fashion Store</title>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="women-hero">
        <div class="women-hero-content">
            <h1 data-aos="fade-up">Thời Trang Nữ</h1>
            <p data-aos="fade-up" data-aos-delay="200">Khám phá bộ sưu tập mới nhất với những thiết kế độc đáo và phong cách</p>
            <div class="women-hero-buttons" data-aos="fade-up" data-aos-delay="400">
                <a href="./womenproducts.html" class="hero-btn hero-btn-primary">Mua sắm ngay</a>
                <a href="#women-categories" class="hero-btn hero-btn-secondary">Xem danh mục</a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="women-categories" id="women-categories">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Danh Mục Sản Phẩm</h2>
            <div class="categories-container">
                <div class="category-item" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://down-vn.img.susercontent.com/file/sg-11134201-7rd48-m7ug6yhta6v1d9@resize_w450_nl.webp" alt="Váy & Đầm" class="category-image">
                    <div class="category-overlay">
                        <h3>Váy & Đầm</h3>
                        <p>Các mẫu váy đầm thời trang cho mọi dịp</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m7bt2jmjiorc11@resize_w450_nl.webp" alt="Áo" class="category-image">
                    <div class="category-overlay">
                        <h3>Áo</h3>
                        <p>Áo sơ mi, áo thun, áo kiểu cho phái đẹp</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lyeiaqil8n1e26@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lyeiaqil8n1e26@resize_w450_nl.webp" alt="Quần" class="category-image">
                    <div class="category-overlay">
                        <h3>Quần</h3>
                        <p>Quần jean, quần rộng viền ren, quần short thời trang</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="400">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lkjq3ktvqlt6e9@resize_w450_nl.webp" alt="Áo khoác" class="category-image">
                    <div class="category-overlay">
                        <h3>Áo khoác</h3>
                        <p>Áo khoác thời trang cho mọi mùa</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="500">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7ra0g-m8bgpae7oygef0@resize_w450_nl.webp" alt="Đồ thể thao" class="category-image">
                    <div class="category-overlay">
                        <h3>Đồ thể thao</h3>
                        <p>Trang phục thể thao năng động</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="600">
                    <img src="https://down-vn.img.susercontent.com/file/d12f24469e54c07e08a1ff486f6928e2@resize_w450_nl.webp" alt="Đồ ngủ" class="category-image">
                    <div class="category-overlay">
                        <h3>Đồ ngủ</h3>
                        <p>Đồ ngủ thoải mái và quyến rũ</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="700">
                    <img src="https://down-vn.img.susercontent.com/file/f0e241b3503fc24b67031c433e8ebe6e@resize_w450_nl.webp" alt="Phụ kiện" class="category-image">
                    <div class="category-overlay">
                        <h3>Phụ kiện</h3>
                        <p>Túi xách, giày dép, trang sức và hơn thế nữa</p>
                        <a href="./womenproducts.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
                <div class="category-item" data-aos="fade-up" data-aos-delay="800">
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-logi2yseeyea83@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-logi2yseeyea83@resize_w450_nl.webp" alt="Đồ lót" class="category-image">
                    <div class="category-overlay">
                        <h3>Đồ lót</h3>
                        <p>Đồ lót thoải mái và quyến rũ</p>
                        <a href="./underwear-women.html" class="category-btn">Xem thêm</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="featured-products">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Sản Phẩm Nổi Bật</h2>
            <div class="products-grid">
                <!-- Product 1 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="product-image">
                        <span class="product-badge new">Mới</span>
                        <img src="./img womens/img women 1.webp" alt="Thời trang công sở trẻ trung">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Thời trang công sở trẻ trung nhất</h3>
                        <div class="product-price">
                            <span class="current-price">350.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="product-image">
                        <span class="product-badge sale">-20%</span>
                        <img src="./img womens/img women 2.webp" alt="Bộ đồ thời trang mùa hè">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Bộ đồ thời trang mùa hè</h3>
                        <div class="product-price">
                            <span class="current-price">450.000đ</span>
                            <span class="old-price">560.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="product-image">
                        <img src="./img womens/women 3.webp" alt="Áo sơ mi thời trang phong cách Hồng Kông">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Áo sơ mi thời trang phong cách Hồng Kông phù hợp với nữ mùa hè</h3>
                        <div class="product-price">
                            <span class="current-price">380.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #f8f9fa;" title="Kem"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="product-image">
                        <img src="./img womens/women 4.webp" alt="Hoodie In Họa Tiết + Chân Váy Xếp Ly">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Hoodie In Họa Tiết + Chân Váy Xếp Ly Lưng Cao Thanh Lịch</h3>
                        <div class="product-price">
                            <span class="current-price">520.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #6c757d;" title="Xám"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 5 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="product-image">
                        <span class="product-badge sale">-15%</span>
                        <img src="./img womens/women 5.webp" alt="Set Áo Sơ Mi polo Ngắn Tay + Quần Dài">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Set Áo Sơ Mi polo Ngắn Tay + Quần Dài Ống Rộng Thời Trang Nữ</h3>
                        <div class="product-price">
                            <span class="current-price">425.000đ</span>
                            <span class="old-price">500.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                            <span class="color-option" style="background-color: #f8f9fa;" title="Kem"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 6 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="product-image">
                        <img src="./img womens/women 6.webp" alt="Set bộ thời trang nữ đi chơi">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Set bộ thời trang nữ đi chơi chất cotton lạnh cổ chữ V</h3>
                        <div class="product-price">
                            <span class="current-price">480.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                            <span class="color-option" style="background-color: #fff; border: 1px solid #ddd;" title="Trắng"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 7 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="700">
                    <div class="product-image">
                        <span class="product-badge new">Mới</span>
                        <img src="./img womens/women 14.webp" alt="Đầm cổ tròn sát nách ôm dáng">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Đầm cổ tròn sát nách ôm dáng ngắn đen v2 - ORIMI</h3>
                        <div class="product-price">
                            <span class="current-price">520.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #000;" title="Đen"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>

                <!-- Product 8 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="800">
                    <div class="product-image">
                        <span class="product-badge sale">-30%</span>
                        <img src="./img womens/women 16.webp" alt="Váy sơ mi denim chân váy bèo tầng">
                        <div class="product-actions">
                            <button class="product-action-btn" title="Thêm vào yêu thích"><i class="far fa-heart"></i></button>
                            <button class="product-action-btn" title="Xem nhanh"><i class="far fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Váy sơ mi denim chân váy bèo tầng siêu xinh V27</h3>
                        <div class="product-price">
                            <span class="current-price">290.000đ</span>
                            <span class="old-price">415.000đ</span>
                        </div>
                        <div class="product-colors">
                            <span class="color-option" style="background-color: #3b5998;" title="Xanh"></span>
                        </div>
                        <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
                    </div>
                </div>
            </div>
            <a href="./womenproducts.html" class="view-all-btn" data-aos="fade-up">Xem tất cả sản phẩm</a>
        </div>
    </section>

    <!-- Lookbook Section -->
    <section class="lookbook">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Bộ Sưu Tập Mới</h2>
            <div class="lookbook-grid">
                <div class="lookbook-item" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lwjfhjfn23ojcb@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lwjfhjfn23ojcb@resize_w450_nl.webphttps://down-vn.img.susercontent.com/file/cn-11134207-7r98o-lwjfhjfn23ojcb@resize_w450_nl.webp" alt="Summer Collection" class="lookbook-image">
                    <div class="lookbook-overlay">
                        <h3 class="lookbook-title">Bộ Sưu Tập Mùa Hè 2024</h3>
                        <a href="./womenproducts.html" class="lookbook-btn">Khám phá ngay</a>
                    </div>
                </div>
                <div class="lookbook-item" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lwavav435sd7dd@resize_w450_nl.webp" alt="Office Wear" class="lookbook-image">
                    <div class="lookbook-overlay">
                        <h3 class="lookbook-title">Thời Trang Công Sở</h3>
                        <a href="./womenproducts.html" class="lookbook-btn">Khám phá ngay</a>
                    </div>
                </div>
                <div class="lookbook-item" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://down-vn.img.susercontent.com/file/vn-11134207-7r98o-lvvcga0urf15f3@resize_w450_nl.webp" alt="Casual Collection" class="lookbook-image">
                    <div class="lookbook-overlay">
                        <h3 class="lookbook-title">Thời Trang Dạo Phố</h3>
                        <a href="./womenproducts.html" class="lookbook-btn">Khám phá ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="footerbox"></div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>
    <script src="./script/wishlist-handler.js"></script>
    <script src="./script/auth-check.js"></script>
    <script src="./script/voice-search.js"></script>
    <script src="./script/dark-mode.js"></script>
    <script src="./script/user-display.js"></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Smooth scroll for category link
        $('a[href="#women-categories"]').on('click', function(event) {
            event.preventDefault();
            $('html, body').animate({
                scrollTop: $($(this).attr('href')).offset().top - 100
            }, 800);
        });

        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                // Check if user is logged in
                const user = JSON.parse(localStorage.getItem('user'));
                if (!user || !user.isLoggedIn) {
                    // Show login prompt
                    showLoginPrompt();
                    return;
                }

                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-title').textContent;
                const productPrice = productCard.querySelector('.current-price').textContent;
                const productImage = productCard.querySelector('.product-image img').src;

                // Get cart from localStorage
                let cart = JSON.parse(localStorage.getItem('cart')) || [];

                // Add product to cart
                cart.push({
                    name: productName,
                    price: parseInt(productPrice.replace(/\D/g, '')),
                    image: productImage,
                    quantity: 1
                });

                // Save cart to localStorage
                localStorage.setItem('cart', JSON.stringify(cart));

                // Update cart count
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = cart.length;
                }

                // Show notification
                showNotification('Sản phẩm đã được thêm vào giỏ hàng', 'success');
            });
        });

        /**
         * Show login prompt popup
         */
        function showLoginPrompt() {
            // Create login prompt container
            const loginPrompt = document.createElement('div');
            loginPrompt.className = 'login-prompt-overlay';
            loginPrompt.style.position = 'fixed';
            loginPrompt.style.top = '0';
            loginPrompt.style.left = '0';
            loginPrompt.style.width = '100%';
            loginPrompt.style.height = '100%';
            loginPrompt.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loginPrompt.style.display = 'flex';
            loginPrompt.style.alignItems = 'center';
            loginPrompt.style.justifyContent = 'center';
            loginPrompt.style.zIndex = '9999';
            loginPrompt.style.opacity = '0';
            loginPrompt.style.transition = 'opacity 0.3s ease';

            loginPrompt.innerHTML = `
                <div class="login-prompt" style="background-color: white; border-radius: 10px; width: 400px; max-width: 90%; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); overflow: hidden;">
                    <div class="login-prompt-header" style="padding: 20px; background-color: #f5f5f5; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd;">
                        <h3 style="margin: 0; font-size: 1.2rem; color: #333;">Đăng nhập để tiếp tục</h3>
                        <button class="login-prompt-close" style="background: none; border: none; cursor: pointer; font-size: 1.2rem;"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="login-prompt-body" style="padding: 20px;">
                        <p style="margin-top: 0; margin-bottom: 20px; color: #666;">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.</p>
                        <div class="login-prompt-buttons" style="display: flex; gap: 10px;">
                            <a href="./pages/login.html" class="btn-login" style="flex: 1; padding: 10px; background-color: #e74c3c; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng nhập</a>
                            <a href="./pages/register.html" class="btn-register" style="flex: 1; padding: 10px; background-color: #3498db; color: white; text-align: center; text-decoration: none; border-radius: 5px; font-weight: 500;">Đăng ký</a>
                        </div>
                    </div>
                </div>
            `;

            // Add to body
            document.body.appendChild(loginPrompt);

            // Show with animation
            setTimeout(() => {
                loginPrompt.style.opacity = '1';
            }, 10);

            // Close button event
            const closeButton = loginPrompt.querySelector('.login-prompt-close');
            closeButton.addEventListener('click', function() {
                loginPrompt.style.opacity = '0';
                setTimeout(() => {
                    loginPrompt.remove();
                }, 300);
            });

            // Save current URL to redirect back after login
            localStorage.setItem('redirectAfterLogin', window.location.href);
        }

        // Product quick view
        document.querySelectorAll('.product-action-btn[title="Xem nhanh"]').forEach(button => {
            button.addEventListener('click', function() {
                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-title').textContent;
                const productPrice = productCard.querySelector('.current-price').textContent;
                const productImage = productCard.querySelector('.product-image img').src;

                // Redirect to product detail page (in a real implementation)
                // For now, just show a notification
                showNotification('Chức năng xem nhanh đang được phát triển', 'info');
            });
        });

        // Wishlist functionality is now handled by wishlist-handler.js
    </script>
</body>
</html>
