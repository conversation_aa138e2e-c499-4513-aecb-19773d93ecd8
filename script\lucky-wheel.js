/**
 * Lucky Wheel for Discount Coupons
 * This script creates an interactive wheel that users can spin to win discount coupons
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const spinPopup = document.querySelector('.spin-popup');
    const closeSpinBtn = document.querySelector('.close-spin');
    const spinBtn = document.getElementById('spin-btn');
    const wheelCanvas = document.getElementById('wheel');
    const spinResult = document.getElementById('spin-result');
    const wheelTrigger = document.querySelector('.wheel-trigger');
    
    // Check if elements exist
    if (!wheelCanvas || !spinPopup) return;
    
    // Wheel configuration
    const prizes = [
        { text: "5%", color: "#FF6B6B", probability: 0.3 },
        { text: "10%", color: "#4CAF50", probability: 0.2 },
        { text: "15%", color: "#2196F3", probability: 0.15 },
        { text: "20%", color: "#9C27B0", probability: 0.1 },
        { text: "25%", color: "#FF9800", probability: 0.05 },
        { text: "30%", color: "#E91E63", probability: 0.02 },
        { text: "50%", color: "#FFEB3B", probability: 0.01 },
        { text: "Không trúng", color: "#CCCCCC", probability: 0.17 }
    ];
    
    // Canvas setup
    const ctx = wheelCanvas.getContext('2d');
    const centerX = wheelCanvas.width / 2;
    const centerY = wheelCanvas.height / 2;
    const radius = Math.min(centerX, centerY) - 10;
    
    // Draw wheel
    function drawWheel() {
        ctx.clearRect(0, 0, wheelCanvas.width, wheelCanvas.height);
        
        // Draw segments
        const totalSegments = prizes.length;
        const arcSize = 2 * Math.PI / totalSegments;
        
        for (let i = 0; i < totalSegments; i++) {
            const angle = i * arcSize;
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, angle, angle + arcSize);
            ctx.closePath();
            ctx.fillStyle = prizes[i].color;
            ctx.fill();
            ctx.stroke();
            
            // Draw text
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(angle + arcSize / 2);
            ctx.textAlign = 'right';
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 18px Arial';
            ctx.fillText(prizes[i].text, radius - 20, 5);
            ctx.restore();
        }
        
        // Draw center circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
        ctx.fillStyle = '#fff';
        ctx.fill();
        ctx.stroke();
        
        // Draw arrow
        ctx.beginPath();
        ctx.moveTo(centerX + radius + 10, centerY);
        ctx.lineTo(centerX + radius - 10, centerY - 15);
        ctx.lineTo(centerX + radius - 10, centerY + 15);
        ctx.closePath();
        ctx.fillStyle = '#333';
        ctx.fill();
    }
    
    // Initialize wheel
    drawWheel();
    
    // Spin the wheel
    function spinWheel() {
        // Disable button during spin
        spinBtn.disabled = true;
        spinResult.textContent = "Đang quay...";
        
        // Generate random result based on probability
        const random = Math.random();
        let cumulativeProbability = 0;
        let selectedPrize = prizes[prizes.length - 1]; // Default to last prize
        
        for (let i = 0; i < prizes.length; i++) {
            cumulativeProbability += prizes[i].probability;
            if (random < cumulativeProbability) {
                selectedPrize = prizes[i];
                break;
            }
        }
        
        // Calculate rotation angle
        const prizeIndex = prizes.indexOf(selectedPrize);
        const segmentSize = 360 / prizes.length;
        const rotationAngle = 3600 + (360 - (prizeIndex * segmentSize + segmentSize / 2));
        
        // Apply rotation animation
        wheelCanvas.style.transition = 'transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67)';
        wheelCanvas.style.transform = `rotate(${rotationAngle}deg)`;
        
        // Show result after animation
        setTimeout(() => {
            if (selectedPrize.text !== "Không trúng") {
                // Generate coupon code
                const couponCode = generateCouponCode(selectedPrize.text);
                
                // Save to localStorage
                saveCoupon(couponCode, selectedPrize.text);
                
                // Show result
                spinResult.innerHTML = `
                    <p>Chúc mừng! Bạn đã trúng mã giảm giá ${selectedPrize.text}!</p>
                    <div class="coupon-code">${couponCode}</div>
                    <button class="copy-btn" onclick="copyToClipboard('${couponCode}')">Sao chép</button>
                `;
                
                // Show confetti
                createConfetti();
            } else {
                spinResult.textContent = "Rất tiếc, bạn không trúng thưởng. Hãy thử lại sau!";
            }
            
            // Reset wheel after 1 second
            setTimeout(() => {
                wheelCanvas.style.transition = 'none';
                wheelCanvas.style.transform = 'rotate(0deg)';
                spinBtn.disabled = false;
            }, 1000);
        }, 4000);
    }
    
    // Generate coupon code
    function generateCouponCode(discount) {
        const prefix = "SALE";
        const discountValue = discount.replace("%", "");
        const randomChars = Math.random().toString(36).substring(2, 6).toUpperCase();
        return `${prefix}${discountValue}${randomChars}`;
    }
    
    // Save coupon to localStorage
    function saveCoupon(code, discount) {
        const coupons = JSON.parse(localStorage.getItem('coupons')) || [];
        coupons.push({
            code: code,
            discount: discount,
            date: new Date().toISOString()
        });
        localStorage.setItem('coupons', JSON.stringify(coupons));
    }
    
    // Create confetti effect
    function createConfetti() {
        const confettiCount = 100;
        const colors = ['#ff6b6b', '#4CAF50', '#2196F3', '#9C27B0', '#FF9800', '#E91E63', '#FFEB3B'];
        
        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.left = `${Math.random() * 100}%`;
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.animation = `confetti-fall ${1 + Math.random() * 3}s forwards`;
            spinPopup.appendChild(confetti);
            
            // Remove confetti after animation
            setTimeout(() => {
                confetti.remove();
            }, 4000);
        }
    }
    
    // Copy to clipboard function
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(() => {
            alert('Đã sao chép mã giảm giá: ' + text);
        });
    };
    
    // Event listeners
    if (spinBtn) {
        spinBtn.addEventListener('click', spinWheel);
    }
    
    if (closeSpinBtn) {
        closeSpinBtn.addEventListener('click', () => {
            spinPopup.classList.add('hide-spin');
        });
    }
    
    if (wheelTrigger) {
        wheelTrigger.addEventListener('click', () => {
            spinPopup.classList.remove('hide-spin');
        });
    }
    
    // Show wheel popup after 30 seconds on site
    setTimeout(() => {
        // Only show if user hasn't spun today
        const lastSpin = localStorage.getItem('lastSpinDate');
        const today = new Date().toDateString();
        
        if (!lastSpin || lastSpin !== today) {
            spinPopup.classList.remove('hide-spin');
        }
    }, 30000);
});
