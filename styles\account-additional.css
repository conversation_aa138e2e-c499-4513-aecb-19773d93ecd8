/* Additional Account Page Styles */

/* Timeline Styles */
.account-activity {
    margin-top: 40px;
    margin-bottom: 40px;
}

.activity-timeline {
    position: relative;
    padding-left: 30px;
    margin-top: 20px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 10px;
    height: 100%;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
}

.timeline-icon {
    position: absolute;
    left: -30px;
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
    z-index: 1;
}

.timeline-content {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-content h4 {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 5px;
}

.timeline-content p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.timeline-date {
    font-size: 0.8rem;
    color: var(--text-light);
    font-style: italic;
}

/* Product Slider Styles */
.recommended-products {
    margin-top: 40px;
}

.product-slider {
    margin-top: 20px;
    margin-bottom: 20px;
}

.product-slide {
    padding: 10px;
}

.slick-dots {
    bottom: -30px;
}

.slick-dots li button:before {
    font-size: 10px;
    color: var(--primary-color);
}

.slick-dots li.slick-active button:before {
    color: var(--primary-dark);
}

.slick-prev, .slick-next {
    width: 30px;
    height: 30px;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.slick-prev:before, .slick-next:before {
    color: var(--primary-color);
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.slick-prev:before {
    content: '\f104';
}

.slick-next:before {
    content: '\f105';
}

.slick-prev:hover, .slick-next:hover {
    background-color: var(--primary-color);
}

.slick-prev:hover:before, .slick-next:hover:before {
    color: white;
}

/* Chatbot Styles */
#chat-bot {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    display: none;
    flex-direction: column;
    z-index: 1000;
    overflow: hidden;
}

.chat-header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
}

#chat-close {
    cursor: pointer;
    font-size: 1.2rem;
}

.chat-body {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.chat-message {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
    background-color: #f1f3f5;
    align-self: flex-end;
}

.chat-message.bot {
    background-color: var(--primary-color);
    color: white;
    align-self: flex-start;
}

#chat-input {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    outline: none;
}

#chat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
    z-index: 999;
    transition: all 0.3s ease;
}

#chat-toggle:hover {
    transform: scale(1.1);
}

/* Lucky Wheel Styles */
.spin-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.hide-spin {
    display: none;
}

.spin-container {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 400px;
    position: relative;
}

.close-spin {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #adb5bd;
}

.close-spin:hover {
    color: var(--primary-color);
}

#wheel {
    margin: 20px auto;
}

#spin-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

#spin-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

#spin-result {
    margin-top: 20px;
    font-weight: 500;
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* Footer Styles */
.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.contact-icon {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 107, 107, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 10px;
    flex-shrink: 0;
}

.contact-text {
    font-size: 0.9rem;
    color: var(--text-light);
}

.footer-newsletter h4 {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 10px;
}

.footer-newsletter p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 15px;
}

.footer-newsletter-form {
    display: flex;
    position: relative;
}

.footer-newsletter-form input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
}

.footer-newsletter-form button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.footer-newsletter-form button:hover {
    color: var(--primary-dark);
}

.app-download {
    margin-top: 20px;
}

.app-download h4 {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 10px;
}

.app-buttons {
    display: flex;
    gap: 10px;
}

.app-buttons img {
    height: 40px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.app-buttons img:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive */
@media (max-width: 992px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    #chat-bot {
        width: 280px;
        bottom: 80px;
        right: 10px;
    }
    
    .spin-container {
        padding: 20px;
        max-width: 300px;
    }
}
