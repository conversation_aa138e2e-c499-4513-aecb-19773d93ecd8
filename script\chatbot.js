/**
 * AI Chatbot for Customer Support
 * This script creates an interactive chatbot to assist customers
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const chatBot = document.getElementById('chat-bot');
    const chatToggle = document.getElementById('chat-toggle');
    const chatClose = document.getElementById('chat-close');
    const chatInput = document.getElementById('chat-input');
    const chatBody = document.querySelector('.chat-body');
    
    // Check if elements exist
    if (!chatBot || !chatToggle) return;
    
    // Predefined responses
    const responses = {
        greeting: [
            "Xin chào! Tôi có thể giúp gì cho bạn?",
            "Chào bạn! Tôi là trợ lý ảo của Fashion Store. Bạn cần hỗ trợ gì?",
            "Xin chào! Rất vui được hỗ trợ bạn hôm nay."
        ],
        product: [
            "Chúng tôi có nhiều sản phẩm thời trang nam, nữ và trẻ em. Bạn có thể xem chi tiết tại trang sản phẩm.",
            "Fashion Store cung cấp đa dạng các sản phẩm thời trang từ áo, quần, váy đến phụ kiện. Bạn đang tìm kiếm sản phẩm nào?",
            "Bạn có thể tìm kiếm sản phẩm theo danh mục hoặc sử dụng thanh tìm kiếm ở trên cùng của trang web."
        ],
        price: [
            "Giá sản phẩm của chúng tôi dao động từ 100.000đ đến 2.000.000đ tùy theo loại sản phẩm.",
            "Bạn có thể xem giá chi tiết của từng sản phẩm trên trang sản phẩm. Chúng tôi thường xuyên có các chương trình khuyến mãi.",
            "Chúng tôi cam kết mang đến mức giá cạnh tranh nhất cho khách hàng. Bạn có thể xem giá trên trang sản phẩm."
        ],
        shipping: [
            "Chúng tôi miễn phí vận chuyển cho đơn hàng từ 500.000đ. Thời gian giao hàng từ 2-5 ngày tùy khu vực.",
            "Fashion Store hỗ trợ giao hàng toàn quốc với phí vận chuyển từ 30.000đ. Miễn phí vận chuyển cho đơn hàng từ 500.000đ.",
            "Thời gian giao hàng thông thường là 2-3 ngày đối với khu vực thành phố lớn và 3-5 ngày đối với các tỉnh khác."
        ],
        return: [
            "Chúng tôi chấp nhận đổi trả trong vòng 7 ngày kể từ ngày nhận hàng nếu sản phẩm còn nguyên tem mác.",
            "Chính sách đổi trả: Bạn có thể đổi trả sản phẩm trong vòng 7 ngày nếu sản phẩm còn nguyên vẹn và có hóa đơn mua hàng.",
            "Để đổi trả sản phẩm, vui lòng liên hệ với chúng tôi qua số hotline hoặc email để được hướng dẫn chi tiết."
        ],
        payment: [
            "Chúng tôi chấp nhận thanh toán qua thẻ tín dụng, chuyển khoản ngân hàng, ví điện tử và COD (thanh toán khi nhận hàng).",
            "Fashion Store hỗ trợ nhiều phương thức thanh toán: thẻ tín dụng/ghi nợ, chuyển khoản, ví điện tử (Momo, ZaloPay, VNPay) và COD.",
            "Bạn có thể thanh toán an toàn qua cổng thanh toán bảo mật của chúng tôi hoặc chọn thanh toán khi nhận hàng (COD)."
        ],
        contact: [
            "Bạn có thể liên hệ với chúng tôi qua số hotline: 1900 1234 hoặc email: <EMAIL>",
            "Để liên hệ với bộ phận CSKH, vui lòng gọi 1900 1234 (8h-22h hàng ngày) hoặc gửi email đến <EMAIL>",
            "Fashion Store có các cửa hàng tại Hà Nội, TP.HCM và Đà Nẵng. Bạn có thể xem địa chỉ chi tiết trong mục Liên hệ."
        ],
        promotion: [
            "Hiện tại chúng tôi đang có chương trình giảm giá 50% cho tất cả sản phẩm mùa hè và miễn phí vận chuyển cho đơn hàng từ 500.000đ.",
            "Khuyến mãi hot: Giảm 50% cho sản phẩm mùa hè, tặng quà cho 100 khách hàng đầu tiên, giảm thêm 10% khi thanh toán qua ví điện tử.",
            "Đừng bỏ lỡ Flash Sale hàng ngày từ 12h-14h với mức giảm giá lên đến 70% cho các sản phẩm hot!"
        ],
        default: [
            "Xin lỗi, tôi không hiểu câu hỏi của bạn. Bạn có thể nói rõ hơn được không?",
            "Tôi chưa hiểu rõ ý của bạn. Bạn có thể diễn đạt theo cách khác được không?",
            "Rất tiếc, tôi không có thông tin về vấn đề này. Bạn có thể liên hệ trực tiếp với bộ phận CSKH qua số 1900 1234."
        ]
    };
    
    // Common questions for quick selection
    const commonQuestions = [
        { text: "Chính sách vận chuyển", category: "shipping" },
        { text: "Chính sách đổi trả", category: "return" },
        { text: "Phương thức thanh toán", category: "payment" },
        { text: "Khuyến mãi hiện có", category: "promotion" },
        { text: "Thông tin liên hệ", category: "contact" }
    ];
    
    // Show common questions
    function showCommonQuestions() {
        const questionsHTML = `
            <div class="chat-message bot">
                Bạn có thể hỏi tôi về:
                <div class="chat-options">
                    ${commonQuestions.map(q => `<button class="chat-option-btn" data-category="${q.category}">${q.text}</button>`).join('')}
                </div>
            </div>
        `;
        chatBody.insertAdjacentHTML('beforeend', questionsHTML);
        
        // Add event listeners to question buttons
        document.querySelectorAll('.chat-option-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const category = this.getAttribute('data-category');
                handleUserMessage(this.textContent);
                setTimeout(() => {
                    botResponse(category);
                }, 500);
            });
        });
        
        // Scroll to bottom
        chatBody.scrollTop = chatBody.scrollHeight;
    }
    
    // Initialize chatbot
    function initChatbot() {
        // Add initial greeting
        const randomGreeting = getRandomResponse('greeting');
        const greetingHTML = `<div class="chat-message bot">${randomGreeting}</div>`;
        chatBody.innerHTML = greetingHTML;
        
        // Show common questions after greeting
        setTimeout(showCommonQuestions, 500);
    }
    
    // Get random response from category
    function getRandomResponse(category) {
        const responseArray = responses[category] || responses.default;
        return responseArray[Math.floor(Math.random() * responseArray.length)];
    }
    
    // Analyze user message and determine category
    function analyzeMessage(message) {
        message = message.toLowerCase();
        
        if (message.includes('xin chào') || message.includes('chào') || message.includes('hi') || message.includes('hello')) {
            return 'greeting';
        } else if (message.includes('sản phẩm') || message.includes('hàng hóa') || message.includes('mua') || message.includes('bán')) {
            return 'product';
        } else if (message.includes('giá') || message.includes('bao nhiêu') || message.includes('tiền')) {
            return 'price';
        } else if (message.includes('ship') || message.includes('vận chuyển') || message.includes('giao hàng') || message.includes('gửi')) {
            return 'shipping';
        } else if (message.includes('đổi') || message.includes('trả') || message.includes('hoàn tiền') || message.includes('bảo hành')) {
            return 'return';
        } else if (message.includes('thanh toán') || message.includes('trả tiền') || message.includes('visa') || message.includes('mastercard') || message.includes('cod')) {
            return 'payment';
        } else if (message.includes('liên hệ') || message.includes('hotline') || message.includes('email') || message.includes('số điện thoại')) {
            return 'contact';
        } else if (message.includes('khuyến mãi') || message.includes('giảm giá') || message.includes('sale') || message.includes('ưu đãi')) {
            return 'promotion';
        } else {
            return 'default';
        }
    }
    
    // Handle user message
    function handleUserMessage(message) {
        // Add user message to chat
        const userMessageHTML = `<div class="chat-message">${message}</div>`;
        chatBody.insertAdjacentHTML('beforeend', userMessageHTML);
        
        // Clear input
        chatInput.value = '';
        
        // Scroll to bottom
        chatBody.scrollTop = chatBody.scrollHeight;
    }
    
    // Show bot response
    function botResponse(category) {
        // Show typing indicator
        const typingHTML = `
            <div class="chat-typing">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;
        chatBody.insertAdjacentHTML('beforeend', typingHTML);
        chatBody.scrollTop = chatBody.scrollHeight;
        
        // Remove typing indicator and show response after delay
        setTimeout(() => {
            const typingIndicator = document.querySelector('.chat-typing');
            if (typingIndicator) {
                typingIndicator.remove();
            }
            
            const response = getRandomResponse(category);
            const botMessageHTML = `<div class="chat-message bot">${response}</div>`;
            chatBody.insertAdjacentHTML('beforeend', botMessageHTML);
            
            // Show common questions again after response
            setTimeout(showCommonQuestions, 500);
            
            // Scroll to bottom
            chatBody.scrollTop = chatBody.scrollHeight;
        }, 1000);
    }
    
    // Event listeners
    if (chatToggle) {
        chatToggle.addEventListener('click', function() {
            chatBot.style.display = 'flex';
            chatToggle.style.display = 'none';
            
            // Initialize chatbot if first time
            if (chatBody.children.length === 0) {
                initChatbot();
            }
        });
    }
    
    if (chatClose) {
        chatClose.addEventListener('click', function() {
            chatBot.style.display = 'none';
            chatToggle.style.display = 'flex';
        });
    }
    
    if (chatInput) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && chatInput.value.trim() !== '') {
                const userMessage = chatInput.value.trim();
                
                // Handle user message
                handleUserMessage(userMessage);
                
                // Analyze message and respond
                const category = analyzeMessage(userMessage);
                setTimeout(() => {
                    botResponse(category);
                }, 500);
            }
        });
    }
});
