/* Popup Styles */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.popup-overlay.active {
    opacity: 1;
    visibility: visible;
}

.popup {
    position: relative;
    width: 90%;
    max-width: 800px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.4s ease;
}

.popup-overlay.active .popup {
    transform: translateY(0);
    opacity: 1;
}

.popup-header {
    padding: 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popup-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.popup-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.popup-close:hover {
    color: #ff6b6b;
}

.popup-body {
    padding: 20px;
}

.popup-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Newsletter Popup */
.newsletter-popup .popup-body {
    display: flex;
    padding: 0;
}

.newsletter-popup .popup-image-container {
    flex: 1;
    max-width: 50%;
    overflow: hidden;
}

.newsletter-popup .popup-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.newsletter-popup .popup-content {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.newsletter-popup h3 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #333;
}

.newsletter-popup p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.newsletter-form input {
    padding: 14px 18px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    background-color: #f9f9f9;
    transition: all 0.2s;
}

.newsletter-form input:focus {
    border-color: #ff6b6b;
    background-color: #fff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.newsletter-form button {
    padding: 14px 20px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.newsletter-form button::after {
    content: '\f061';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.8rem;
}

.newsletter-form button:hover {
    background-color: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.2);
}

.newsletter-popup .dont-show {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
    color: #666;
    font-size: 0.9rem;
}

.newsletter-popup .benefits {
    margin-top: 25px;
}

.newsletter-popup .benefit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.newsletter-popup .benefit-icon {
    width: 24px;
    height: 24px;
    background-color: rgba(255, 107, 107, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff6b6b;
    font-size: 0.8rem;
}

.newsletter-popup .benefit-text {
    font-size: 0.9rem;
    color: #555;
}

.newsletter-popup .dont-show input {
    margin: 0;
}

/* Login Popup */
.login-popup .popup-body {
    display: flex;
    padding: 0;
}

.login-popup .login-image {
    flex: 1;
    max-width: 40%;
    background-image: url('../img banner/banner 2.avif');
    background-size: cover;
    background-position: center;
    position: relative;
}

.login-popup .login-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,107,107,0.8) 0%, rgba(255,107,107,0.4) 100%);
}

.login-popup .login-image-content {
    position: absolute;
    bottom: 40px;
    left: 30px;
    right: 30px;
    color: white;
}

.login-popup .login-image-content h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.login-popup .login-image-content p {
    font-size: 1rem;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.login-popup .login-form-container {
    flex: 1;
    padding: 40px;
}

.login-popup .form-group {
    margin-bottom: 20px;
}

.login-popup label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.login-popup input {
    width: 100%;
    padding: 14px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    background-color: #f9f9f9;
    transition: all 0.2s;
}

.login-popup input:focus {
    border-color: #ff6b6b;
    background-color: #fff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.login-popup .forgot-password {
    display: block;
    text-align: right;
    margin-top: 5px;
    color: #ff6b6b;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s;
}

.login-popup .forgot-password:hover {
    color: #ff5252;
    text-decoration: underline;
}

.login-popup .login-btn {
    width: 100%;
    padding: 14px 20px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-popup .login-btn::after {
    content: '\f061';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.8rem;
}

.login-popup .login-btn:hover {
    background-color: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.2);
}

.login-popup .social-login {
    margin-top: 30px;
}

.login-popup .social-login p {
    color: #666;
    margin-bottom: 15px;
    position: relative;
    text-align: center;
    font-size: 0.9rem;
}

.login-popup .social-login p::before,
.login-popup .social-login p::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: #ddd;
}

.login-popup .social-login p::before {
    left: 0;
}

.login-popup .social-login p::after {
    right: 0;
}

.login-popup .social-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.login-popup .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.login-popup .social-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.login-popup .facebook {
    background-color: #3b5998;
}

.login-popup .google {
    background-color: #db4437;
}

.login-popup .register-link {
    text-align: center;
    margin-top: 25px;
    color: #666;
    font-size: 0.95rem;
}

.login-popup .register-link a {
    color: #ff6b6b;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.login-popup .register-link a:hover {
    color: #ff5252;
    text-decoration: underline;
}

/* Quick View Popup */
.quickview-popup .popup {
    max-width: 1000px;
}

.quickview-popup .popup-body {
    padding: 0;
    display: flex;
}

.quickview-popup .product-image-container {
    flex: 1;
    max-width: 50%;
    position: relative;
    background-color: #f9f9f9;
}

.quickview-popup .product-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.quickview-popup .product-image img {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.quickview-popup .product-image:hover img {
    transform: scale(1.05);
}

.quickview-popup .product-badges {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quickview-popup .product-badge {
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 3px;
    text-transform: uppercase;
}

.quickview-popup .badge-new {
    background-color: #4CAF50;
    color: white;
}

.quickview-popup .badge-sale {
    background-color: #ff6b6b;
    color: white;
}

.quickview-popup .product-actions {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.quickview-popup .product-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s;
}

.quickview-popup .product-action-btn:hover {
    background-color: #ff6b6b;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.quickview-popup .product-details {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
}

.quickview-popup .product-title {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #333;
}

.quickview-popup .product-price-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.quickview-popup .product-price {
    font-size: 1.8rem;
    color: #ff6b6b;
    font-weight: 600;
}

.quickview-popup .product-old-price {
    font-size: 1.2rem;
    color: #999;
    text-decoration: line-through;
}

.quickview-popup .product-discount {
    padding: 3px 8px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 3px;
    font-size: 0.9rem;
    font-weight: 600;
}

.quickview-popup .product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 20px;
}

.quickview-popup .rating-stars {
    color: #FFD700;
}

.quickview-popup .rating-count {
    color: #666;
    font-size: 0.9rem;
}

.quickview-popup .product-description {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
}

.quickview-popup .product-options {
    margin-bottom: 25px;
}

.quickview-popup .option-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #333;
}

.quickview-popup .color-options,
.quickview-popup .size-options {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.quickview-popup .color-option {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.quickview-popup .color-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.quickview-popup .color-option.selected::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px solid #ff6b6b;
    border-radius: 50%;
}

.quickview-popup .size-option {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.quickview-popup .size-option:hover {
    border-color: #ff6b6b;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.quickview-popup .size-option.selected {
    background-color: #ff6b6b;
    border-color: #ff6b6b;
    color: white;
}

.quickview-popup .product-meta {
    margin-bottom: 25px;
}

.quickview-popup .meta-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.quickview-popup .meta-label {
    width: 100px;
    color: #666;
}

.quickview-popup .meta-value {
    color: #333;
    font-weight: 500;
}

.quickview-popup .quantity-selector {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.quickview-popup .quantity-btn {
    width: 35px;
    height: 35px;
    background-color: #f0f0f0;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quickview-popup .quantity-btn:hover {
    background-color: #e0e0e0;
}

.quickview-popup .quantity-input {
    width: 60px;
    height: 35px;
    text-align: center;
    border: 1px solid #ddd;
    margin: 0 8px;
    font-size: 1rem;
}

.quickview-popup .product-actions-container {
    display: flex;
    gap: 15px;
}

.quickview-popup .add-to-cart {
    flex: 1;
    padding: 14px 25px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.quickview-popup .add-to-cart:hover {
    background-color: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.2);
}

.quickview-popup .wishlist-btn {
    width: 45px;
    height: 45px;
    border-radius: 5px;
    background-color: #f0f0f0;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
}

.quickview-popup .wishlist-btn:hover {
    background-color: #ff6b6b;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .popup {
        max-width: 95%;
    }

    /* Newsletter Popup Responsive */
    .newsletter-popup .popup-body {
        flex-direction: column;
    }

    .newsletter-popup .popup-image-container {
        max-width: 100%;
        height: 200px;
    }

    .newsletter-popup .popup-content {
        padding: 30px;
    }

    /* Login Popup Responsive */
    .login-popup .popup-body {
        flex-direction: column;
    }

    .login-popup .login-image {
        max-width: 100%;
        height: 200px;
    }

    .login-popup .login-form-container {
        padding: 30px;
    }

    /* Quick View Popup Responsive */
    .quickview-popup .popup-body {
        flex-direction: column;
    }

    .quickview-popup .product-image-container,
    .quickview-popup .product-details {
        max-width: 100%;
    }

    .quickview-popup .product-image {
        height: 300px;
    }

    .quickview-popup .product-details {
        padding: 30px;
    }
}

@media (max-width: 576px) {
    .popup-header {
        padding: 15px;
    }

    .popup-header h3 {
        font-size: 1.2rem;
    }

    /* Newsletter Popup Responsive */
    .newsletter-popup .popup-content {
        padding: 20px;
    }

    .newsletter-popup h3 {
        font-size: 1.5rem;
    }

    .newsletter-popup .benefits {
        display: none;
    }

    /* Login Popup Responsive */
    .login-popup .login-image-content {
        bottom: 20px;
        left: 20px;
        right: 20px;
    }

    .login-popup .login-image-content h3 {
        font-size: 1.5rem;
    }

    .login-popup .login-form-container {
        padding: 20px;
    }

    /* Quick View Popup Responsive */
    .quickview-popup .product-image {
        height: 250px;
    }

    .quickview-popup .product-details {
        padding: 20px;
    }

    .quickview-popup .product-title {
        font-size: 1.5rem;
    }

    .quickview-popup .product-price {
        font-size: 1.5rem;
    }

    .quickview-popup .product-actions-container {
        flex-direction: column;
        gap: 10px;
    }

    .quickview-popup .wishlist-btn {
        width: 100%;
    }
}


.spin-popup {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.hide-spin {
    display: none;
}

.spin-container {
    background: white;
    padding: 20px;
    text-align: center;
    border-radius: 10px;
    position: relative;
}

.close-spin {
    position: absolute;
    top: 10px; right: 15px;
    cursor: pointer;
    font-size: 24px;
}

#spin-btn {
    margin-top: 20px;
    padding: 10px 20px;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    background: #e74c3c;
    color: white;
    cursor: pointer;
}

#spin-result {
    margin-top: 15px;
    font-size: 18px;
    color: #2c3e50;
}
