// Placeholder Images JavaScript

// This script adds placeholder images to the product cards
// In a real implementation, these would be replaced with actual product images

document.addEventListener('DOMContentLoaded', function() {
    // Function to create a placeholder image URL with specified dimensions and text
    function createPlaceholderImage(width, height, text, bgColor, textColor) {
        const colors = {
            'women': 'ff6b6b',
            'men': '339af0',
            'girls': 'f06595',
            'boys': '51cf66'
        };
        
        const bg = bgColor || colors[text.split(' ')[1].toLowerCase()] || 'cccccc';
        const fg = textColor || 'ffffff';
        
        return `https://via.placeholder.com/${width}x${height}/${bg}/${fg}?text=${encodeURIComponent(text)}`;
    }
    
    // Function to set placeholder images for product cards
    function setPlaceholderImages() {
        // Determine the page type
        let pageType = '';
        if (window.location.pathname.includes('underwear-women')) {
            pageType = 'Women';
        } else if (window.location.pathname.includes('underwear-men')) {
            pageType = 'Men';
        } else if (window.location.pathname.includes('underwear-girls')) {
            pageType = 'Girls';
        } else if (window.location.pathname.includes('underwear-boys')) {
            pageType = 'Boys';
        }
        
        if (!pageType) return;
        
        // Set hero image
        const heroSection = document.querySelector('.product-hero');
        if (heroSection) {
            const heroImage = createPlaceholderImage(1200, 400, `Underwear ${pageType}`, null, null);
            heroSection.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('${heroImage}')`;
        }
        
        // Set product images
        const productImages = document.querySelectorAll('.product-image img');
        productImages.forEach((img, index) => {
            if (!img.src || img.src.includes('undefined') || img.src.includes('img/underwear')) {
                img.src = createPlaceholderImage(300, 400, `${pageType} Product ${index + 1}`, null, null);
            }
        });
    }
    
    // Call the function to set placeholder images
    setPlaceholderImages();
});
