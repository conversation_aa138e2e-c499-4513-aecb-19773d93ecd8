// Wishlist Page Scripts

document.addEventListener('DOMContentLoaded', async function() {
    console.log('Wishlist page loaded');

    // Check if user is logged in - check both formats for compatibility
    const user = JSON.parse(localStorage.getItem('user')) || {};
    const isLoggedIn = user.isLoggedIn || localStorage.getItem('isLoggedIn') === 'true';

    console.log('Login status:', isLoggedIn);

    // Get elements
    const notLoggedInSection = document.getElementById('not-logged-in');
    const wishlistContent = document.getElementById('wishlist-content');
    const emptyWishlist = document.getElementById('empty-wishlist');
    const wishlistItems = document.getElementById('wishlist-items');
    const wishlistGrid = document.getElementById('wishlist-grid');
    const clearWishlistBtn = document.getElementById('clear-wishlist');
    const addAllToCartBtn = document.getElementById('add-all-to-cart');

    // Update cart count
    updateCartCount();

    // Update UI based on login status
    if (isLoggedIn) {
        console.log('User is logged in, showing wishlist content');

        // User is logged in
        if (notLoggedInSection) notLoggedInSection.style.display = 'none';
        if (wishlistContent) wishlistContent.style.display = 'block';

        try {
            // Try to get wishlist from API
            const response = await ApiService.wishlist.getWishlist();
            let wishlist = response.data;
            console.log('Wishlist items from API:', wishlist);

            // If API fails, fall back to localStorage
            if (!wishlist || wishlist.length === 0) {
                // Get wishlist from localStorage as fallback
                wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
                console.log('Wishlist items from localStorage:', wishlist);
            }

            // Check if wishlist is empty
            if (wishlist.length === 0) {
                console.log('Wishlist is empty');
                if (emptyWishlist) emptyWishlist.style.display = 'flex';
                if (wishlistItems) wishlistItems.style.display = 'none';
            } else {
                console.log('Wishlist has items, rendering...');
                if (emptyWishlist) emptyWishlist.style.display = 'none';
                if (wishlistItems) wishlistItems.style.display = 'block';

                // Render wishlist items
                renderWishlistItems(wishlist);
            }
        } catch (error) {
            console.error('Error fetching wishlist from API:', error);

            // Fallback to localStorage
            let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
            console.log('Fallback: Wishlist items from localStorage:', wishlist);

            // Check if wishlist is empty
            if (wishlist.length === 0) {
                console.log('Wishlist is empty');
                if (emptyWishlist) emptyWishlist.style.display = 'flex';
                if (wishlistItems) wishlistItems.style.display = 'none';
            } else {
                console.log('Wishlist has items, rendering...');
                if (emptyWishlist) emptyWishlist.style.display = 'none';
                if (wishlistItems) wishlistItems.style.display = 'block';

                // Render wishlist items
                renderWishlistItems(wishlist);
            }
        }

        // Handle clear wishlist button
        if (clearWishlistBtn) {
            clearWishlistBtn.addEventListener('click', async function() {
                if (confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi danh sách yêu thích?')) {
                    try {
                        // Try to clear wishlist via API
                        await ApiService.wishlist.clearWishlist();

                        // Show notification
                        showNotification('Đã xóa tất cả sản phẩm khỏi danh sách yêu thích', 'success');
                    } catch (error) {
                        console.error('Error clearing wishlist via API:', error);

                        // Fallback to localStorage
                        localStorage.setItem('wishlist', JSON.stringify([]));

                        // Show notification
                        showNotification('Đã xóa tất cả sản phẩm khỏi danh sách yêu thích (offline mode)', 'success');
                    }

                    // Update UI
                    if (emptyWishlist) emptyWishlist.style.display = 'flex';
                    if (wishlistItems) wishlistItems.style.display = 'none';

                    // Clear grid
                    if (wishlistGrid) wishlistGrid.innerHTML = '';
                }
            });
        }

        // Handle add all to cart button
        if (addAllToCartBtn) {
            addAllToCartBtn.addEventListener('click', async function() {
                try {
                    // Get wishlist from API
                    const wishlistResponse = await ApiService.wishlist.getWishlist();
                    let wishlist = wishlistResponse.data;

                    // If API fails, fall back to localStorage
                    if (!wishlist || wishlist.length === 0) {
                        wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
                    }

                    if (wishlist.length === 0) {
                        showNotification('Danh sách yêu thích trống', 'warning');
                        return;
                    }

                    // Add all wishlist items to cart via API
                    for (const item of wishlist) {
                        const productId = item.product ? item.product.id : item.id;

                        try {
                            await ApiService.cart.addItem({
                                productId: productId,
                                quantity: 1
                            });
                        } catch (error) {
                            console.error('Error adding item to cart via API:', error);

                            // Fallback to localStorage
                            let cart = JSON.parse(localStorage.getItem('cart')) || [];

                            // Create cart item from wishlist item
                            const cartItem = {
                                id: item.id || item.product?.id || generateProductId(),
                                name: item.name || item.title || item.product?.name || 'Sản phẩm',
                                price: item.price || item.product?.price || '0đ',
                                image: item.image || item.product?.image || '../img/placeholder.jpg',
                                image1: item.image || item.product?.image || '../img/placeholder.jpg',
                                quantity: 1
                            };

                            // Add to cart
                            cart.push(cartItem);

                            // Save cart to localStorage
                            localStorage.setItem('cart', JSON.stringify(cart));
                        }
                    }

                    // Update cart count
                    updateCartCount();

                    // Show notification
                    showNotification('Đã thêm tất cả sản phẩm vào giỏ hàng', 'success');
                } catch (error) {
                    console.error('Error getting wishlist or adding to cart:', error);

                    // Fallback to localStorage
                    let cart = JSON.parse(localStorage.getItem('cart')) || [];
                    let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

                    if (wishlist.length === 0) {
                        showNotification('Danh sách yêu thích trống', 'warning');
                        return;
                    }

                    // Add all wishlist items to cart
                    wishlist.forEach(item => {
                        // Create cart item from wishlist item
                        const cartItem = {
                            id: item.id || generateProductId(),
                            name: item.name || item.title || 'Sản phẩm',
                            price: item.price || '0đ',
                            image: item.image || '../img/placeholder.jpg',
                            image1: item.image || '../img/placeholder.jpg',
                            quantity: 1
                        };

                        // Add to cart
                        cart.push(cartItem);
                    });

                    // Save cart to localStorage
                    localStorage.setItem('cart', JSON.stringify(cart));

                    // Update cart count
                    updateCartCount();

                    // Show notification
                    showNotification('Đã thêm tất cả sản phẩm vào giỏ hàng (offline mode)', 'success');
                }
            });
        }
    } else {
        console.log('User is not logged in, showing login message');
        // User is not logged in
        if (notLoggedInSection) notLoggedInSection.style.display = 'flex';
        if (wishlistContent) wishlistContent.style.display = 'none';
    }

    // Generate random ID for products
    function generateProductId() {
        return Date.now() + Math.random().toString(36).substring(2, 7);
    }
});

// Render wishlist items
function renderWishlistItems(wishlist) {
    const wishlistGrid = document.getElementById('wishlist-grid');
    if (!wishlistGrid) {
        console.error('Wishlist grid element not found');
        return;
    }

    console.log('Rendering wishlist items:', wishlist);

    // Clear existing items
    wishlistGrid.innerHTML = '';

    // Remove example item if it exists
    const exampleItem = wishlistGrid.querySelector('.wishlist-item');
    if (exampleItem) {
        exampleItem.remove();
    }

    // Add items to grid
    wishlist.forEach((item, index) => {
        const wishlistItem = document.createElement('div');
        wishlistItem.className = 'wishlist-item';
        wishlistItem.dataset.index = index;

        // Handle API vs localStorage format differences
        const product = item.product || item;

        // Handle missing properties
        const itemName = product.name || item.name || item.title || 'Sản phẩm';
        let itemImage = product.image || item.image || '../img/placeholder.jpg';
        const itemPrice = product.price || item.price || 'Liên hệ';
        const itemId = item.id || product.id || `wishlist-${index}`;

        // Fix relative paths if needed
        if (itemImage.startsWith('./')) {
            itemImage = itemImage.replace('./', '../');
        }

        // Create item HTML
        wishlistItem.innerHTML = `
            <div class="wishlist-item-image">
                <img src="${itemImage}" alt="${itemName}" onerror="this.src='../img/placeholder.jpg'">
                <div class="wishlist-item-actions">
                    <button class="btn-add-to-cart" data-index="${index}" data-id="${itemId}"><i class="fas fa-shopping-bag"></i> Thêm vào giỏ</button>
                    <button class="btn-remove" data-index="${index}" data-id="${itemId}"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="wishlist-item-info">
                <h3 class="wishlist-item-name">${itemName}</h3>
                <p class="wishlist-item-price">${itemPrice}</p>
                <div class="wishlist-item-colors">
                    ${renderColorOptions(product.availableColors || item.availableColors, product.color || item.color)}
                </div>
            </div>
        `;

        // Add item to grid
        wishlistGrid.appendChild(wishlistItem);

        console.log(`Added item ${index} to grid:`, itemName);
    });

    // Add event listeners to buttons
    addEventListenersToButtons();
}

// Render color options
function renderColorOptions(availableColors, selectedColor) {
    if (!availableColors || !Array.isArray(availableColors) || availableColors.length === 0) {
        return '';
    }

    return availableColors.map(color => {
        const isActive = color === selectedColor ? 'active' : '';
        return `<span class="color-option ${color} ${isActive}" data-color="${color}"></span>`;
    }).join('');
}

// Add event listeners to buttons
function addEventListenersToButtons() {
    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.btn-add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', async function() {
            const index = this.dataset.index;
            const id = this.dataset.id;

            try {
                // Try to add to cart via API
                if (id) {
                    await ApiService.cart.addItem({
                        productId: id,
                        quantity: 1
                    });
                    showNotification('Đã thêm sản phẩm vào giỏ hàng', 'success');
                    updateCartCount();
                } else {
                    // Fallback to localStorage
                    addToCart(index);
                }
            } catch (error) {
                console.error('Error adding to cart via API:', error);
                // Fallback to localStorage
                addToCart(index);
            }
        });
    });

    // Remove buttons
    const removeButtons = document.querySelectorAll('.btn-remove');
    removeButtons.forEach(button => {
        button.addEventListener('click', async function() {
            const index = this.dataset.index;
            const id = this.dataset.id;

            try {
                // Try to remove from wishlist via API
                if (id) {
                    await ApiService.wishlist.removeItem(id);
                    showNotification('Đã xóa sản phẩm khỏi danh sách yêu thích', 'success');

                    // Refresh wishlist
                    const response = await ApiService.wishlist.getWishlist();
                    let wishlist = response.data;

                    // Update UI
                    if (wishlist.length === 0) {
                        const emptyWishlist = document.getElementById('empty-wishlist');
                        const wishlistItems = document.getElementById('wishlist-items');

                        if (emptyWishlist) emptyWishlist.style.display = 'flex';
                        if (wishlistItems) wishlistItems.style.display = 'none';
                    } else {
                        renderWishlistItems(wishlist);
                    }
                } else {
                    // Fallback to localStorage
                    removeFromWishlist(index);
                }
            } catch (error) {
                console.error('Error removing from wishlist via API:', error);
                // Fallback to localStorage
                removeFromWishlist(index);
            }
        });
    });

    // Color options
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            const color = this.dataset.color;
            const itemElement = this.closest('.wishlist-item');
            const index = itemElement.dataset.index;

            // Update color in wishlist
            updateWishlistItemColor(index, color);

            // Update UI
            const colorOptions = itemElement.querySelectorAll('.color-option');
            colorOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Add to cart
function addToCart(index) {
    console.log('Adding item to cart, index:', index);

    // Get wishlist from localStorage
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

    // Get item from wishlist
    const item = wishlist[index];
    if (!item) {
        console.error('Item not found in wishlist');
        showNotification('Không tìm thấy sản phẩm', 'error');
        return;
    }

    console.log('Adding to cart:', item);

    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Create cart item from wishlist item with proper structure
    const cartItem = {
        id: item.id || Date.now() + Math.random().toString(36).substring(2, 7),
        name: item.name || item.title || 'Sản phẩm',
        price: item.price || '0đ',
        image: item.image || '../img/placeholder.jpg',
        image1: item.image || '../img/placeholder.jpg',
        quantity: 1,
        color: item.color || 'default',
        size: item.size || 'M'
    };

    // Fix relative paths if needed
    if (cartItem.image.startsWith('./')) {
        cartItem.image = cartItem.image.replace('./', '../');
    }

    if (cartItem.image1.startsWith('./')) {
        cartItem.image1 = cartItem.image1.replace('./', '../');
    }

    // Add to cart as a new item (don't increase quantity of existing items)
    cart.push(cartItem);

    // Save cart to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart count
    updateCartCount();

    // Show notification
    showNotification(`Đã thêm ${cartItem.name} vào giỏ hàng`, 'success');
}

// Remove from wishlist
function removeFromWishlist(index) {
    console.log('Removing item from wishlist, index:', index);

    // Get wishlist from localStorage
    let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

    // Get item name for notification
    const itemName = wishlist[index] ? (wishlist[index].name || wishlist[index].title || 'Sản phẩm') : 'Sản phẩm';

    // Remove item from wishlist
    wishlist.splice(index, 1);

    console.log('Wishlist after removal:', wishlist);

    // Save wishlist to localStorage
    localStorage.setItem('wishlist', JSON.stringify(wishlist));

    // Update wishlist count
    updateWishlistCount();

    // Update UI
    if (wishlist.length === 0) {
        console.log('Wishlist is now empty, updating UI');
        const emptyWishlist = document.getElementById('empty-wishlist');
        const wishlistItems = document.getElementById('wishlist-items');

        if (emptyWishlist) emptyWishlist.style.display = 'flex';
        if (wishlistItems) wishlistItems.style.display = 'none';
    } else {
        console.log('Wishlist still has items, re-rendering');
        renderWishlistItems(wishlist);
    }

    // Show notification
    showNotification(`Đã xóa ${itemName} khỏi danh sách yêu thích`, 'success');
}

// Update wishlist item color
function updateWishlistItemColor(index, color) {
    // Get wishlist from localStorage
    let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

    // Update item color
    if (wishlist[index]) {
        wishlist[index].color = color;
    }

    // Save wishlist to localStorage
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
}

// Update cart count
function updateCartCount() {
    const cartCountElement = document.querySelector('.cart-count');
    if (!cartCountElement) return;

    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Update cart count
    cartCountElement.textContent = cart.length;
}

// Update wishlist count
function updateWishlistCount() {
    const wishlistCountElement = document.querySelector('.wishlist-count');
    if (!wishlistCountElement) return;

    // Get wishlist from localStorage
    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

    // Update wishlist count
    wishlistCountElement.textContent = wishlist.length;

    console.log('Updated wishlist count:', wishlist.length);
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icon based on notification type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle notification-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle notification-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle notification-icon"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle notification-icon"></i>';
    }

    // Set notification content
    notification.innerHTML = `
        ${icon}
        <div class="notification-message">${message}</div>
    `;

    // Add notification to container
    container.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        // Remove from DOM after animation completes
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
