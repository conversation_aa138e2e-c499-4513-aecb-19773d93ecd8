/* Blog Styles */

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --text-light: #6c757d;
    --border-color: #ddd;
    --bg-light: #f8f9fa;
}

/* Blog Hero Banner */
.blog-hero-banner {
    position: relative;
    height: 500px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    margin-bottom: 60px;
}

.blog-hero-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.blog-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.blog-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.blog-hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Featured Posts Slider */
.featured-posts-section {
    padding: 60px 0;
}

.featured-posts-slider {
    margin-top: 40px;
}

.featured-post-card {
    display: flex;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin: 10px;
}

.featured-post-image {
    width: 50%;
    height: 400px;
}

.featured-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-post-content {
    width: 50%;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.post-meta {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.post-category {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-transform: uppercase;
    margin-right: 15px;
}

.post-date {
    color: var(--text-light);
    font-size: 0.9rem;
}

.post-title {
    font-size: 1.8rem;
    color: var(--text-color);
    margin-bottom: 15px;
    line-height: 1.4;
}

.post-excerpt {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.btn-read-more {
    display: inline-flex;
    align-items: center;
    color: var(--accent-color);
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-read-more i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.btn-read-more:hover {
    color: var(--accent-hover);
}

.btn-read-more:hover i {
    transform: translateX(5px);
}

/* Blog Categories */
.blog-categories-section {
    padding: 60px 0;
}

.blog-categories-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.blog-category-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.blog-category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.category-icon {
    width: 70px;
    height: 70px;
    background-color: var(--bg-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.5rem;
    color: var(--accent-color);
    transition: all 0.3s ease;
}

.blog-category-card:hover .category-icon {
    background-color: var(--accent-color);
    color: white;
}

.blog-category-card h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

.blog-category-card p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.btn-category {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--bg-light);
    color: var(--text-color);
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-category:hover {
    background-color: var(--accent-color);
    color: white;
}

/* Latest Posts */
.latest-posts-section {
    padding: 60px 0;
}

.latest-posts-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.blog-post-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.blog-post-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.post-image {
    position: relative;
    height: 200px;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-image .post-category {
    position: absolute;
    bottom: 15px;
    left: 15px;
    margin: 0;
}

.post-content {
    padding: 20px;
}

.post-content .post-meta {
    justify-content: space-between;
    margin-bottom: 10px;
}

.post-content .post-meta span {
    display: flex;
    align-items: center;
}

.post-content .post-meta i {
    margin-right: 5px;
}

.post-content .post-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.post-content .post-excerpt {
    font-size: 0.9rem;
    margin-bottom: 15px;
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    color: white;
    text-align: center;
    margin-top: 60px;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;
}

.newsletter-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.newsletter-content p {
    font-size: 1rem;
    margin-bottom: 30px;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 5px 0 0 5px;
    font-size: 1rem;
}

.newsletter-form button {
    padding: 0 30px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter-form button:hover {
    background-color: var(--accent-hover);
}

/* Responsive */
@media (max-width: 992px) {
    .blog-categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .latest-posts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .blog-hero-banner {
        height: 400px;
    }
    
    .blog-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .featured-post-card {
        flex-direction: column;
    }
    
    .featured-post-image, .featured-post-content {
        width: 100%;
    }
    
    .featured-post-image {
        height: 250px;
    }
    
    .featured-post-content {
        padding: 30px;
    }
    
    .latest-posts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .blog-categories-grid {
        grid-template-columns: 1fr;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .newsletter-form input {
        border-radius: 5px;
        margin-bottom: 10px;
    }
    
    .newsletter-form button {
        border-radius: 5px;
        padding: 15px;
    }
}
