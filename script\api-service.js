/**
 * API Service for Fashion Store
 * Handles all API requests to the backend
 */

// API base URL
const API_BASE_URL = 'http://localhost:3000/api';

// API Service object
const ApiService = {
  /**
   * Get token from localStorage
   * @returns {string|null} JWT token
   */
  getToken() {
    const user = JSON.parse(localStorage.getItem('user')) || {};
    return user.token || null;
  },

  /**
   * Set auth header for fetch requests
   * @returns {Object} Headers object with Authorization
   */
  getAuthHeaders() {
    const token = this.getToken();
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  },

  /**
   * Handle API response
   * @param {Response} response - Fetch response object
   * @returns {Promise} Promise with response data
   */
  async handleResponse(response) {
    const data = await response.json();

    if (!response.ok) {
      // If response is 401 Unauthorized, clear user data
      if (response.status === 401) {
        localStorage.removeItem('user');
        localStorage.removeItem('isLoggedIn');
        // Redirect to login page if not already there
        if (!window.location.href.includes('login.html')) {
          window.location.href = 'login.html';
        }
      }

      // Throw error with message from API
      const error = (data && data.message) || response.statusText;
      return Promise.reject(error);
    }

    return data;
  },

  /**
   * Products API
   */
  products: {
    /**
     * Get all products
     * @param {Object} params - Query parameters
     * @returns {Promise} Promise with products data
     */
    async getAll(params = {}) {
      // Build query string from params
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      const url = `${API_BASE_URL}/products${queryString ? `?${queryString}` : ''}`;

      try {
        const response = await fetch(url);
        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error fetching products:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Get product by ID
     * @param {string} id - Product ID
     * @returns {Promise} Promise with product data
     */
    async getById(id) {
      try {
        const response = await fetch(`${API_BASE_URL}/products/${id}`);
        return ApiService.handleResponse(response);
      } catch (error) {
        console.error(`Error fetching product ${id}:`, error);
        return Promise.reject(error);
      }
    },

    /**
     * Get related products
     * @param {string} id - Product ID
     * @returns {Promise} Promise with related products data
     */
    async getRelated(id) {
      try {
        const response = await fetch(`${API_BASE_URL}/products/${id}/related`);
        return ApiService.handleResponse(response);
      } catch (error) {
        console.error(`Error fetching related products for ${id}:`, error);
        return Promise.reject(error);
      }
    }
  },

  /**
   * Users API
   */
  users: {
    /**
     * Login user
     * @param {Object} credentials - User credentials
     * @returns {Promise} Promise with user data and token
     */
    async login(credentials) {
      try {
        const response = await fetch(`${API_BASE_URL}/users/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(credentials)
        });

        const data = await ApiService.handleResponse(response);

        // Save user data to localStorage
        if (data.token) {
          localStorage.setItem('user', JSON.stringify({
            ...data.data,
            token: data.token,
            isLoggedIn: true
          }));
          localStorage.setItem('isLoggedIn', 'true');
        }

        return data;
      } catch (error) {
        console.error('Error logging in:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Register user
     * @param {Object} userData - User registration data
     * @returns {Promise} Promise with user data and token
     */
    async register(userData) {
      try {
        const response = await fetch(`${API_BASE_URL}/users/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(userData)
        });

        const data = await ApiService.handleResponse(response);

        // Save user data to localStorage
        if (data.token) {
          localStorage.setItem('user', JSON.stringify({
            ...data.data,
            token: data.token,
            isLoggedIn: true
          }));
          localStorage.setItem('isLoggedIn', 'true');
        }

        return data;
      } catch (error) {
        console.error('Error registering:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Get user profile
     * @returns {Promise} Promise with user profile data
     */
    async getProfile() {
      try {
        const response = await fetch(`${API_BASE_URL}/users/profile`, {
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error fetching profile:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Update user profile
     * @param {Object} profileData - User profile data
     * @returns {Promise} Promise with updated user profile data
     */
    async updateProfile(profileData) {
      try {
        const response = await fetch(`${API_BASE_URL}/users/profile`, {
          method: 'PUT',
          headers: ApiService.getAuthHeaders(),
          body: JSON.stringify(profileData)
        });

        const data = await ApiService.handleResponse(response);

        // Update user data in localStorage
        const user = JSON.parse(localStorage.getItem('user')) || {};
        localStorage.setItem('user', JSON.stringify({
          ...user,
          ...data.data
        }));

        return data;
      } catch (error) {
        console.error('Error updating profile:', error);
        return Promise.reject(error);
      }
    }
  },

  /**
   * Cart API
   */
  cart: {
    /**
     * Get cart
     * @returns {Promise} Promise with cart data
     */
    async getCart() {
      try {
        const response = await fetch(`${API_BASE_URL}/cart`, {
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error fetching cart:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Add item to cart
     * @param {Object} item - Cart item data
     * @returns {Promise} Promise with updated cart data
     */
    async addItem(item) {
      try {
        const response = await fetch(`${API_BASE_URL}/cart/add`, {
          method: 'POST',
          headers: ApiService.getAuthHeaders(),
          body: JSON.stringify(item)
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error adding item to cart:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Update cart item
     * @param {string} itemId - Cart item ID
     * @param {Object} updates - Updates to apply
     * @returns {Promise} Promise with updated item data
     */
    async updateItem(itemId, updates) {
      try {
        const response = await fetch(`${API_BASE_URL}/cart/update/${itemId}`, {
          method: 'PUT',
          headers: ApiService.getAuthHeaders(),
          body: JSON.stringify(updates)
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error(`Error updating cart item ${itemId}:`, error);
        return Promise.reject(error);
      }
    },

    /**
     * Remove item from cart
     * @param {string} itemId - Cart item ID
     * @returns {Promise} Promise with updated cart data
     */
    async removeItem(itemId) {
      try {
        const response = await fetch(`${API_BASE_URL}/cart/remove/${itemId}`, {
          method: 'DELETE',
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error(`Error removing cart item ${itemId}:`, error);
        return Promise.reject(error);
      }
    },

    /**
     * Clear cart
     * @returns {Promise} Promise with empty cart data
     */
    async clearCart() {
      try {
        const response = await fetch(`${API_BASE_URL}/cart/clear`, {
          method: 'DELETE',
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error clearing cart:', error);
        return Promise.reject(error);
      }
    }
  },

  /**
   * Wishlist API
   */
  wishlist: {
    /**
     * Get wishlist
     * @returns {Promise} Promise with wishlist data
     */
    async getWishlist() {
      try {
        const response = await fetch(`${API_BASE_URL}/wishlist`, {
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error fetching wishlist:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Add item to wishlist
     * @param {string} productId - Product ID
     * @returns {Promise} Promise with updated wishlist data
     */
    async addItem(productId) {
      try {
        const response = await fetch(`${API_BASE_URL}/wishlist/add`, {
          method: 'POST',
          headers: ApiService.getAuthHeaders(),
          body: JSON.stringify({ productId })
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error adding item to wishlist:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Toggle item in wishlist
     * @param {string} productId - Product ID
     * @returns {Promise} Promise with updated wishlist data
     */
    async toggleItem(productId) {
      try {
        const response = await fetch(`${API_BASE_URL}/wishlist/toggle`, {
          method: 'POST',
          headers: ApiService.getAuthHeaders(),
          body: JSON.stringify({ productId })
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error toggling wishlist item:', error);
        return Promise.reject(error);
      }
    },

    /**
     * Remove item from wishlist
     * @param {string} itemId - Wishlist item ID
     * @returns {Promise} Promise with updated wishlist data
     */
    async removeItem(itemId) {
      try {
        const response = await fetch(`${API_BASE_URL}/wishlist/remove/${itemId}`, {
          method: 'DELETE',
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error(`Error removing wishlist item ${itemId}:`, error);
        return Promise.reject(error);
      }
    },

    /**
     * Clear wishlist
     * @returns {Promise} Promise with empty wishlist data
     */
    async clearWishlist() {
      try {
        const response = await fetch(`${API_BASE_URL}/wishlist/clear`, {
          method: 'DELETE',
          headers: ApiService.getAuthHeaders()
        });

        return ApiService.handleResponse(response);
      } catch (error) {
        console.error('Error clearing wishlist:', error);
        return Promise.reject(error);
      }
    }
  }
};

// Export API Service
window.ApiService = ApiService;
