/* Account Page Styles */

/* General Styles */
:root {
    --primary-color: #ff6b6b;
    --primary-dark: #fa5252;
    --secondary-color: #339af0;
    --text-color: #343a40;
    --text-light: #6c757d;
    --border-color: #e9ecef;
    --bg-light: #f8f9fa;
    --success-color: #40c057;
    --warning-color: #fd7e14;
    --danger-color: #fa5252;
}

/* Account Container */
.account-container {
    padding: 40px 0 80px;
    background-color: var(--bg-light);
    min-height: calc(100vh - 300px);
}

.account-header {
    text-align: center;
    margin-bottom: 40px;
}

.account-header h1 {
    font-size: 2rem;
    color: var(--text-color);
    margin-bottom: 10px;
    position: relative;
    display: inline-block;
}

.account-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
}

.account-header p {
    font-size: 1rem;
    color: var(--text-light);
}

.account-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    position: relative;
}

/* Account Sidebar */
.account-sidebar {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: sticky;
    top: 20px;
    height: fit-content;
}

.account-user {
    padding: 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--primary-color);
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.user-info h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.user-info p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.account-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.account-menu-item {
    border-bottom: 1px solid var(--border-color);
}

.account-menu-item:last-child {
    border-bottom: none;
}

.account-menu-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.account-menu-item a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.account-menu-item:hover a {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.account-menu-item:hover a i {
    color: var(--primary-color);
}

.account-menu-item.active a {
    background-color: var(--bg-light);
    color: var(--primary-color);
    font-weight: 500;
    border-left: 3px solid var(--primary-color);
}

.account-menu-item.active a i {
    color: var(--primary-color);
}

/* Account Main Content */
.account-main {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
}

/* Not Logged In Message */
.not-logged-in {
    text-align: center;
    padding: 50px 20px;
}

.login-message h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 15px;
}

.login-message p {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 30px;
}

.login-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Account Sections */
.account-section {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.account-section h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.account-section > p {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 30px;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-box {
    background-color: var(--bg-light);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.stat-info h3 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 5px;
}

.stat-info p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
}

/* Recent Orders */
.recent-orders {
    background-color: var(--bg-light);
    border-radius: 10px;
    padding: 20px;
}

.recent-orders h3 {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 20px;
}

.empty-orders {
    text-align: center;
    padding: 30px 0;
}

.empty-orders p {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

/* Profile Section */
.profile-content {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 30px;
}

.profile-avatar {
    text-align: center;
}

.avatar-container {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.avatar-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.avatar-overlay i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* Form Styles */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 8px;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="date"],
select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

input:focus,
select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    outline: none;
}

input:disabled {
    background-color: var(--bg-light);
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

/* Button Styles */
.btn {
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #228be6;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(51, 154, 240, 0.3);
}

.btn-outline {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #e03131;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(250, 82, 82, 0.3);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #f76707;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(253, 126, 20, 0.3);
}

/* Responsive */
@media (max-width: 992px) {
    .account-content {
        grid-template-columns: 1fr;
    }
    
    .account-sidebar {
        position: static;
    }
    
    .profile-content {
        grid-template-columns: 1fr;
    }
    
    .avatar-container {
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
}
