/* Contact Page Styles */

.contact-container {
    padding: 60px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 50px;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #343a40;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.page-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #ff6b6b;
}

.page-header p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 40px;
    margin-bottom: 60px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-card {
    display: flex;
    gap: 15px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: #ff6b6b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details {
    flex: 1;
}

.contact-details h3 {
    font-size: 1.1rem;
    color: #343a40;
    margin-bottom: 8px;
}

.contact-details p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

.social-links {
    margin-top: 10px;
}

.social-links h3 {
    font-size: 1.1rem;
    color: #343a40;
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    gap: 10px;
}

.social-icon {
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #343a40;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #ff6b6b;
    color: white;
    transform: translateY(-3px);
}

.contact-form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 40px;
}

.contact-form-container h2 {
    font-size: 1.5rem;
    color: #343a40;
    margin-bottom: 10px;
}

.contact-form-container p {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 25px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 8px;
}

.form-group .required {
    color: #ff6b6b;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #ff6b6b;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.checkbox-group label {
    font-size: 0.9rem;
    color: #495057;
    margin: 0;
}

.checkbox-group label a {
    color: #ff6b6b;
    text-decoration: none;
}

.checkbox-group label a:hover {
    text-decoration: underline;
}

.btn-primary {
    padding: 12px 20px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #fa5252;
}

.store-locations {
    margin-bottom: 60px;
}

.store-locations h2 {
    font-size: 1.8rem;
    color: #343a40;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
}

.store-locations h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #ff6b6b;
}

.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.location-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.location-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.location-image {
    height: 200px;
    overflow: hidden;
}

.location-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.location-card:hover .location-image img {
    transform: scale(1.1);
}

.location-details {
    padding: 20px;
}

.location-details h3 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
}

.location-details p {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.location-details i {
    color: #ff6b6b;
}

.btn-directions {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f8f9fa;
    color: #343a40;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.btn-directions:hover {
    background-color: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

.map-container {
    margin-bottom: 60px;
}

.map-container h2 {
    font-size: 1.8rem;
    color: #343a40;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
}

.map-container h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #ff6b6b;
}

.map {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.faq-section {
    margin-bottom: 60px;
}

.faq-section h2 {
    font-size: 1.8rem;
    color: #343a40;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
}

.faq-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #ff6b6b;
}

.faq-container {
    margin-top: 40px;
}

.faq-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.faq-question {
    padding: 15px 20px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.faq-question h3 {
    font-size: 1rem;
    color: #343a40;
    margin: 0;
}

.faq-toggle {
    color: #6c757d;
    transition: all 0.3s ease;
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 15px 20px;
    max-height: 500px;
}

.faq-item.active .faq-toggle {
    transform: rotate(180deg);
}

/* Responsive */
@media (max-width: 992px) {
    .contact-wrapper {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 20px;
    }
    
    .contact-form-container {
        padding: 30px 20px;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
}
