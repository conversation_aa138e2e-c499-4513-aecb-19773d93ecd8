<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://www.theory.com/on/demandware.static/Sites-theory2_US-Site/-/default/dw580c9d16/images/favicons/favicon2.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" type="text/css" href="./styles/style.css">
    <link rel="stylesheet" type="text/css" href="./styles/header.css">
    <link rel="stylesheet" type="text/css" href="./styles/sections.css">
    <link rel="stylesheet" type="text/css" href="./styles/footer.css">
    <link rel="stylesheet" type="text/css" href="./styles/notification.css">

    <title>Xác nhận đơn hàng | Fashion Store</title>
    <style>
        /* Confirm Order Styles */
        .confirm-container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 15px;
        }

        .confirm-header {
            background-color: #f8f9fa;
            padding: 30px 0;
            margin-bottom: 30px;
            text-align: center;
        }

        .confirm-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }

        .confirm-breadcrumb {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 15px;
            font-size: 14px;
        }

        .confirm-breadcrumb a {
            color: #666;
            text-decoration: none;
        }

        .confirm-breadcrumb a:hover {
            color: #ff6b6b;
        }

        .confirm-breadcrumb .separator {
            margin: 0 10px;
            color: #ccc;
        }

        .confirm-breadcrumb .current {
            color: #ff6b6b;
            font-weight: 500;
        }

        .confirm-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .confirm-section {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 25px;
            margin-bottom: 20px;
        }

        .confirm-section h2 {
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            color: #333;
        }

        .customer-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .customer-info-item {
            margin-bottom: 10px;
        }

        .customer-info-label {
            font-weight: 500;
            color: #666;
            margin-bottom: 5px;
            display: block;
        }

        .customer-info-value {
            color: #333;
        }

        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .order-table th {
            background-color: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #eee;
        }

        .order-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .order-table tr:last-child td {
            border-bottom: none;
        }

        .product-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-details {
            flex: 1;
        }

        .product-name {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }

        .product-variant {
            font-size: 12px;
            color: #666;
        }

        .order-summary {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .order-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .order-row.total {
            font-size: 18px;
            font-weight: 600;
            color: #ff6b6b;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .payment-method {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .payment-icon {
            font-size: 20px;
            color: #666;
        }

        .payment-name {
            font-weight: 500;
            color: #333;
        }

        .btn-place-order {
            display: block;
            width: 100%;
            max-width: 300px;
            margin: 30px auto;
            padding: 15px;
            background-color: #ff6b6b;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
            text-align: center;
        }

        .btn-place-order:hover {
            background-color: #ff5252;
        }

        .order-note {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }

        .order-note p {
            margin-bottom: 10px;
        }

        .order-note p:last-child {
            margin-bottom: 0;
        }

        .order-note i {
            margin-right: 5px;
            color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .customer-info {
                grid-template-columns: 1fr;
            }

            .product-info {
                flex-direction: column;
                align-items: flex-start;
            }

            .product-image {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Confirm Header -->
    <div class="confirm-header">
        <div class="container">
            <h1>Xác nhận đơn hàng</h1>
            <p>Vui lòng kiểm tra lại thông tin đơn hàng của bạn</p>

            <div class="confirm-breadcrumb">
                <a href="index.html">Trang chủ</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <a href="AddCart.html">Giỏ hàng</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <a href="checkout.html">Thanh toán</a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <span class="current">Xác nhận</span>
            </div>
        </div>
    </div>

    <!-- Main Confirm Content -->
    <div class="confirm-container">
        <div class="confirm-content">
            <!-- Customer Information -->
            <div class="confirm-section">
                <h2>Thông tin giao hàng</h2>
                <div class="customer-info" id="customer-info">
                    <!-- Customer info will be added dynamically with JavaScript -->
                </div>
            </div>

            <!-- Order Information -->
            <div class="confirm-section">
                <h2>Thông tin đơn hàng</h2>
                <table class="order-table">
                    <thead>
                        <tr>
                            <th>Sản phẩm</th>
                            <th>Đơn giá</th>
                            <th>Số lượng</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody id="order-items">
                        <!-- Order items will be added dynamically with JavaScript -->
                    </tbody>
                </table>

                <div class="order-summary">
                    <div class="order-row">
                        <span>Tạm tính:</span>
                        <span id="subtotal"></span>
                    </div>

                    <div class="order-row">
                        <span>Phí vận chuyển:</span>
                        <span>Miễn phí</span>
                    </div>

                    <div class="order-row">
                        <span>Giảm giá:</span>
                        <span id="discount">0đ</span>
                    </div>

                    <div class="order-row total">
                        <span>Tổng cộng:</span>
                        <span id="total"></span>
                    </div>
                </div>
            </div>

            <!-- Payment Method -->
            <div class="confirm-section">
                <h2>Phương thức thanh toán</h2>
                <div class="payment-method" id="payment-method">
                    <!-- Payment method will be added dynamically with JavaScript -->
                </div>
            </div>

            <!-- Order Note -->
            <div class="order-note">
                <p><i class="fas fa-info-circle"></i> Vui lòng kiểm tra kỹ thông tin đơn hàng trước khi xác nhận.</p>
                <p><i class="fas fa-truck"></i> Đơn hàng sẽ được giao trong vòng 3-5 ngày làm việc.</p>
                <p><i class="fas fa-phone"></i> Chúng tôi sẽ liên hệ với bạn qua số điện thoại để xác nhận đơn hàng.</p>
            </div>

            <button id="place-order" class="btn-place-order">
                <i class="fas fa-check-circle"></i> Xác nhận đặt hàng
            </button>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="./script/main.js"></script>
    <script src="./script/notification.js"></script>

    <script type="module">
        import navbar from "./components/navbar.js"
        import footer from "./components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();
    </script>

    <script>
        // Kiểm tra đăng nhập
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.isLoggedIn) {
            // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
            localStorage.setItem('redirectAfterLogin', window.location.href);

            // Chuyển hướng đến trang đăng nhập
            window.location.href = './pages/login.html';
        }

        // Lấy thông tin đơn hàng từ localStorage
        let orderInfo = JSON.parse(localStorage.getItem("order_info"));

        // Lấy thông tin giỏ hàng
        const cart = JSON.parse(localStorage.getItem("cart")) || [];

        // Kiểm tra nếu không có sản phẩm trong giỏ hàng thì chuyển về trang giỏ hàng
        if (!cart || cart.length === 0) {
            console.error("Giỏ hàng trống, chuyển hướng đến AddCart.html");
            window.location.href = "AddCart.html";
            return;
        }

        // Nếu không có thông tin đơn hàng, tạo thông tin đơn hàng từ giỏ hàng
        if (!orderInfo || !orderInfo.items || orderInfo.items.length === 0) {
            console.warn("Không tìm thấy thông tin đơn hàng, tạo thông tin đơn hàng từ giỏ hàng");

            // Lấy thông tin từ localStorage
            const cartTotal = parseInt(localStorage.getItem('cart_total') || '0');
            const cartDiscount = parseInt(localStorage.getItem('cart_discount') || '0');
            const cartFinal = parseInt(localStorage.getItem('cart_final') || cartTotal.toString());

            // Tạo thông tin đơn hàng mới
            orderInfo = {
                customer: {
                    fullname: "Khách hàng",
                    email: "<EMAIL>",
                    phone: "0123456789",
                    address: "Địa chỉ mặc định",
                    city: "Hà Nội",
                    district: "Cầu Giấy",
                    note: ""
                },
                payment: "cod",
                items: cart,
                subtotal: cartTotal,
                discount: cartDiscount,
                total: cartFinal,
                orderDate: new Date().toISOString(),
                orderNumber: 'ORD' + Date.now()
            };

            // Lưu thông tin đơn hàng vào localStorage
            localStorage.setItem('order_info', JSON.stringify(orderInfo));
            console.log("Đã tạo thông tin đơn hàng mới:", orderInfo);
        }

        // Hàm định dạng tiền tệ
        function formatCurrency(value) {
            // Đảm bảo value là số
            let numValue = 0;

            if (typeof value === 'string') {
                // Loại bỏ tất cả các ký tự không phải số
                numValue = parseInt(value.replace(/[^\d]/g, '')) || 0;
            } else if (typeof value === 'number') {
                numValue = value;
            }

            return numValue.toLocaleString("vi-VN");
        }

        // Hiển thị thông tin khách hàng
        function renderCustomerInfo() {
            const container = document.getElementById("customer-info");
            if (!container) {
                console.error("Không tìm thấy phần tử customer-info");
                return;
            }

            const customer = orderInfo.customer || {};

            // Kiểm tra xem có thông tin khách hàng không
            if (!customer.fullname) {
                console.warn("Thông tin khách hàng không đầy đủ:", customer);
            }

            container.innerHTML = `
                <div class="customer-info-item">
                    <span class="customer-info-label">Họ và tên:</span>
                    <span class="customer-info-value">${customer.fullname || "Chưa cung cấp"}</span>
                </div>
                <div class="customer-info-item">
                    <span class="customer-info-label">Email:</span>
                    <span class="customer-info-value">${customer.email || "Chưa cung cấp"}</span>
                </div>
                <div class="customer-info-item">
                    <span class="customer-info-label">Số điện thoại:</span>
                    <span class="customer-info-value">${customer.phone || "Chưa cung cấp"}</span>
                </div>
                <div class="customer-info-item">
                    <span class="customer-info-label">Địa chỉ:</span>
                    <span class="customer-info-value">${customer.address || "Chưa cung cấp"}</span>
                </div>
                <div class="customer-info-item">
                    <span class="customer-info-label">Tỉnh/Thành phố:</span>
                    <span class="customer-info-value">${customer.city || "Chưa cung cấp"}</span>
                </div>
                <div class="customer-info-item">
                    <span class="customer-info-label">Quận/Huyện:</span>
                    <span class="customer-info-value">${customer.district || "Chưa cung cấp"}</span>
                </div>
                <div class="customer-info-item">
                    <span class="customer-info-label">Ghi chú:</span>
                    <span class="customer-info-value">${customer.note || "Không có"}</span>
                </div>
            `;

            console.log("Đã hiển thị thông tin khách hàng:", customer);
        }

        // Hiển thị thông tin đơn hàng
        function renderOrderItems() {
            const tbody = document.getElementById("order-items");
            if (!tbody) {
                console.error("Không tìm thấy phần tử order-items");
                return;
            }

            const items = orderInfo.items || [];

            if (items.length === 0) {
                console.warn("Không có sản phẩm trong đơn hàng");
                return;
            }

            // Xóa các hàng cũ
            tbody.innerHTML = "";

            items.forEach((item, index) => {
                // Xử lý giá tiền an toàn
                let price = 0;
                if (typeof item.price === 'string') {
                    // Loại bỏ tất cả các ký tự không phải số
                    price = parseInt(item.price.replace(/[^\d]/g, "")) || 0;
                } else if (typeof item.price === 'number') {
                    price = item.price;
                }

                const quantity = item.quantity || 1;
                const subTotal = price * quantity;

                const tr = document.createElement("tr");
                tr.innerHTML = `
                    <td>
                        <div class="product-info">
                            <div class="product-image">
                                <img src="${item.image || '../img/placeholder.jpg'}" alt="${item.name || 'Sản phẩm'}" onerror="this.src='../img/placeholder.jpg'">
                            </div>
                            <div class="product-details">
                                <div class="product-name">${item.name || 'Sản phẩm không xác định'}</div>
                                <div class="product-variant">${item.size ? 'Size: ' + item.size : ''} ${item.color ? '| Màu: ' + item.color : ''}</div>
                            </div>
                        </div>
                    </td>
                    <td>${formatCurrency(price)}đ</td>
                    <td>${quantity}</td>
                    <td>${formatCurrency(subTotal)}đ</td>
                `;

                tbody.appendChild(tr);
            });

            console.log("Đã hiển thị thông tin đơn hàng:", items);

            // Hiển thị tổng tiền
            // Lấy giá trị từ localStorage để đảm bảo tính nhất quán
            // Chuyển đổi thành số ngay khi lấy từ localStorage
            const cartTotal = parseInt(localStorage.getItem('cart_total') || '0');
            const cartDiscount = parseInt(localStorage.getItem('cart_discount') || '0');
            const cartFinal = parseInt(localStorage.getItem('cart_final') || cartTotal.toString());

            console.log('Raw values from localStorage:');
            console.log('- cart_total:', localStorage.getItem('cart_total'));
            console.log('- cart_discount:', localStorage.getItem('cart_discount'));
            console.log('- cart_final:', localStorage.getItem('cart_final'));

            // Hiển thị giá trị
            document.getElementById("subtotal").textContent = formatCurrency(cartTotal) + "đ";
            document.getElementById("discount").textContent = parseInt(cartDiscount) > 0 ? "-" + formatCurrency(cartDiscount) + "đ" : "0đ";
            document.getElementById("total").textContent = formatCurrency(cartFinal) + "đ";

            // Cập nhật lại giá trị trong orderInfo để đảm bảo tính nhất quán
            orderInfo.subtotal = parseInt(cartTotal);
            orderInfo.discount = parseInt(cartDiscount);
            orderInfo.total = parseInt(cartFinal);

            // Log để debug
            console.log('Loaded order summary from localStorage:');
            console.log('- Subtotal:', formatCurrency(cartTotal) + 'đ');
            console.log('- Discount:', formatCurrency(cartDiscount) + 'đ');
            console.log('- Final Total:', formatCurrency(cartFinal) + 'đ');
        }

        // Hiển thị phương thức thanh toán
        function renderPaymentMethod() {
            const container = document.getElementById("payment-method");
            if (!container) {
                console.error("Không tìm thấy phần tử payment-method");
                return;
            }

            const payment = orderInfo.payment || "cod";

            let icon = "";
            let name = "";

            switch (payment) {
                case "cod":
                    icon = '<i class="fas fa-money-bill-wave payment-icon"></i>';
                    name = "Thanh toán khi nhận hàng (COD)";
                    break;
                case "bank-transfer":
                    icon = '<i class="fas fa-university payment-icon"></i>';
                    name = "Chuyển khoản ngân hàng";
                    break;
                case "credit-card":
                    icon = '<i class="far fa-credit-card payment-icon"></i>';
                    name = "Thẻ tín dụng/Ghi nợ";
                    break;
                case "momo":
                    icon = '<i class="fas fa-wallet payment-icon"></i>';
                    name = "Ví điện tử MoMo";
                    break;
                default:
                    icon = '<i class="fas fa-money-bill-wave payment-icon"></i>';
                    name = "Thanh toán khi nhận hàng (COD)";
            }

            container.innerHTML = `
                ${icon}
                <span class="payment-name">${name}</span>
            `;

            console.log("Đã hiển thị phương thức thanh toán:", payment);
        }

        // Hàm hiển thị thông báo
        function showNotification(message, type = 'info') {
            // Tạo phần tử thông báo
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                               type === 'error' ? '#F44336' :
                                               type === 'warning' ? '#FF9800' : '#2196F3';
            notification.style.color = '#fff';
            notification.style.padding = '15px 20px';
            notification.style.borderRadius = '4px';
            notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '250px';
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(50px)';
            notification.style.transition = 'opacity 0.3s, transform 0.3s';

            // Icon dựa trên loại thông báo
            let icon = '';
            switch (type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
            }

            // Nội dung thông báo
            notification.innerHTML = `${icon} ${message}`;

            // Thêm vào body
            document.body.appendChild(notification);

            // Hiển thị thông báo với animation
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Xóa thông báo sau 3 giây
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(50px)';

                // Xóa khỏi DOM sau khi animation hoàn tất
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Xử lý nút đặt hàng
        function submitOrder() {
            // Hiển thị thông báo đang xử lý
            showNotification("Đang xử lý đơn hàng của bạn...", "info");

            // Đặt thời gian chờ để mô phỏng quá trình xử lý đơn hàng
            setTimeout(() => {
                // Xóa thông tin giỏ hàng và đơn hàng khỏi localStorage
                localStorage.removeItem("cart");
                localStorage.removeItem("cart_total");
                localStorage.removeItem("cart_discount");
                localStorage.removeItem("cart_final");
                localStorage.removeItem("promo_code");
                localStorage.removeItem("discount_percent");

                // Lưu thông tin đơn hàng vào lịch sử đơn hàng
                const orderHistory = JSON.parse(localStorage.getItem("order_history")) || [];
                orderInfo.status = "Đang xử lý";
                orderInfo.orderDate = new Date().toISOString();
                orderHistory.push(orderInfo);
                localStorage.setItem("order_history", JSON.stringify(orderHistory));

                // Chuyển đến trang đặt hàng thành công
                window.location.href = "success.html";
            }, 1500);
        }

        // Hiển thị thông tin khi trang được tải
        document.addEventListener("DOMContentLoaded", function() {
            renderCustomerInfo();
            renderOrderItems();
            renderPaymentMethod();

            // Thêm event listener cho nút xác nhận đơn hàng
            document.getElementById('place-order').addEventListener('click', function() {
                submitOrder();
            });
        });
    </script>
</body>
</html>
