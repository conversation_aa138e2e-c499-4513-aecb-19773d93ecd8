


function appendData(products, location) {
	location.innerHTML = "";
	products.map((item) => {
		let div = document.createElement("div");
		div.style.cursor = "pointer";

		let imgdiv = document.createElement("div");
		imgdiv.setAttribute("class", "product-img");

		let icon = document.createElement("span");
		icon.className = "heart-icon-container";
		icon.innerHTML = `<i class="fa fa-heart-o heart-icon" style="font-size:20px"></i>`;

		let img = document.createElement("img");
		img.src = item.image1;

		// Product actions (Quick View and Add to Cart)
		let actionsDiv = document.createElement("div");
		actionsDiv.setAttribute("class", "product-actions");

		// Quick View button
		let quickViewBtn = document.createElement("button");
		quickViewBtn.setAttribute("class", "btn-quick-view");
		quickViewBtn.innerText = "Xem nhanh";

		// Add to Cart button
		let addToCartBtn = document.createElement("button");
		addToCartBtn.setAttribute("class", "btn-add-to-cart");
		addToCartBtn.innerText = "Thêm vào giỏ";

		// Add buttons to actions div
		actionsDiv.appendChild(quickViewBtn);
		actionsDiv.appendChild(addToCartBtn);

		// Hover effect - change image only if image2 exists
		imgdiv.addEventListener("mouseover", function () {
			if (item.image2) {
				img.src = item.image2;
			}
		});

		imgdiv.addEventListener("mouseout", function () {
			img.src = item.image1;
		});

		// Mở chi tiết sản phẩm
		img.addEventListener("click", function () {
			localStorage.setItem("product_details", JSON.stringify(item));
			window.location.href = "../pages/productdetail.html";
		});

		// Thêm vào giỏ hàng khi click vào nút Add to Cart
		addToCartBtn.addEventListener("click", function (e) {
			e.stopPropagation(); // Ngăn chặn sự kiện click lan ra ngoài

			// Kiểm tra xem đã chọn size và màu sắc chưa
			const selectedSize = div.dataset.selectedSize;
			const selectedColor = div.dataset.selectedColor;

			if (!selectedSize) {
				showNotification("Vui lòng chọn kích thước", "warning");
				return;
			}

			if (!selectedColor) {
				showNotification("Vui lòng chọn màu sắc", "warning");
				return;
			}

			// Tạo bản sao của sản phẩm với size và màu sắc đã chọn
			const productWithOptions = {
				...item,
				size: selectedSize,
				color: selectedColor
			};

			// Thêm sản phẩm vào giỏ hàng
			addTocart(productWithOptions);
		});

		// Xem nhanh sản phẩm khi click vào nút Quick View
		quickViewBtn.addEventListener("click", function (e) {
			e.stopPropagation(); // Ngăn chặn sự kiện click lan ra ngoài
			localStorage.setItem("product_details", JSON.stringify(item));
			window.location.href = "productdetail.html";
		});

		let detail_div = document.createElement("div");
		detail_div.setAttribute("class", "products-name");

		// Tạo tiêu đề sản phẩm
		let title = document.createElement("p");
		title.innerText = item.title;

		// Tạo giá sản phẩm
		let price = document.createElement("p");
		price.innerText = item.price;

		// Tạo phần chọn màu sắc
		let colorSelector = document.createElement("div");
		colorSelector.className = "product-colors";
		colorSelector.innerHTML = `<p class="option-label">Màu sắc:</p>`;

		// Tạo các tùy chọn màu sắc
		let colorOptions = document.createElement("div");
		colorOptions.className = "color-options";

		// Danh sách màu sắc với hình ảnh tương ứng
		const colors = [
			{ name: "Đen", value: "#000000", image: item.image1 },
			{ name: "Trắng", value: "#FFFFFF", border: true, image: item.image2 || item.image1 },
			{ name: "Xanh dương", value: "#0000FF", image: item.image2 || item.image1 },
			{ name: "Đỏ", value: "#FF0000", image: item.image1 },
			{ name: "Xám", value: "#808080", image: item.image2 || item.image1 },
			{ name: "Nâu", value: "#A52A2A", image: item.image1 }
		];

		// Tạo các tùy chọn màu sắc
		colors.forEach(color => {
			let colorOption = document.createElement("div");
			colorOption.className = "color-option";
			colorOption.setAttribute("data-color", color.name);
			colorOption.setAttribute("title", color.name);
			colorOption.style.backgroundColor = color.value;

			if (color.border) {
				colorOption.style.border = "1px solid #ddd";
			}

			// Thêm sự kiện click cho màu sắc
			colorOption.addEventListener("click", function() {
				// Bỏ chọn tất cả các màu khác
				colorOptions.querySelectorAll(".color-option").forEach(opt => {
					opt.classList.remove("selected");
				});

				// Chọn màu hiện tại
				this.classList.add("selected");

				// Lưu màu đã chọn vào dataset của div cha
				div.dataset.selectedColor = this.getAttribute("data-color");

				// Thay đổi hình ảnh sản phẩm theo màu đã chọn
				const selectedColor = colors.find(c => c.name === this.getAttribute("data-color"));
				if (selectedColor && selectedColor.image) {
					img.src = selectedColor.image;
				}
			});

			colorOptions.appendChild(colorOption);
		});

		colorSelector.appendChild(colorOptions);

		// Tạo phần chọn kích thước
		let sizeSelector = document.createElement("div");
		sizeSelector.className = "product-sizes";
		sizeSelector.innerHTML = `<p class="option-label">Kích thước:</p>`;

		// Tạo các tùy chọn kích thước
		let sizeOptions = document.createElement("div");
		sizeOptions.className = "size-options";

		// Danh sách kích thước
		const sizes = ["S", "M", "L", "XL", "XXL"];

		// Tạo các tùy chọn kích thước
		sizes.forEach(size => {
			let sizeOption = document.createElement("div");
			sizeOption.className = "size-option";
			sizeOption.setAttribute("data-size", size);
			sizeOption.innerText = size;

			// Thêm sự kiện click cho kích thước
			sizeOption.addEventListener("click", function() {
				// Bỏ chọn tất cả các kích thước khác
				sizeOptions.querySelectorAll(".size-option").forEach(opt => {
					opt.classList.remove("selected");
				});

				// Chọn kích thước hiện tại
				this.classList.add("selected");

				// Lưu kích thước đã chọn vào dataset của div cha
				div.dataset.selectedSize = this.getAttribute("data-size");
			});

			sizeOptions.appendChild(sizeOption);
		});

		sizeSelector.appendChild(sizeOptions);

		imgdiv.append(img, actionsDiv, icon);
		detail_div.append(title, price, colorSelector, sizeSelector);
		div.append(imgdiv, detail_div);
		location.append(div);
	});
}

function addTocart(product) {
	let cart = JSON.parse(localStorage.getItem("cart")) || [];

	// Kiểm tra xem sản phẩm đã tồn tại trong giỏ hàng chưa (cùng tên, size và màu sắc)
	const existingProductIndex = cart.findIndex(item =>
		item.title === product.title &&
		item.size === product.size &&
		item.color === product.color
	);

	if (existingProductIndex !== -1) {
		// Nếu sản phẩm đã tồn tại, hiển thị thông báo
		showNotification(`Sản phẩm với size ${product.size} và màu ${product.color} đã có trong giỏ hàng`, "info");
		return;
	}

	// Tạo ID duy nhất cho sản phẩm mới
	const uniqueId = Date.now() + Math.random().toString(36).substring(2, 7);

	// Xác định hình ảnh dựa trên màu sắc đã chọn
	let selectedImage = product.image1;

	// Nếu màu là Trắng, Xanh dương hoặc Xám, sử dụng image2 nếu có
	if (["Trắng", "Xanh dương", "Xám"].includes(product.color) && product.image2) {
		selectedImage = product.image2;
	}

	// Tạo sản phẩm mới để thêm vào giỏ hàng
	let newProduct = {
		...product,
		id: uniqueId,  // Thêm ID duy nhất
		name: product.title,       // cần cho AddCart.html
		price: product.price,
		image: selectedImage, // Sử dụng hình ảnh tương ứng với màu sắc đã chọn
		quantity: 1,
		addedAt: new Date().toISOString() // Thêm thời gian thêm vào giỏ hàng
	};

	// Thêm sản phẩm mới vào giỏ hàng
	cart.push(newProduct);
	localStorage.setItem("cart", JSON.stringify(cart));

	// Cập nhật số lượng sản phẩm trong giỏ hàng
	let countBag = document.querySelector(".cart-count");
	if (countBag) {
		countBag.textContent = cart.length;
	}

	// Hiển thị thông báo thành công với thông tin size và màu sắc
	showNotification(`Đã thêm sản phẩm size ${product.size}, màu ${product.color} vào giỏ hàng`, "success");
}

// Hàm hiển thị thông báo
function showNotification(message, type = 'info') {
    // Tạo container nếu chưa tồn tại
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }

    // Tạo thông báo
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                       type === 'error' ? '#F44336' :
                                       type === 'warning' ? '#FF9800' : '#2196F3';
    notification.style.color = '#fff';
    notification.style.padding = '15px 20px';
    notification.style.marginBottom = '10px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(50px)';
    notification.style.transition = 'opacity 0.3s, transform 0.3s';

    // Icon dựa trên loại thông báo
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px;"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle" style="margin-right: 10px;"></i>';
    }

    // Nội dung thông báo
    notification.innerHTML = `${icon} ${message}`;

    // Thêm vào container
    container.appendChild(notification);

    // Hiển thị thông báo với animation
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);

    // Xóa thông báo sau 3 giây
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(50px)';

        // Xóa khỏi DOM sau khi animation hoàn tất
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Cập nhật số lượng yêu thích
function updateWishlistCount() {
    let wishlist = JSON.parse(localStorage.getItem("wishlist")) || [];
    let wishlistCount = document.querySelector(".wishlist-count");
    if (wishlistCount) {
        wishlistCount.textContent = wishlist.length;
        wishlistCount.style.display = wishlist.length > 0 ? "flex" : "none";
    }
}

// Cập nhật số lượng giỏ hàng
function updateCartCount() {
    let cart = JSON.parse(localStorage.getItem("cart")) || [];
    let cartCount = document.querySelector(".cart-count");
    if (cartCount) {
        cartCount.textContent = cart.length;
    }
}

// Cập nhật số lượng khi trang được tải
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();
    updateWishlistCount();
});

export default appendData;