/* Account Page Styles */
@import url('style.css');

:root {
    --primary-color: #2c3e50;
    --primary-dark: #1a2530;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --text-light: #6c757d;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --bg-light: #f8f9fa;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --background-color: #f8f9fa;
}

/* Account Container */
.account-container {
    padding: 3rem 0;
    background-color: var(--background-color);
}

.account-header {
    text-align: center;
    margin-bottom: 2rem;
}

.account-header h1 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.account-header p {
    color: var(--text-color);
    font-size: 1rem;
}

/* Account Content */
.account-content {
    display: flex;
    gap: 2rem;
    background-color: #fff;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* Account Sidebar */
.account-sidebar {
    flex: 0 0 280px;
    background-color: var(--secondary-color);
    padding: 2rem 0;
}

.account-user {
    display: flex;
    align-items: center;
    padding: 0 1.5rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-right: 1rem;
}

.user-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
    color: var(--primary-color);
}

.user-info p {
    font-size: 0.85rem;
    color: var(--text-color);
}

.account-menu {
    list-style: none;
}

.account-menu-item {
    margin-bottom: 0.5rem;
}

.account-menu-item a {
    display: flex;
    align-items: center;
    padding: 0.8rem 1.5rem;
    color: var(--text-color);
    transition: all var(--transition-fast);
    text-decoration: none;
}

.account-menu-item a i {
    margin-right: 0.8rem;
    width: 20px;
    text-align: center;
}

.account-menu-item:hover a {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--accent-color);
}

.account-menu-item.active a {
    background-color: var(--primary-color);
    color: #fff;
}

#logout-btn {
    margin-top: 2rem;
}

#logout-btn a {
    color: var(--accent-color);
}

#logout-btn:hover a {
    background-color: var(--accent-color);
    color: #fff;
}

/* Account Main Content */
.account-main {
    flex: 1;
    padding: 2rem;
}

/* Not Logged In Message */
.not-logged-in {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
}

.login-message {
    text-align: center;
    max-width: 500px;
}

.login-message h2 {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.login-message p {
    color: var(--text-color);
    margin-bottom: 2rem;
}

.login-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Dashboard Section */
.account-section h2 {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.account-section > p {
    color: var(--text-color);
    margin-bottom: 2rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.stat-box {
    background-color: var(--secondary-color);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.stat-info h3 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 0.3rem;
}

.stat-info p {
    font-size: 0.9rem;
    color: var(--text-color);
}

.recent-orders h3 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.empty-orders {
    text-align: center;
    padding: 3rem 0;
}

.empty-orders p {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

/* Auth Container (Login/Register) */
.auth-container {
    padding: 3rem 0;
    background-color: var(--background-color);
}

.auth-wrapper {
    display: flex;
    background-color: #fff;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.auth-form-container {
    flex: 1;
    padding: 3rem;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h1 {
    font-family: var(--heading-font);
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--text-color);
}

.auth-form {
    margin-bottom: 2rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.form-group input {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
}

.form-group input:focus {
    border-color: var(--accent-color);
    outline: none;
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-meter {
    height: 4px;
    background-color: #eee;
    border-radius: 2px;
    margin-bottom: 0.3rem;
}

.strength-meter-fill {
    height: 100%;
    border-radius: 2px;
    transition: width var(--transition-fast);
}

.strength-meter-fill[data-strength="0"] {
    width: 20%;
    background-color: #ff4d4d;
}

.strength-meter-fill[data-strength="1"] {
    width: 40%;
    background-color: #ffa64d;
}

.strength-meter-fill[data-strength="2"] {
    width: 60%;
    background-color: #ffff4d;
}

.strength-meter-fill[data-strength="3"] {
    width: 80%;
    background-color: #4dff4d;
}

.strength-meter-fill[data-strength="4"] {
    width: 100%;
    background-color: #4d4dff;
}

.strength-text {
    font-size: 0.8rem;
    color: #999;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 0.5rem;
}

.forgot-password {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-block {
    width: 100%;
}

.social-login {
    text-align: center;
    margin-bottom: 2rem;
}

.social-login p {
    position: relative;
    margin-bottom: 1.5rem;
}

.social-login p::before,
.social-login p::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background-color: var(--border-color);
}

.social-login p::before {
    left: 0;
}

.social-login p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: 1rem;
}

.social-btn {
    flex: 1;
    padding: 0.8rem;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.social-btn.facebook {
    background-color: #3b5998;
    color: #fff;
}

.social-btn.google {
    background-color: #db4437;
    color: #fff;
}

.social-btn:hover {
    opacity: 0.9;
}

.auth-footer {
    text-align: center;
}

.auth-footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
}

.auth-image {
    flex: 1;
    position: relative;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    text-align: center;
    color: #fff;
}

.auth-image-overlay h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.auth-image-overlay p {
    font-size: 1.1rem;
    max-width: 400px;
}

/* Profile Section */
.profile-content {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.profile-avatar {
    flex: 0 0 200px;
    text-align: center;
}

.avatar-container {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 1rem;
    position: relative;
    border: 3px solid var(--primary-color);
}

.avatar-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    opacity: 0;
    transition: opacity var(--transition-fast);
    cursor: pointer;
}

.avatar-overlay i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.profile-form {
    flex: 1;
}

/* Address Section */
.address-actions {
    margin-bottom: 1.5rem;
}

.address-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.address-item {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    position: relative;
}

.address-item.default {
    border-color: var(--primary-color);
}

.default-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--primary-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
}

.address-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.address-phone {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.address-full {
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.address-actions {
    display: flex;
    gap: 1rem;
}

.empty-address {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 0;
    color: var(--text-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
}

.modal-body {
    padding: 1.5rem;
}

/* Security Section */
.security-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.security-card {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.security-card-header {
    background-color: var(--secondary-color);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.security-card-header h3 {
    margin: 0 0 0.5rem;
    color: var(--primary-color);
}

.security-card-header p {
    margin: 0;
    color: var(--text-color);
    font-size: 0.9rem;
}

.security-card-body {
    padding: 1.5rem;
}

.security-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.security-option:last-child {
    border-bottom: none;
}

.security-option-info {
    flex: 1;
}

.security-option-info h4 {
    margin: 0 0 0.5rem;
    color: var(--primary-color);
}

.security-option-info p {
    margin: 0;
    color: var(--text-color);
    font-size: 0.9rem;
}

.security-option-action {
    margin-left: 1rem;
}

/* Login History Section */
.login-history-filters {
    margin-bottom: 2rem;
}

.login-history-table {
    overflow-x: auto;
}

.login-history-table table {
    width: 100%;
    border-collapse: collapse;
}

.login-history-table th,
.login-history-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.login-history-table th {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    font-weight: 600;
}

.login-history-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.empty-history {
    text-align: center;
    padding: 2rem;
    color: var(--text-color);
}

.status-success {
    color: #28a745;
}

.status-failed {
    color: #dc3545;
}

/* Form Elements */
select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
    background-color: #fff;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23333' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    padding-right: 2.5rem;
}

select:focus {
    border-color: var(--accent-color);
    outline: none;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

/* Button Styles */
.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.btn-danger {
    background-color: #dc3545;
    color: #fff;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

/* Responsive */
@media (max-width: 992px) {
    .account-content {
        flex-direction: column;
    }

    .account-sidebar {
        flex: none;
        width: 100%;
    }

    .auth-wrapper {
        flex-direction: column;
    }

    .auth-image {
        height: 300px;
    }

    .profile-content {
        flex-direction: column;
        align-items: center;
    }

    .profile-avatar {
        margin-bottom: 2rem;
    }

    .security-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
    }

    .address-list {
        grid-template-columns: 1fr;
    }
}
