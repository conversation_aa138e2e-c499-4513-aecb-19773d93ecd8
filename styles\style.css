/* Style chính cho website */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
    /* <PERSON><PERSON>u sắc */
    --primary-color: #2c3e50;
    --secondary-color: #f5f5f5;
    --accent-color: #e74c3c;
    --accent-hover: #c0392b;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #222;
    --border-color: #ddd;
    --hover-color: #f9f9f9;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --body-font: 'Montserrat', sans-serif;
    --heading-font: 'Playfair Display', serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    background-color: #fff;
}

a {
    text-decoration: none;
    color: inherit;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.8rem 1.8rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--light-text);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    color: var(--light-text);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-secondary {
    background-color: transparent;
    color: var(--light-text);
    border: 1px solid var(--light-text);
}

.btn-secondary:hover {
    background-color: var(--light-text);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-dark {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.btn-dark:hover {
    background-color: #1a2530;
    color: var(--light-text);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-shop {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 5px;
    font-weight: 500;
    display: inline-block;
}

.btn-shop::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform var(--transition-medium);
}

.btn-shop:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Section Styles */
.section-title {
    font-family: var(--heading-font);
    font-size: 2.2rem;
    margin-bottom: var(--spacing-xl);
    text-align: center;
    position: relative;
    padding-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
}

section {
    padding: var(--spacing-xxl) 0;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.8s ease forwards;
}

/* Responsive */
@media (max-width: 992px) {
    .section-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 1.6rem;
    }
    
    .btn {
        padding: 0.7rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 1.4rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: var(--spacing-xs);
}

.mb-2 {
    margin-bottom: var(--spacing-sm);
}

.mb-3 {
    margin-bottom: var(--spacing-md);
}

.mb-4 {
    margin-bottom: var(--spacing-lg);
}

.mb-5 {
    margin-bottom: var(--spacing-xl);
}

.mt-1 {
    margin-top: var(--spacing-xs);
}

.mt-2 {
    margin-top: var(--spacing-sm);
}

.mt-3 {
    margin-top: var(--spacing-md);
}

.mt-4 {
    margin-top: var(--spacing-lg);
}

.mt-5 {
    margin-top: var(--spacing-xl);
}

.py-1 {
    padding-top: var(--spacing-xs);
    padding-bottom: var(--spacing-xs);
}

.py-2 {
    padding-top: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
}

.py-3 {
    padding-top: var(--spacing-md);
    padding-bottom: var(--spacing-md);
}

.py-4 {
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
}

.py-5 {
    padding-top: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
}

.px-1 {
    padding-left: var(--spacing-xs);
    padding-right: var(--spacing-xs);
}

.px-2 {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
}

.px-3 {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
}

.px-4 {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
}

.px-5 {
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
}

.d-flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

.gap-1 {
    gap: var(--spacing-xs);
}

.gap-2 {
    gap: var(--spacing-sm);
}

.gap-3 {
    gap: var(--spacing-md);
}

.gap-4 {
    gap: var(--spacing-lg);
}

.gap-5 {
    gap: var(--spacing-xl);
}


.google-map {
    padding: 60px 0;
    background: #fcfcfc;
    text-align: center;
}

.google-map .section-title {
    margin-bottom: 10px;
}

#chat-bot {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 300px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    display: none;
    flex-direction: column;
    z-index: 9999;
    overflow: hidden;
    font-family: 'Arial', sans-serif;
  }
  
  .chat-header {
    background: #111;
    color: #fff;
    padding: 10px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .chat-body {
    padding: 10px;
  }
  
  .chat-body input {
    width: 100%;
    padding: 8px;
    margin-top: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .chat-message {
    margin: 8px 0;
    font-size: 0.9rem;
  }
  
  .chat-message.bot {
    color: #333;
    background: #f2f2f2;
    padding: 6px 10px;
    border-radius: 5px;
  }
  
  #chat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #111;
    color: white;
    border-radius: 50%;
    padding: 12px;
    font-size: 20px;
    cursor: pointer;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  }
  








  .popup {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.65);
    z-index: 9999;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.popup-content {
    background: #fff;
    max-width: 720px;
    width: 95%;
    display: flex;
    flex-wrap: wrap;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
    position: relative;
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(40px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.popup-img-container {
    flex: 1;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.popup-img {
    max-width: 100%;
    height: auto;
}

.popup-right {
    flex: 1;
    padding: 30px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.right-content {
    text-align: center;
}

.right-content h1 {
    font-size: 28px;
    margin-bottom: 15px;
    color: #111;
}

.right-content h1 span {
    color: #e74c3c;
}

.right-content p {
    font-size: 16px;
    color: #555;
    margin-bottom: 25px;
}

.popup-form {
    padding: 12px;
    width: 80%;
    border: 1px solid #ccc;
    border-radius: 25px;
    margin-bottom: 10px;
    font-size: 15px;
    outline: none;
}

.right-content a {
    display: inline-block;
    background-color: #111;
    color: white;
    padding: 10px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.right-content a:hover {
    background-color: #e74c3c;
}

.popup-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 30px;
    cursor: pointer;
    color: #888;
    transition: color 0.2s;
}

.popup-close:hover {
    color: #e74c3c;
}

.hide-popup {
    display: none;
}
