<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="../styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <title>Chính sách đổi trả | Fashion Store</title>

    <style>
        /* Returns Policy Styles */
        .returns-container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .returns-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .returns-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .returns-header h1 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .returns-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .returns-section {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .returns-section h2 {
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .returns-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .returns-section ul {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        .returns-section li {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .returns-steps {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 30px;
        }

        .step-card {
            flex: 1;
            min-width: 200px;
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.03);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background-color: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 15px;
        }

        .step-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .step-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .faq-item {
            margin-bottom: 20px;
        }

        .faq-question {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Pro';
            color: #ff6b6b;
            transition: transform 0.3s;
        }

        .faq-question.active::after {
            transform: rotate(180deg);
        }

        .faq-answer {
            color: #666;
            line-height: 1.6;
            padding-left: 20px;
            border-left: 2px solid #ff6b6b;
            margin-top: 10px;
            display: none;
        }

        .faq-answer.show {
            display: block;
        }

        .contact-box {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-top: 40px;
        }

        .contact-box h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .contact-box p {
            color: #666;
            margin-bottom: 20px;
        }

        .contact-btn {
            display: inline-block;
            background-color: #ff6b6b;
            color: white;
            padding: 12px 25px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .contact-btn:hover {
            background-color: #ff5252;
        }

        @media (max-width: 768px) {
            .returns-steps {
                flex-direction: column;
            }

            .step-card {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Promotion Marquee -->
    <div class="marquee-container">
        <div class="marquee-content">
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
            <div class="marquee-item">
                <i class="fas fa-percent"></i> Giảm thêm 10% khi thanh toán qua ví điện tử
            </div>
            <div class="marquee-item">
                <i class="fas fa-calendar-alt"></i> Flash sale mỗi ngày từ 12h-14h
            </div>
            <div class="marquee-item">
                <i class="fas fa-tags"></i> Giảm giá 50% cho tất cả sản phẩm mùa hè
            </div>
            <div class="marquee-item">
                <i class="fas fa-shipping-fast"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ
            </div>
            <div class="marquee-item">
                <i class="fas fa-gift"></i> Tặng quà cho 100 khách hàng đầu tiên
            </div>
        </div>
    </div>

    <!-- Header Section -->
    <header id="header">
        <!-- Top Announcement Bar -->
        <div class="announcement-bar">
            <p><i class="fas fa-truck"></i> Miễn phí vận chuyển cho đơn hàng trên 500.000đ</p>
        </div>

        <!-- Main Navigation -->
        <nav class="main-nav">
            <div class="container">
                <div class="nav-wrapper">
                    <!-- Mobile Menu Toggle -->
                    <div class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </div>

                    <!-- Logo -->
                    <div class="logo">
                        <a href="../index.html">
                            <img src="../img logo/logo 1.jpg" alt="Fashion Store Logo">
                        </a>
                    </div>

                    <!-- Navigation Menu -->
                    <ul class="nav-menu">
                        <li class="nav-item has-dropdown">
                            <a href="../womenproducts-new.html">Nữ</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="../womenproducts-new.html">Áo sơ mi</a></li>
                                        <li><a href="../womenproducts-new.html">Áo thun</a></li>
                                        <li><a href="../womenproducts-new.html">Quần jean</a></li>
                                        <li><a href="../womenproducts-new.html">Váy & Đầm</a></li>
                                        <li><a href="../womenproducts-new.html">Áo khoác</a></li>
                                        <li><a href="../womenproducts-new.html">Đồ thể thao</a></li>
                                        <li><a href="../womenproducts-new.html">Đồ ngủ</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="../womenproducts-new.html">Mùa hè 2024</a></li>
                                        <li><a href="../womenproducts-new.html">Công sở thanh lịch</a></li>
                                        <li><a href="../womenproducts-new.html">Dạo phố năng động</a></li>
                                        <li><a href="../womenproducts-new.html">Dự tiệc sang trọng</a></li>
                                        <li><a href="../sexy-women.html">Đồ sexy</a></li>
                                        <li><a href="../underwear-women.html">Nội y</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img womens/women 15.webp" alt="Women's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="../womenproducts-new.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="../menproducts-new.html">Nam</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Danh mục</h3>
                                    <ul>
                                        <li><a href="../menproducts-new.html">Áo sơ mi</a></li>
                                        <li><a href="../menproducts-new.html">Áo thun</a></li>
                                        <li><a href="../menproducts-new.html">Quần jean</a></li>
                                        <li><a href="../menproducts-new.html">Quần kaki</a></li>
                                        <li><a href="../menproducts-new.html">Áo khoác</a></li>
                                        <li><a href="../menproducts-new.html">Đồ thể thao</a></li>
                                        <li><a href="../underwear-men.html">Nội y</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bộ sưu tập</h3>
                                    <ul>
                                        <li><a href="../menproducts-new.html">Mùa hè 2024</a></li>
                                        <li><a href="../menproducts-new.html">Công sở lịch lãm</a></li>
                                        <li><a href="../menproducts-new.html">Thể thao năng động</a></li>
                                        <li><a href="../menproducts-new.html">Dạo phố cá tính</a></li>
                                        <li><a href="../menproducts-new.html">Hàng mới về</a></li>
                                        <li><a href="../menproducts-new.html">Bán chạy nhất</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img mens/men 10.jpg" alt="Men's Collection">
                                    <h4>Bộ sưu tập mới</h4>
                                    <a href="../menproducts-new.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item has-dropdown">
                            <a href="./kids.html" class="active">Trẻ em</a>
                            <div class="dropdown-menu">
                                <div class="dropdown-column">
                                    <h3>Bé gái</h3>
                                    <ul>
                                        <li><a href="kids.html?category=girls">Áo</a></li>
                                        <li><a href="kids.html?category=girls">Quần</a></li>
                                        <li><a href="kids.html?category=girls">Váy đầm</a></li>
                                        <li><a href="kids.html?category=girls">Đồ ngủ</a></li>
                                        <li><a href="kids.html?category=girls">Đồ thể thao</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column">
                                    <h3>Bé trai</h3>
                                    <ul>
                                        <li><a href="kids.html?category=boys">Áo</a></li>
                                        <li><a href="kids.html?category=boys">Quần</a></li>
                                        <li><a href="kids.html?category=boys">Đồ ngủ</a></li>
                                        <li><a href="kids.html?category=boys">Đồ thể thao</a></li>
                                        <li><a href="kids.html?category=boys">Bộ quần áo</a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-column dropdown-featured">
                                    <img src="../img womens/women 12.webp" alt="Kids Collection">
                                    <h4>Bộ sưu tập mới cho bé</h4>
                                    <a href="kids.html" class="btn-shop">Mua ngay</a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="sale.html">Khuyến mãi</a>
                        </li>
                        <li class="nav-item">
                            <a href="about.html">Giới thiệu</a>
                        </li>
                    </ul>


                    <!-- Right Navigation -->
                    <div class="nav-right">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="Tìm kiếm...">
                            <button id="search-btn"><i class="fas fa-search"></i></button>
                        </div>
                        <div class="nav-icons">
                            <a href="../account.html" class="nav-icon" title="Tài khoản">
                                <i class="fas fa-user"></i>
                            </a>
                            <a href="../wishlist.html" class="nav-icon" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </a>
                            <a href="../AddCart.html" class="nav-icon cart-icon" title="Giỏ hàng">
                                <i class="fas fa-shopping-bag"></i>
                                <span class="cart-count">0</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Returns Content -->
    <div class="returns-container">
        <div class="returns-header">
            <h1>Chính sách đổi trả</h1>
            <p>Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất cho khách hàng. Nếu bạn không hài lòng với sản phẩm, hãy tham khảo chính sách đổi trả của chúng tôi.</p>
        </div>

        <div class="returns-section">
            <h2>Điều kiện đổi trả</h2>
            <p>Fashion Store chấp nhận đổi trả sản phẩm trong các trường hợp sau:</p>
            <ul>
                <li>Sản phẩm còn nguyên tem, nhãn mác, thẻ bài và trong tình trạng chưa qua sử dụng.</li>
                <li>Sản phẩm không bị hư hỏng, bẩn, có mùi lạ hoặc đã qua giặt ủi.</li>
                <li>Sản phẩm còn đầy đủ bao bì, hộp đựng như ban đầu (nếu có).</li>
                <li>Khách hàng có hóa đơn mua hàng hoặc đơn hàng online.</li>
                <li>Thời gian đổi trả trong vòng 7 ngày kể từ ngày nhận hàng.</li>
            </ul>
            <p>Lưu ý: Một số sản phẩm đặc biệt như đồ lót, đồ bơi, phụ kiện tóc, trang sức và các sản phẩm trong chương trình khuyến mãi đặc biệt có thể không được áp dụng chính sách đổi trả.</p>
        </div>

        <div class="returns-section">
            <h2>Quy trình đổi trả</h2>
            <p>Để đổi trả sản phẩm, bạn có thể thực hiện theo các bước sau:</p>

            <div class="returns-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <div class="step-title">Liên hệ với chúng tôi</div>
                    <div class="step-description">Gọi điện hoặc gửi email thông báo về việc muốn đổi trả sản phẩm trong vòng 7 ngày kể từ khi nhận hàng.</div>
                </div>

                <div class="step-card">
                    <div class="step-number">2</div>
                    <div class="step-title">Chuẩn bị sản phẩm</div>
                    <div class="step-description">Đóng gói sản phẩm cẩn thận với đầy đủ phụ kiện, tem nhãn và hóa đơn mua hàng.</div>
                </div>

                <div class="step-card">
                    <div class="step-number">3</div>
                    <div class="step-title">Gửi trả sản phẩm</div>
                    <div class="step-description">Mang sản phẩm đến cửa hàng gần nhất hoặc gửi qua đơn vị vận chuyển theo hướng dẫn.</div>
                </div>

                <div class="step-card">
                    <div class="step-number">4</div>
                    <div class="step-title">Nhận hoàn tiền/đổi hàng</div>
                    <div class="step-description">Sau khi kiểm tra, chúng tôi sẽ tiến hành hoàn tiền hoặc đổi sản phẩm mới theo yêu cầu của bạn.</div>
                </div>
            </div>
        </div>

        <div class="returns-section">
            <h2>Hình thức hoàn tiền</h2>
            <p>Tùy vào phương thức thanh toán ban đầu, chúng tôi sẽ hoàn tiền theo các hình thức sau:</p>
            <ul>
                <li><strong>Thanh toán bằng thẻ tín dụng/ghi nợ:</strong> Hoàn tiền vào thẻ đã dùng để thanh toán (thời gian hoàn tiền từ 5-15 ngày làm việc tùy theo ngân hàng).</li>
                <li><strong>Thanh toán qua ví điện tử:</strong> Hoàn tiền vào ví điện tử đã dùng để thanh toán (thời gian hoàn tiền từ 1-5 ngày làm việc).</li>
                <li><strong>Thanh toán khi nhận hàng (COD):</strong> Hoàn tiền qua tài khoản ngân hàng do khách hàng cung cấp (thời gian hoàn tiền từ 3-7 ngày làm việc).</li>
            </ul>
            <p>Lưu ý: Phí vận chuyển ban đầu sẽ không được hoàn lại trong trường hợp đổi trả do khách hàng thay đổi ý định mua hàng.</p>
        </div>

        <div class="returns-section">
            <h2>Câu hỏi thường gặp</h2>

            <div class="faq-item">
                <div class="faq-question">Tôi có thể đổi sang sản phẩm khác không?</div>
                <div class="faq-answer">
                    Có, bạn có thể đổi sang sản phẩm khác có giá trị tương đương hoặc cao hơn (bạn sẽ thanh toán phần chênh lệch). Việc đổi sang sản phẩm có giá trị thấp hơn sẽ được hoàn lại phần chênh lệch.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi có phải trả phí vận chuyển khi đổi trả không?</div>
                <div class="faq-answer">
                    Trong trường hợp sản phẩm bị lỗi từ nhà sản xuất, chúng tôi sẽ chịu phí vận chuyển hai chiều. Nếu đổi trả do nhu cầu cá nhân, khách hàng sẽ chịu phí vận chuyển.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi có thể đổi trả sản phẩm đã mua trong chương trình khuyến mãi không?</div>
                <div class="faq-answer">
                    Các sản phẩm mua trong chương trình giảm giá trên 30% thường không được áp dụng chính sách đổi trả. Tuy nhiên, nếu sản phẩm bị lỗi từ nhà sản xuất, chúng tôi vẫn hỗ trợ đổi sản phẩm mới cùng loại.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi không có hóa đơn mua hàng, có thể đổi trả không?</div>
                <div class="faq-answer">
                    Trong trường hợp không có hóa đơn, bạn có thể cung cấp thông tin đơn hàng online hoặc số điện thoại đã dùng để mua hàng để chúng tôi kiểm tra. Tuy nhiên, việc không có hóa đơn có thể kéo dài thời gian xử lý đổi trả.
                </div>
            </div>
        </div>

        <div class="contact-box">
            <h3>Bạn cần hỗ trợ thêm?</h3>
            <p>Nếu bạn có bất kỳ câu hỏi nào về chính sách đổi trả, vui lòng liên hệ với đội ngũ chăm sóc khách hàng của chúng tôi.</p>
            <a href="./contact.html" class="contact-btn">Liên hệ ngay</a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Thông tin</h3>
                    <ul>
                        <li><a href="../about.html">Về chúng tôi</a></li>
                        <li><a href="../contact.html">Liên hệ</a></li>
                        <li><a href="../blog.html">Tin tức & Xu hướng</a></li>
                        <li><a href="../faq.html">Câu hỏi thường gặp</a></li>
                        <li><a href="../stores.html">Hệ thống cửa hàng</a></li>
                        <li><a href="../careers.html">Tuyển dụng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Dịch vụ khách hàng</h3>
                    <ul>
                        <li><a href="../account.html">Tài khoản của tôi</a></li>
                        <li><a href="../orders.html">Theo dõi đơn hàng</a></li>
                        <li><a href="../shipping.html">Chính sách vận chuyển</a></li>
                        <li><a href="../returns.html">Chính sách đổi trả</a></li>
                        <li><a href="../size-guide.html">Hướng dẫn chọn size</a></li>
                        <li><a href="../gift-cards.html">Thẻ quà tặng</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Liên hệ với chúng tôi</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                123 Đường Nguyễn Trãi, Quận 1, TP. Hồ Chí Minh
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-text">
                                +84 (0) 123 456 789
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <EMAIL>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                Thứ Hai - Chủ Nhật: 9:00 - 21:00
                            </div>
                        </div>
                    </div>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <div class="footer-newsletter">
                        <h4>Đăng ký nhận tin</h4>
                        <p>Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
                        <form class="footer-newsletter-form">
                            <input type="email" placeholder="Email của bạn" required>
                            <button type="submit"><i class="fas fa-paper-plane"></i></button>
                        </form>
                    </div>
                    <div class="app-download">
                        <h4>Tải ứng dụng</h4>
                        <div class="app-buttons">
                            <a href="#"><img src="../img logo/apple-store-logo.png" alt="App Store"></a>
                            <a href="#"><img src="../img logo/google-store-logo.png" alt="Google Play"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Fashion Store. Tất cả các quyền được bảo lưu. | Thiết kế bởi <a href="#">MindX JSA08</a></p>
            </div>
        </div>
    </footer>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/lucky-wheel.js"></script>
    <script src="../script/chatbot.js"></script>
    <script src="../script/auth-check.js"></script>
    <script src="../script/search.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ Toggle
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const answer = this.nextElementSibling;
                    answer.classList.toggle('show');
                });
            });

            // Hiển thị tên người dùng đã đăng nhập
            function displayUsername() {
                const user = JSON.parse(localStorage.getItem('user'));
                if (user && user.isLoggedIn) {
                    const userIcon = document.querySelector('.nav-icons .nav-icon[title="Tài khoản"]');
                    if (userIcon) {
                        // Thay đổi icon thành tên người dùng
                        const userName = user.name || user.email.split('@')[0];
                        userIcon.innerHTML = `<span class="user-name">${userName}</span>`;
                        userIcon.title = `Xin chào, ${userName}`;

                        // Thêm sự kiện click để hiển thị menu tài khoản
                        userIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showAccountMenu(this);
                        });
                    }
                }
            }

            // Hiển thị menu tài khoản
            function showAccountMenu(element) {
                // Xóa menu cũ nếu có
                const existingMenu = document.querySelector('.account-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }

                // Tạo menu tài khoản
                const accountMenu = document.createElement('div');
                accountMenu.className = 'account-menu';

                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user'));
                const userName = user.name || user.email.split('@')[0];

                // Thiết lập nội dung menu
                accountMenu.innerHTML = `
                    <div class="account-menu-header">
                        <div class="account-menu-user">
                            <div class="account-menu-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="account-menu-info">
                                <div class="account-menu-name">${userName}</div>
                                <div class="account-menu-email">${user.email}</div>
                            </div>
                        </div>
                    </div>
                    <div class="account-menu-body">
                        <a href="./account.html" class="account-menu-item">
                            <i class="fas fa-user"></i> Tài khoản của tôi
                        </a>
                        <a href="./orders.html" class="account-menu-item">
                            <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                        </a>
                        <a href="./wishlist.html" class="account-menu-item">
                            <i class="fas fa-heart"></i> Danh sách yêu thích
                        </a>
                        <a href="./settings.html" class="account-menu-item">
                            <i class="fas fa-cog"></i> Cài đặt
                        </a>
                        <div class="account-menu-divider"></div>
                        <a href="#" class="account-menu-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Đăng xuất
                        </a>
                    </div>
                `;

                // Định vị menu
                const rect = element.getBoundingClientRect();
                accountMenu.style.top = `${rect.bottom + window.scrollY}px`;
                accountMenu.style.right = `${window.innerWidth - rect.right}px`;

                // Thêm vào body
                document.body.appendChild(accountMenu);

                // Hiển thị với hiệu ứng
                setTimeout(() => {
                    accountMenu.classList.add('show');
                }, 10);

                // Thêm sự kiện cho nút đăng xuất
                const logoutBtn = accountMenu.querySelector('.logout-btn');
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });

                // Đóng menu khi click bên ngoài
                document.addEventListener('click', function closeMenu(e) {
                    if (!accountMenu.contains(e.target) && e.target !== element) {
                        accountMenu.classList.remove('show');
                        setTimeout(() => {
                            accountMenu.remove();
                        }, 300);
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }

            // Hàm đăng xuất
            function logout() {
                // Lấy thông tin người dùng
                const user = JSON.parse(localStorage.getItem('user')) || {};

                // Cập nhật trạng thái đăng nhập
                user.isLoggedIn = false;

                // Lưu vào localStorage
                localStorage.setItem('user', JSON.stringify(user));

                // Hiển thị thông báo
                alert('Đăng xuất thành công');

                // Khôi phục icon người dùng
                const userIcon = document.querySelector('.nav-icons .nav-icon[title="Xin chào, ' + (user.name || user.email.split('@')[0]) + '"]');
                if (userIcon) {
                    userIcon.innerHTML = '<i class="fas fa-user"></i>';
                    userIcon.title = 'Tài khoản';
                }

                // Chuyển hướng về trang chủ sau một khoảng thời gian ngắn
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            }

            // Gọi hàm hiển thị tên người dùng
            displayUsername();
        });
    </script>
</body>
</html>