<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
    integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" type="text/css" href="../styles/style.css">
    <link rel="stylesheet" type="text/css" href="../styles/header.css">
    <link rel="stylesheet" type="text/css" href="../styles/footer.css">
    <link rel="stylesheet" type="text/css" href="../styles/marquee.css">
    <link rel="stylesheet" type="text/css" href="../styles/chatbot.css">
    <link rel="stylesheet" type="text/css" href="../styles/lucky-wheel.css">
    <link rel="stylesheet" type="text/css" href="../styles/auth-check.css">
    <link rel="stylesheet" type="text/css" href="../styles/notification.css">
    <title>Chính sách đổi trả | Fashion Store</title>

    <style>
        /* Returns Policy Styles */
        .returns-container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .returns-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .returns-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: #ff6b6b;
        }

        .returns-header h1 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .returns-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .returns-section {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .returns-section h2 {
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .returns-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .returns-section ul {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        .returns-section li {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .returns-steps {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 30px;
        }

        .step-card {
            flex: 1;
            min-width: 200px;
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.03);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background-color: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 15px;
        }

        .step-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .step-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .faq-item {
            margin-bottom: 20px;
        }

        .faq-question {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Pro';
            color: #ff6b6b;
            transition: transform 0.3s;
        }

        .faq-question.active::after {
            transform: rotate(180deg);
        }

        .faq-answer {
            color: #666;
            line-height: 1.6;
            padding-left: 20px;
            border-left: 2px solid #ff6b6b;
            margin-top: 10px;
            display: none;
        }

        .faq-answer.show {
            display: block;
        }

        .contact-box {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-top: 40px;
        }

        .contact-box h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .contact-box p {
            color: #666;
            margin-bottom: 20px;
        }

        .contact-btn {
            display: inline-block;
            background-color: #ff6b6b;
            color: white;
            padding: 12px 25px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .contact-btn:hover {
            background-color: #ff5252;
        }

        @media (max-width: 768px) {
            .returns-steps {
                flex-direction: column;
            }

            .step-card {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div id="navbar"></div>

    <!-- Marquee Announcement -->
    <div class="marquee-container">
        <div class="marquee-content">
            <span>🔥 Giảm giá lên đến 50% cho tất cả sản phẩm</span>
            <span>🎁 Mua 2 tặng 1 cho bộ sưu tập mới</span>
            <span>✨ Bộ sưu tập mùa hè đã có mặt tại cửa hàng</span>
            <span>🚚 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</span>
        </div>
    </div>

    <!-- Returns Content -->
    <div class="returns-container">
        <div class="returns-header">
            <h1>Chính sách đổi trả</h1>
            <p>Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất cho khách hàng. Nếu bạn không hài lòng với sản phẩm, hãy tham khảo chính sách đổi trả của chúng tôi.</p>
        </div>

        <div class="returns-section">
            <h2>Điều kiện đổi trả</h2>
            <p>Fashion Store chấp nhận đổi trả sản phẩm trong các trường hợp sau:</p>
            <ul>
                <li>Sản phẩm còn nguyên tem, nhãn mác, thẻ bài và trong tình trạng chưa qua sử dụng.</li>
                <li>Sản phẩm không bị hư hỏng, bẩn, có mùi lạ hoặc đã qua giặt ủi.</li>
                <li>Sản phẩm còn đầy đủ bao bì, hộp đựng như ban đầu (nếu có).</li>
                <li>Khách hàng có hóa đơn mua hàng hoặc đơn hàng online.</li>
                <li>Thời gian đổi trả trong vòng 7 ngày kể từ ngày nhận hàng.</li>
            </ul>
            <p>Lưu ý: Một số sản phẩm đặc biệt như đồ lót, đồ bơi, phụ kiện tóc, trang sức và các sản phẩm trong chương trình khuyến mãi đặc biệt có thể không được áp dụng chính sách đổi trả.</p>
        </div>

        <div class="returns-section">
            <h2>Quy trình đổi trả</h2>
            <p>Để đổi trả sản phẩm, bạn có thể thực hiện theo các bước sau:</p>

            <div class="returns-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <div class="step-title">Liên hệ với chúng tôi</div>
                    <div class="step-description">Gọi điện hoặc gửi email thông báo về việc muốn đổi trả sản phẩm trong vòng 7 ngày kể từ khi nhận hàng.</div>
                </div>

                <div class="step-card">
                    <div class="step-number">2</div>
                    <div class="step-title">Chuẩn bị sản phẩm</div>
                    <div class="step-description">Đóng gói sản phẩm cẩn thận với đầy đủ phụ kiện, tem nhãn và hóa đơn mua hàng.</div>
                </div>

                <div class="step-card">
                    <div class="step-number">3</div>
                    <div class="step-title">Gửi trả sản phẩm</div>
                    <div class="step-description">Mang sản phẩm đến cửa hàng gần nhất hoặc gửi qua đơn vị vận chuyển theo hướng dẫn.</div>
                </div>

                <div class="step-card">
                    <div class="step-number">4</div>
                    <div class="step-title">Nhận hoàn tiền/đổi hàng</div>
                    <div class="step-description">Sau khi kiểm tra, chúng tôi sẽ tiến hành hoàn tiền hoặc đổi sản phẩm mới theo yêu cầu của bạn.</div>
                </div>
            </div>
        </div>

        <div class="returns-section">
            <h2>Hình thức hoàn tiền</h2>
            <p>Tùy vào phương thức thanh toán ban đầu, chúng tôi sẽ hoàn tiền theo các hình thức sau:</p>
            <ul>
                <li><strong>Thanh toán bằng thẻ tín dụng/ghi nợ:</strong> Hoàn tiền vào thẻ đã dùng để thanh toán (thời gian hoàn tiền từ 5-15 ngày làm việc tùy theo ngân hàng).</li>
                <li><strong>Thanh toán qua ví điện tử:</strong> Hoàn tiền vào ví điện tử đã dùng để thanh toán (thời gian hoàn tiền từ 1-5 ngày làm việc).</li>
                <li><strong>Thanh toán khi nhận hàng (COD):</strong> Hoàn tiền qua tài khoản ngân hàng do khách hàng cung cấp (thời gian hoàn tiền từ 3-7 ngày làm việc).</li>
            </ul>
            <p>Lưu ý: Phí vận chuyển ban đầu sẽ không được hoàn lại trong trường hợp đổi trả do khách hàng thay đổi ý định mua hàng.</p>
        </div>

        <div class="returns-section">
            <h2>Câu hỏi thường gặp</h2>

            <div class="faq-item">
                <div class="faq-question">Tôi có thể đổi sang sản phẩm khác không?</div>
                <div class="faq-answer">
                    Có, bạn có thể đổi sang sản phẩm khác có giá trị tương đương hoặc cao hơn (bạn sẽ thanh toán phần chênh lệch). Việc đổi sang sản phẩm có giá trị thấp hơn sẽ được hoàn lại phần chênh lệch.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi có phải trả phí vận chuyển khi đổi trả không?</div>
                <div class="faq-answer">
                    Trong trường hợp sản phẩm bị lỗi từ nhà sản xuất, chúng tôi sẽ chịu phí vận chuyển hai chiều. Nếu đổi trả do nhu cầu cá nhân, khách hàng sẽ chịu phí vận chuyển.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi có thể đổi trả sản phẩm đã mua trong chương trình khuyến mãi không?</div>
                <div class="faq-answer">
                    Các sản phẩm mua trong chương trình giảm giá trên 30% thường không được áp dụng chính sách đổi trả. Tuy nhiên, nếu sản phẩm bị lỗi từ nhà sản xuất, chúng tôi vẫn hỗ trợ đổi sản phẩm mới cùng loại.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Tôi không có hóa đơn mua hàng, có thể đổi trả không?</div>
                <div class="faq-answer">
                    Trong trường hợp không có hóa đơn, bạn có thể cung cấp thông tin đơn hàng online hoặc số điện thoại đã dùng để mua hàng để chúng tôi kiểm tra. Tuy nhiên, việc không có hóa đơn có thể kéo dài thời gian xử lý đổi trả.
                </div>
            </div>
        </div>

        <div class="contact-box">
            <h3>Bạn cần hỗ trợ thêm?</h3>
            <p>Nếu bạn có bất kỳ câu hỏi nào về chính sách đổi trả, vui lòng liên hệ với đội ngũ chăm sóc khách hàng của chúng tôi.</p>
            <a href="./contact.html" class="contact-btn">Liên hệ ngay</a>
        </div>
    </div>

    <div id="footerbox"></div>

    <!-- Lucky Wheel Popup -->
    <div class="spin-popup hide-spin">
        <div class="spin-container">
            <div class="close-spin">&times;</div>
            <h2>Quay số may mắn</h2>
            <p>Hãy quay để nhận mã giảm giá!</p>
            <canvas id="wheel" width="300" height="300"></canvas>
            <button id="spin-btn">Quay ngay</button>
            <div id="spin-result"></div>
        </div>
    </div>

    <!-- Lucky Wheel Trigger Button -->
    <div class="wheel-trigger">
        <i class="fas fa-gift"></i>
    </div>

    <!-- Chatbot -->
    <div id="chat-bot">
        <div class="chat-header">
            <span>Trợ lý ảo Fashion Store</span>
            <span id="chat-close">&times;</span>
        </div>
        <div class="chat-body">
            <!-- Chat messages will be added here -->
        </div>
        <input type="text" id="chat-input" placeholder="Nhập câu hỏi của bạn...">
    </div>

    <!-- Chatbot Trigger Button -->
    <div id="chat-toggle">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../script/main.js"></script>
    <script src="../script/notification.js"></script>
    <script src="../script/voice-search.js"></script>
    <script src="../script/dark-mode.js"></script>
    <script src="../script/user-display.js"></script>

    <script type="module">
        import navbar from "../components/navbar.js"
        import footer from "../components/footer.js"

        let navbarbox = document.getElementById("navbar");
        navbarbox.innerHTML = navbar();

        let footerbox = document.getElementById("footerbox");
        footerbox.innerHTML = footer();

        // FAQ Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const answer = this.nextElementSibling;
                    answer.classList.toggle('show');
                });
            });
        });
    </script>
</body>
</html>